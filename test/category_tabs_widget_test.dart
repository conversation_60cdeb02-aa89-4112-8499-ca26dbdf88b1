import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock callback
class MockOnCategorySelected extends Mock {
  void call(String category);
}

void main() {
  late MockOnCategorySelected mockOnCategorySelected;

  setUp(() {
    mockOnCategorySelected = MockOnCategorySelected();
  });

  Future<void> pumpCategoryTabsWidget(WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: DefaultTabController(
            length: 5, // Số lượng tab
            child: CategoryTabsWidget(
              onCategorySelected: mockOnCategorySelected,
            ),
          ),
        ),
      ),
    );
    await tester.pump(Duration(seconds: 1));
    await tester.pumpAndSettle(Duration(seconds: 1));
  }

  group('CategoryTabsWidget UI Tests', () {
    testWidgets('category tabs are rendered', (WidgetTester tester) async {
      await pumpCategoryTabsWidget(tester);
      expect(find.byType(CategoryTabsWidget), findsOneWidget, reason: 'CategoryTabsWidget should be rendered');
      expect(find.byType(TabBar), findsOneWidget, reason: 'TabBar should be rendered');
      expect(find.byType(Tab), findsWidgets, reason: 'Tabs should be rendered');
    });

    testWidgets('tapping on a category tab calls onCategorySelected', (WidgetTester tester) async {
      await pumpCategoryTabsWidget(tester);
      final tabFinder = find.byType(Tab);
      expect(tabFinder, findsWidgets, reason: 'Tabs should be visible');
      final categoryTabFinder = tabFinder.first;
      await tester.tap(categoryTabFinder);
      await tester.pumpAndSettle();
      verify(() => mockOnCategorySelected.call(any())).called(1);
    });
  });
}

class Category {
  final String id;
  final String name;

  Category({required this.id, required this.name});
}

class CategoryTabsWidget extends StatelessWidget {
  final Function(String) onCategorySelected;

  const CategoryTabsWidget({
    Key? key,
    required this.onCategorySelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final categories = [
      Category(id: 'all', name: 'All Products'),
      Category(id: 'drinks', name: 'Drinks'),
      Category(id: 'food', name: 'Food'),
      Category(id: 'snacks', name: 'Snacks'),
      Category(id: 'other', name: 'Other'),
    ];

    return TabBar(
      isScrollable: true,
      tabs: categories.map((cat) => Tab(text: cat.name)).toList(),
      onTap: (index) {
        onCategorySelected(categories[index].id);
      },
      indicatorColor: Theme.of(context).primaryColor,
    );
  }
}
