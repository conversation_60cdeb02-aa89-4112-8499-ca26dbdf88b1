import 'package:flutter/material.dart';

class AppLocalizations {
  static AppLocalizations of(BuildContext context) => AppLocalizations();
  
  // Common strings
  String get appName => 'Test App';
  String get ok => 'OK';
  String get cancel => 'Cancel';
  String get error => 'Error';
  String get success => 'Success';
  String get warning => 'Warning';
  String get information => 'Information';
  String get confirm => 'Confirm';
  String get close => 'Close';
  String get save => 'Save';
  String get delete => 'Delete';
  String get edit => 'Edit';
  String get add => 'Add';
  String get search => 'Search';
  String get noData => 'No data available';
  String get loading => 'Loading...';
  String get pleaseWait => 'Please wait...';
  String get retry => 'Retry';
  String get noInternet => 'No internet connection';
  String get somethingWentWrong => 'Something went wrong';
  
  // Validation messages
  String get fieldIsRequired => 'This field is required';
  String get invalidEmail => 'Invalid email';
  String get invalidPhoneNumber => 'Invalid phone number';
  String get invalidPostalCode => 'Invalid postal code';
  String get invalidUrl => 'Invalid URL';
  String get invalidDate => 'Invalid date';
  String get invalidTime => 'Invalid time';
  String get invalidNumber => 'Invalid number';
  String get invalidInteger => 'Invalid integer';
  String get invalidDouble => 'Invalid decimal number';
  String get invalidBoolean => 'Invalid boolean';
  String get invalidList => 'Invalid list';
  String get invalidMap => 'Invalid map';
  String get invalidFile => 'Invalid file';
  String get invalidImage => 'Invalid image';
  String get invalidVideo => 'Invalid video';
  String get invalidAudio => 'Invalid audio';
  String get invalidDocument => 'Invalid document';
  String get invalidArchive => 'Invalid archive';
  String get invalidSize => 'Invalid size';
  String get invalidFormat => 'Invalid format';
  String get invalidColor => 'Invalid color';
  
  // Authentication
  String get login => 'Login';
  String get register => 'Register';
  String get email => 'Email';
  String get password => 'Password';
  String get confirmPassword => 'Confirm password';
  String get forgotPassword => 'Forgot password?';
  String get rememberMe => 'Remember me';
  String get or => 'OR';
  String get continueWithGoogle => 'Continue with Google';
  String get continueWithFacebook => 'Continue with Facebook';
  String get continueWithApple => 'Continue with Apple';
  String get dontHaveAnAccount => 'Don\'t have an account?';
  String get alreadyHaveAnAccount => 'Already have an account?';
  String get signUp => 'Sign up';
  String get signIn => 'Sign in';
  String get firstName => 'First name';
  String get lastName => 'Last name';
  String get phoneNumber => 'Phone number';
  String get address => 'Address';
  String get city => 'City';
  String get country => 'Country';
  String get postalCode => 'Postal code';
  String get saveChanges => 'Save changes';
  String get changePassword => 'Change password';
  String get currentPassword => 'Current password';
  String get newPassword => 'New password';
  String get confirmNewPassword => 'Confirm new password';
  String get passwordMustBeAtLeast8Characters => 'Password must be at least 8 characters';
  String get passwordsDoNotMatch => 'Passwords do not match';
  
  // Navigation
  String get home => 'Home';
  String get settings => 'Settings';
  String get profile => 'Profile';
  String get logout => 'Logout';
  String get goBack => 'Go back';
  
  // Errors
  String get unauthorized => 'Unauthorized';
  String get forbidden => 'Forbidden';
  String get notFound => 'Not found';
  String get serverError => 'Server error';
  String get timeout => 'Request timeout';
  String get unknownError => 'Unknown error';
  String get noRouteFound => 'No route found';
}
