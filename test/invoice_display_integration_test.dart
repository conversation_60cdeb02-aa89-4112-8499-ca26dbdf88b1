import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';

import 'package:city_pos/core/providers/invoice_settings_provider.dart';
import 'package:city_pos/core/routers/app_router.dart';
import 'package:city_pos/data/models/order.dart';
import 'package:city_pos/data/models/order_item.dart';
import 'package:city_pos/features/sales/presentation/invoice_display_screen.dart';

void main() {
  group('Invoice Display Integration Tests', () {
    late Order testOrder;

    setUp(() {
      testOrder = Order(
        id: 'test-order-1',
        orderNumber: 'ORD-001',
        totalAmount: 100000,
        subtotal: 90000,
        taxAmount: 10000,
        discountAmount: 0,
        status: 'completed',
        paymentMethod: 'cash',
        items: [
          OrderItem(
            id: 'item-1',
            productName: 'Test Product',
            quantity: 2,
            unitPrice: 45000,
            totalAmount: 90000,
            totalPrice: 90000,
          ),
        ],
        createdAt: DateTime.now(),
        notes: 'Test Customer\n**********',
      );
    });

    testWidgets('should show invoice display screen when setting is enabled', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            invoiceSettingsProvider.overrideWith(
              (ref) => InvoiceSettingsNotifier()..setShowInvoiceAfterPayment(true),
            ),
          ],
          child: MaterialApp.router(
            routerConfig: GoRouter(
              initialLocation: AppRoutes.invoiceDisplay,
              routes: [
                GoRoute(
                  path: AppRoutes.invoiceDisplay,
                  builder: (context, state) => InvoiceDisplayScreen(order: testOrder),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify invoice display screen is shown
      expect(find.text('Hóa đơn bán hàng'), findsOneWidget);
      expect(find.text('ORD-001'), findsOneWidget);
      expect(find.text('Test Product'), findsOneWidget);
      expect(find.text('Chia sẻ'), findsOneWidget);
    });

    testWidgets('should display correct order information', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: InvoiceDisplayScreen(order: testOrder),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify order details
      expect(find.text('ORD-001'), findsOneWidget);
      expect(find.text('Test Product'), findsOneWidget);
      expect(find.text('2'), findsOneWidget); // quantity
      expect(find.text('45.000₫'), findsOneWidget); // unit price
      expect(find.text('90.000₫'), findsOneWidget); // total amount
    });

    testWidgets('should display customer information from notes', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: InvoiceDisplayScreen(order: testOrder),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify customer info extracted from notes
      expect(find.text('Test Customer'), findsOneWidget);
      expect(find.text('**********'), findsOneWidget);
    });

    test('invoice settings provider should default to true', () {
      final notifier = InvoiceSettingsNotifier();
      expect(notifier.state.showInvoiceAfterPayment, isTrue);
    });

    test('invoice settings provider should toggle correctly', () async {
      final notifier = InvoiceSettingsNotifier();
      
      // Initial state should be true
      expect(notifier.state.showInvoiceAfterPayment, isTrue);
      
      // Toggle to false
      await notifier.setShowInvoiceAfterPayment(false);
      expect(notifier.state.showInvoiceAfterPayment, isFalse);
      
      // Toggle back to true
      await notifier.setShowInvoiceAfterPayment(true);
      expect(notifier.state.showInvoiceAfterPayment, isTrue);
    });
  });
}
