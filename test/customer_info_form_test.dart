import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock callback
class MockOnSave extends Mock {
  void call(String name, String phone, String address);
}

void main() {
  late MockOnSave mockOnSave;

  setUp(() {
    mockOnSave = MockOnSave();
  });

  Future<void> pumpCustomerInfoForm(WidgetTester tester, {String initialName = '', String initialPhone = '', String initialAddress = ''}) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: CustomerInfoForm(
            initialName: initialName,
            initialPhone: initialPhone,
            initialAddress: initialAddress,
            onSave: mockOnSave.call,
          ),
        ),
      ),
    );
    await tester.pump();
  }

  group('CustomerInfoForm UI Tests', () {
    testWidgets('renders customer info form with input fields', (WidgetTester tester) async {
      await pumpCustomerInfoForm(tester);

      expect(find.byType(TextFormField), findsNWidgets(3));
      expect(find.text('Name'), findsOneWidget);
      expect(find.text('Phone'), findsOneWidget);
      expect(find.text('Address'), findsOneWidget);
      expect(find.text('Save'), findsOneWidget);
    });

    testWidgets('renders customer info form with initial values', (WidgetTester tester) async {
      await pumpCustomerInfoForm(tester, initialName: 'John Doe', initialPhone: '1234567890', initialAddress: '123 Main St');

      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('1234567890'), findsOneWidget);
      expect(find.text('123 Main St'), findsOneWidget);
    });

    testWidgets('entering data and tapping save calls onSave with correct values', (WidgetTester tester) async {
      await pumpCustomerInfoForm(tester);

      // Kiểm tra xem các trường có hiển thị không trước khi nhập dữ liệu
      expect(find.byType(TextFormField), findsNWidgets(3));
      
      // Nhập dữ liệu vào các trường
      await tester.enterText(find.byKey(Key('name_field')), 'John Doe');
      await tester.enterText(find.byKey(Key('phone_field')), '1234567890');
      await tester.enterText(find.byKey(Key('address_field')), '123 Main St');
      await tester.pump();

      // Nhấn nút lưu
      await tester.tap(find.byKey(Key('save_button')));
      await tester.pump();

      // Xác minh rằng onSave được gọi với các giá trị đúng
      verify(() => mockOnSave.call('John Doe', '1234567890', '123 Main St')).called(1);
    });
  });
}

class CustomerInfoForm extends StatelessWidget {
  final String initialName;
  final String initialPhone;
  final String initialAddress;
  final Function(String, String, String) onSave;

  const CustomerInfoForm({
    Key? key,
    required this.initialName,
    required this.initialPhone,
    required this.initialAddress,
    required this.onSave,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final nameController = TextEditingController(text: initialName);
    final phoneController = TextEditingController(text: initialPhone);
    final addressController = TextEditingController(text: initialAddress);

    return Column(
      children: [
        TextFormField(
          key: Key('name_field'),
          controller: nameController,
          decoration: InputDecoration(labelText: 'Name'),
        ),
        TextFormField(
          key: Key('phone_field'),
          controller: phoneController,
          decoration: InputDecoration(labelText: 'Phone'),
          keyboardType: TextInputType.phone,
        ),
        TextFormField(
          key: Key('address_field'),
          controller: addressController,
          decoration: InputDecoration(labelText: 'Address'),
        ),
        ElevatedButton(
          key: Key('save_button'),
          onPressed: () => onSave(nameController.text, phoneController.text, addressController.text),
          child: Text('Save'),
        ),
      ],
    );
  }
}
