import 'package:city_pos/data/models/printer.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';

void main() {
  group('Printer Model Tests', () {
    setUpAll(() async {
      // Register adapters for testing
      if (!Hive.isAdapterRegistered(8)) {
        Hive.registerAdapter(PrinterAdapter());
      }
      if (!Hive.isAdapterRegistered(9)) {
        Hive.registerAdapter(PrinterTypeAdapter());
      }
    });

    setUp(() async {
      // Initialize PrinterService before each test
      await PrinterService.init();
    });

    tearDown(() async {
      // Clear all printers after each test
      final printers = PrinterService.getAllPrinters();
      for (final printer in printers) {
        await PrinterService.deletePrinter(printer.id);
      }
    });

    test('should initialize successfully', () async {
      await PrinterService.init();
      expect(PrinterService.getAllPrinters(), isEmpty);
    });

    test('should add printer successfully', () async {
      final printer = Printer(
        id: 'test-1',
        name: 'Test Printer',
        ipAddress: '*************',
        port: 9100,
        createdAt: DateTime.now(),
      );

      final result = await PrinterService.addPrinter(printer);
      expect(result, isTrue);

      final printers = PrinterService.getAllPrinters();
      expect(printers, hasLength(1));
      expect(printers.first.name, equals('Test Printer'));
    });

    test('should update printer successfully', () async {
      final printer = Printer(
        id: 'test-1',
        name: 'Test Printer',
        ipAddress: '*************',
        port: 9100,
        createdAt: DateTime.now(),
      );

      await PrinterService.addPrinter(printer);

      final updatedPrinter = printer.copyWith(name: 'Updated Printer');
      final result = await PrinterService.updatePrinter(updatedPrinter);
      expect(result, isTrue);

      final printers = PrinterService.getAllPrinters();
      expect(printers.first.name, equals('Updated Printer'));
    });

    test('should delete printer successfully', () async {
      final printer = Printer(
        id: 'test-1',
        name: 'Test Printer',
        ipAddress: '*************',
        port: 9100,
        createdAt: DateTime.now(),
      );

      await PrinterService.addPrinter(printer);
      expect(PrinterService.getAllPrinters(), hasLength(1));

      final result = await PrinterService.deletePrinter(printer.id);
      expect(result, isTrue);
      expect(PrinterService.getAllPrinters(), isEmpty);
    });

    test('should set default printer successfully', () async {
      final printer1 = Printer(
        id: 'test-1',
        name: 'Printer 1',
        ipAddress: '*************',
        port: 9100,
        createdAt: DateTime.now(),
      );

      final printer2 = Printer(
        id: 'test-2',
        name: 'Printer 2',
        ipAddress: '*************',
        port: 9100,
        createdAt: DateTime.now(),
      );

      await PrinterService.addPrinter(printer1);
      await PrinterService.addPrinter(printer2);

      final result = await PrinterService.setDefaultPrinter('test-2');
      expect(result, isTrue);

      final defaultPrinter = PrinterService.getDefaultPrinter();
      expect(defaultPrinter?.id, equals('test-2'));
    });

    test('should get default printer correctly', () async {
      final printer = Printer(
        id: 'test-1',
        name: 'Test Printer',
        ipAddress: '*************',
        port: 9100,
        isDefault: true,
        createdAt: DateTime.now(),
      );

      await PrinterService.addPrinter(printer);

      final defaultPrinter = PrinterService.getDefaultPrinter();
      expect(defaultPrinter?.id, equals('test-1'));
    });

    test('should return null when no default printer exists', () {
      final defaultPrinter = PrinterService.getDefaultPrinter();
      expect(defaultPrinter, isNull);
    });

    test('should handle multiple printers correctly', () async {
      final printers = [
        Printer(
          id: 'test-1',
          name: 'Printer 1',
          ipAddress: '*************',
          port: 9100,
          createdAt: DateTime.now(),
        ),
        Printer(
          id: 'test-2',
          name: 'Printer 2',
          ipAddress: '*************',
          port: 9100,
          createdAt: DateTime.now(),
        ),
        Printer(
          id: 'test-3',
          name: 'Printer 3',
          ipAddress: '*************',
          port: 9100,
          createdAt: DateTime.now(),
        ),
      ];

      for (final printer in printers) {
        await PrinterService.addPrinter(printer);
      }

      final allPrinters = PrinterService.getAllPrinters();
      expect(allPrinters, hasLength(3));
    });

    test('printer model should have correct properties', () {
      final printer = Printer(
        id: 'test-1',
        name: 'Test Printer',
        ipAddress: '*************',
        port: 9100,
        isDefault: true,
        isActive: false,
        createdAt: DateTime.now(),
        type: PrinterType.network,
      );

      expect(printer.displayAddress, equals('*************:9100'));
      expect(printer.isConnectable, isTrue);
      expect(printer.statusText, equals('Máy in mặc định'));
    });

    test('printer model should validate connectivity', () {
      final validPrinter = Printer(
        id: 'test-1',
        name: 'Valid Printer',
        ipAddress: '*************',
        port: 9100,
        createdAt: DateTime.now(),
      );

      final invalidPrinter = Printer(
        id: 'test-2',
        name: 'Invalid Printer',
        ipAddress: '',
        port: 0,
        createdAt: DateTime.now(),
      );

      expect(validPrinter.isConnectable, isTrue);
      expect(invalidPrinter.isConnectable, isFalse);
    });

    test('printer type extension should return correct display names', () {
      expect(PrinterType.network.displayName, equals('Mạng LAN/WiFi'));
      expect(PrinterType.bluetooth.displayName, equals('Bluetooth'));
      expect(PrinterType.usb.displayName, equals('USB'));
    });
  });
}
