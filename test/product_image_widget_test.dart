import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:city_pos/core/widgets/product_image_widget.dart';

void main() {
  group('ProductImageWidget Tests', () {
    testWidgets('should display placeholder when no image URL provided', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImageWidget(
              productId: 'test-product-id',
              isEditable: false,
            ),
          ),
        ),
      );

      // Should display placeholder icon
      expect(find.byIcon(Icons.image), findsOneWidget);
      
      // Should not display edit button when not editable
      expect(find.byIcon(Icons.edit), findsNothing);
    });

    testWidgets('should display edit button when editable', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImageWidget(
              productId: 'test-product-id',
              isEditable: true,
            ),
          ),
        ),
      );

      // Should display "Thêm ảnh sản phẩm" text
      expect(find.text('Thêm ảnh sản phẩm'), findsOneWidget);
      
      // Should display add photo icon
      expect(find.byIcon(Icons.add_photo_alternate), findsOneWidget);
    });

    testWidgets('should display image when URL is provided', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImageWidget(
              productId: 'test-product-id',
              initialImageUrl: 'https://example.com/image.jpg',
              isEditable: false,
            ),
          ),
        ),
      );

      // Should display network image
      expect(find.byType(Image), findsOneWidget);
    });

    testWidgets('should display edit button overlay when editable and has image', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImageWidget(
              productId: 'test-product-id',
              initialImageUrl: 'https://example.com/image.jpg',
              isEditable: true,
            ),
          ),
        ),
      );

      // Should display edit button overlay
      expect(find.byIcon(Icons.edit), findsOneWidget);
    });

    testWidgets('should call onImageChanged when image changes', (WidgetTester tester) async {
      String? changedImageUrl;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImageWidget(
              productId: 'test-product-id',
              isEditable: true,
              onImageChanged: (url) {
                changedImageUrl = url;
              },
            ),
          ),
        ),
      );

      // This test would require mocking image picker
      // For now, we just verify the callback is set up correctly
      expect(changedImageUrl, isNull);
    });

    testWidgets('should call onError when error occurs', (WidgetTester tester) async {
      String? errorMessage;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImageWidget(
              productId: 'test-product-id',
              isEditable: true,
              onError: (error) {
                errorMessage = error;
              },
            ),
          ),
        ),
      );

      // This test would require mocking error scenarios
      // For now, we just verify the callback is set up correctly
      expect(errorMessage, isNull);
    });

    testWidgets('should respect custom dimensions', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImageWidget(
              productId: 'test-product-id',
              width: 300,
              height: 250,
              isEditable: false,
            ),
          ),
        ),
      );

      // Find the container with custom dimensions
      final container = tester.widget<Container>(
        find.byType(Container).first,
      );
      
      expect(container.constraints?.maxWidth, 300);
      expect(container.constraints?.maxHeight, 250);
    });

    testWidgets('should apply custom border radius', (WidgetTester tester) async {
      const customRadius = BorderRadius.all(Radius.circular(16));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImageWidget(
              productId: 'test-product-id',
              borderRadius: customRadius,
              isEditable: false,
            ),
          ),
        ),
      );

      // Find the container with custom border radius
      final container = tester.widget<Container>(
        find.byType(Container).first,
      );
      
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.borderRadius, customRadius);
    });
  });
}
