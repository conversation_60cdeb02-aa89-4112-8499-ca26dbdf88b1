import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock data class
class Product {
  final String name;
  final String description;
  final double price;

  Product({required this.name, required this.description, required this.price});
}

// Mock callbacks
class MockOnAddToCart extends Mock {
  void call();
}

class MockOnClose extends Mock {
  void call();
}

void main() {
  late MockOnAddToCart mockOnAddToCart;
  late MockOnClose mockOnClose;

  setUp(() {
    mockOnAddToCart = MockOnAddToCart();
    mockOnClose = MockOnClose();
  });

  Future<void> pumpProductDetailDialog(WidgetTester tester, {Product? product}) async {
    final testProduct = product ?? Product(name: 'Test Product', description: '', price: 10.0);
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) => ElevatedButton(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => ProductDetailDialog(
                    product: testProduct,
                    onAddToCart: mockOnAddToCart.call,
                    onClose: mockOnClose.call,
                  ),
                );
              },
              child: Text('Show Dialog'),
            ),
          ),
        ),
      ),
    );
    await tester.pump();
    await tester.tap(find.text('Show Dialog'));
    await tester.pump();
  }

  group('ProductDetailDialog UI Tests', () {
    testWidgets('displays product details correctly', (WidgetTester tester) async {
      await pumpProductDetailDialog(tester);

      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('Product Details'), findsOneWidget);
      expect(find.text('Test Product'), findsOneWidget);
      expect(find.text(''), findsOneWidget);
      expect(find.text('Price: 10.0'), findsOneWidget);
      expect(find.text('Add to Cart'), findsOneWidget);
      expect(find.text('Close'), findsOneWidget);
    });

    testWidgets('tapping add to cart calls onAddToCart', (WidgetTester tester) async {
      await pumpProductDetailDialog(tester);

      await tester.tap(find.text('Add to Cart'));
      await tester.pump();

      verify(() => mockOnAddToCart.call()).called(1);
    });

    testWidgets('tapping close calls onClose', (WidgetTester tester) async {
      await pumpProductDetailDialog(tester);

      await tester.tap(find.text('Close'));
      await tester.pump();

      verify(() => mockOnClose.call()).called(1);
    });
  });
}

class ProductDetailDialog extends StatelessWidget {
  final Product product;
  final Function() onAddToCart;
  final Function() onClose;

  const ProductDetailDialog({
    Key? key,
    required this.product,
    required this.onAddToCart,
    required this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Product Details'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(product.name),
          Text(product.description),
          Text('Price: ${product.price.toStringAsFixed(1)}'),
        ],
      ),
      actions: [
        TextButton(
          onPressed: onClose,
          child: Text('Close'),
        ),
        TextButton(
          onPressed: onAddToCart,
          child: Text('Add to Cart'),
        ),
      ],
    );
  }
}
