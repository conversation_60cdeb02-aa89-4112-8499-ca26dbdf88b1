import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock callback
class MockOnMethodSelected extends Mock {
  void call(String method);
}

void main() {
  late MockOnMethodSelected mockOnMethodSelected;

  setUp(() {
    mockOnMethodSelected = MockOnMethodSelected();
  });

  Future<void> pumpPaymentMethodSelector(WidgetTester tester, {String selectedMethod = 'cash'}) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: PaymentMethodSelector(
            selectedMethod: selectedMethod,
            onMethodSelected: mockOnMethodSelected.call,
          ),
        ),
      ),
    );
    await tester.pump();
  }

  group('PaymentMethodSelector UI Tests', () {
    testWidgets('renders payment method selector with selected method', (WidgetTester tester) async {
      await pumpPaymentMethodSelector(tester, selectedMethod: 'credit_card');

      expect(find.byType(RadioListTile<String>), findsNWidgets(3));
      expect(find.text('Cash'), findsOneWidget);
      expect(find.text('Credit Card'), findsOneWidget);
      expect(find.text('Digital Wallet'), findsOneWidget);
    });

    testWidgets('selecting a different method calls onMethodSelected', (WidgetTester tester) async {
      await pumpPaymentMethodSelector(tester, selectedMethod: 'cash');

      await tester.tap(find.text('Credit Card'));
      await tester.pump();

      verify(() => mockOnMethodSelected.call('credit_card')).called(1);
    });

    testWidgets('displays payment methods correctly', (WidgetTester tester) async {
      await pumpPaymentMethodSelector(tester);

      expect(find.text('Cash'), findsOneWidget);
      expect(find.text('Credit Card'), findsOneWidget);
      expect(find.text('Digital Wallet'), findsOneWidget);
    });

    testWidgets('tapping a payment method calls onChanged with correct value', (WidgetTester tester) async {
      await pumpPaymentMethodSelector(tester);

      await tester.tap(find.text('Credit Card'));
      await tester.pump();

      verify(() => mockOnMethodSelected.call('credit_card')).called(1);
    });
  });
}

class PaymentMethodSelector extends StatelessWidget {
  final String selectedMethod;
  final Function(String) onMethodSelected;

  const PaymentMethodSelector({
    Key? key,
    required this.selectedMethod,
    required this.onMethodSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        RadioListTile<String>(
          title: Text('Cash'),
          value: 'cash',
          groupValue: selectedMethod,
          onChanged: (value) {
            if (value != null) {
              onMethodSelected(value);
            }
          },
        ),
        RadioListTile<String>(
          title: Text('Credit Card'),
          value: 'credit_card',
          groupValue: selectedMethod,
          onChanged: (value) {
            if (value != null) {
              onMethodSelected(value);
            }
          },
        ),
        RadioListTile<String>(
          title: Text('Digital Wallet'),
          value: 'digital_wallet',
          groupValue: selectedMethod,
          onChanged: (value) {
            if (value != null) {
              onMethodSelected(value);
            }
          },
        ),
      ],
    );
  }
}
