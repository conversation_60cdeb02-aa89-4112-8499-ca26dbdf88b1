import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:city_pos/generated/l10n/app_localizations.dart';

// Mock callbacks
class MockOnPaymentConfirmed extends Mock {
  void call(double amount);
}

class MockOnCancel extends Mock {
  void call();
}

void main() {
  late MockOnPaymentConfirmed mockOnPaymentConfirmed;
  late MockOnCancel mockOnCancel;

  setUp(() {
    mockOnPaymentConfirmed = MockOnPaymentConfirmed();
    mockOnCancel = MockOnCancel();
  });

  Future<void> pumpCheckoutDialog(WidgetTester tester, {double totalAmount = 100.0}) async {
    await tester.pumpWidget(
      MaterialApp(
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: AppLocalizations.supportedLocales,
        home: Builder(
          builder: (context) => Scaffold(
            body: ElevatedButton(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => CheckoutDialog(
                    totalAmount: totalAmount,
                    onPaymentConfirmed: mockOnPaymentConfirmed.call,
                    onCancel: mockOnCancel.call,
                  ),
                );
              },
              child: Text('Show Dialog'),
            ),
          ),
        ),
      ),
    );
    await tester.pump();
    await tester.tap(find.text('Show Dialog'));
    await tester.pump();
  }

  group('CheckoutDialog UI Tests', () {
    testWidgets('displays checkout dialog with correct total amount', (WidgetTester tester) async {
      await pumpCheckoutDialog(tester, totalAmount: 100.0);

      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('Checkout'), findsOneWidget);
      expect(find.text('Total Amount: 100.00'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Confirm Payment'), findsOneWidget);
    });

    testWidgets('tapping confirm calls onPaymentConfirmed with correct amount', (WidgetTester tester) async {
      await pumpCheckoutDialog(tester, totalAmount: 200.0);

      await tester.tap(find.text('Confirm Payment'));
      await tester.pump();

      verify(() => mockOnPaymentConfirmed.call(200.0)).called(1);
    });

    testWidgets('tapping cancel calls onCancel', (WidgetTester tester) async {
      await pumpCheckoutDialog(tester);

      await tester.tap(find.text('Cancel'));
      await tester.pump();

      verify(() => mockOnCancel.call()).called(1);
    });
  });
}

class CheckoutDialog extends StatelessWidget {
  final double totalAmount;
  final Function(double) onPaymentConfirmed;
  final Function() onCancel;

  const CheckoutDialog({
    Key? key,
    required this.totalAmount,
    required this.onPaymentConfirmed,
    required this.onCancel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Checkout'),
      content: Text('Total Amount: ${totalAmount.toStringAsFixed(2)}'),
      actions: [
        TextButton(
          onPressed: onCancel,
          child: Text('Cancel'),
        ),
        TextButton(
          onPressed: () => onPaymentConfirmed(totalAmount),
          child: Text('Confirm Payment'),
        ),
      ],
    );
  }
}
