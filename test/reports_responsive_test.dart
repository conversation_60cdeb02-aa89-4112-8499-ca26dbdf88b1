import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mocktail/mocktail.dart';
import '../lib/features/reports/presentation/reports_screen_simple.dart';
import '../lib/features/reports/domain/entities/report_data.dart';

// Mock for ReportsSimpleNotifier
class MockReportsSimpleNotifier extends Mock implements ReportsSimpleNotifier {}

void main() {
  late ReportsSimpleState testState;
  late MockReportsSimpleNotifier mockNotifier;

  // Common test data setup
  setUp(() {
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));
    
    testState = ReportsSimpleState(
      isLoading: false,
      salesReport: SalesReportData(
        date: now,
        totalRevenue: 1000000,
        totalCost: 600000,
        grossProfit: 400000,
        totalOrders: 25,
        totalItems: 100,
        averageOrderValue: 40000,
        revenueByCategory: {},
        ordersByPaymentMethod: {},
        topProducts: [],
      ),
      inventoryReport: InventoryReportData(
        date: now,
        totalProducts: 50,
        totalStockValue: 2000000,
        lowStockProducts: 5,
        outOfStockProducts: 2,
        productStocks: [],
        stockValueByCategory: {},
      ),
      financeReport: FinanceReportData(
        date: now,
        totalReceipts: 1000000,
        totalPayments: 600000,
        netCashFlow: 400000,
        openingBalance: 500000,
        closingBalance: 900000,
        receiptsByCategory: {},
        paymentsByCategory: {},
      ),
      salesTrend: [],
      topProducts: [],
      lowStockProducts: [],
      startDate: thirtyDaysAgo,
      endDate: now,
    );

    mockNotifier = MockReportsSimpleNotifier();
    when(() => mockNotifier.state).thenReturn(testState);
  });

  // Clean up after tests
  tearDown(() {
    reset(mockNotifier);
  });

  // Helper function to build the test widget
  Future<void> pumpReportsScreen(
    WidgetTester tester, {
    required Size screenSize,
  }) async {
    await tester.binding.setSurfaceSize(screenSize);
    
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          reportsSimpleProvider.overrideWith((ref) => mockNotifier),
        ],
        child: const MaterialApp(
          home: Scaffold(
            body: ReportsScreenSimple(),
          ),
        ),
      ),
    );
    
    await tester.pumpAndSettle();
  }

  group('ReportsScreenSimple Responsive Tests', () {
    testWidgets('should display 4 stat cards in 2x2 grid on mobile', (WidgetTester tester) async {
      // Arrange
      await pumpReportsScreen(
        tester,
        screenSize: const Size(400, 800), // Mobile size
      );

      // Assert - Verify all stat cards are present with correct values
      final statCards = find.byType(Card);
      expect(statCards, findsNWidgets(4));
      
      // Verify card titles
      expect(find.text('Doanh thu'), findsOneWidget);
      expect(find.text('Lợi nhuận'), findsOneWidget);
      expect(find.text('Đơn hàng'), findsOneWidget);
      expect(find.text('Tồn kho'), findsOneWidget);
      
      // Verify values are displayed correctly
      expect(find.text('1.000.000₫'), findsWidgets); // Revenue and other amounts
      expect(find.text('400.000₫'), findsOneWidget); // Profit
      expect(find.text('25'), findsOneWidget); // Orders
      expect(find.text('2.000.000₫'), findsOneWidget); // Stock value
      
      // Verify layout - 2x2 grid on mobile
      final grid = find.byType(GridView);
      expect(grid, findsOneWidget);
      final gridView = tester.widget<GridView>(grid);
      expect(gridView.gridDelegate, isA<SliverGridDelegateWithFixedCrossAxisCount>());
      expect(
        (gridView.gridDelegate as SliverGridDelegateWithFixedCrossAxisCount).crossAxisCount,
        2, // 2 columns on mobile
      );
    });

    testWidgets('should display stat cards in single row on desktop', (WidgetTester tester) async {
      // Arrange
      await pumpReportsScreen(
        tester,
        screenSize: const Size(1200, 800), // Desktop size
      );

      // Assert - Verify all stat cards are present
      final statCards = find.byType(Card);
      expect(statCards, findsNWidgets(4));
      
      // Verify card titles
      expect(find.text('Doanh thu'), findsOneWidget);
      expect(find.text('Lợi nhuận'), findsOneWidget);
      expect(find.text('Đơn hàng'), findsOneWidget);
      expect(find.text('Tồn kho'), findsOneWidget);
      
      // Verify layout - 4 columns on desktop
      final grid = find.byType(GridView);
      expect(grid, findsOneWidget);
      final gridView = tester.widget<GridView>(grid);
      expect(gridView.gridDelegate, isA<SliverGridDelegateWithFixedCrossAxisCount>());
      expect(
        (gridView.gridDelegate as SliverGridDelegateWithFixedCrossAxisCount).crossAxisCount,
        4, // 4 columns on desktop
      );
    });

    testWidgets('should show loading indicator when data is loading', (WidgetTester tester) async {
      // Arrange - Set loading state
      when(() => mockNotifier.state).thenReturn(testState.copyWith(isLoading: true));
      
      // Act
      await pumpReportsScreen(
        tester,
        screenSize: const Size(400, 800),
      );
      
      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
    
    testWidgets('should show error message when there is an error', (WidgetTester tester) async {
      // Arrange - Set error state
      when(() => mockNotifier.state).thenReturn(
  testState.copyWith(
          error: 'Failed to load reports',
          isLoading: false,
        ),
      );
      
      // Act
      await pumpReportsScreen(
        tester,
        screenSize: const Size(400, 800),
      );
      
      // Assert
      expect(find.text('Failed to load reports'), findsOneWidget);
    });
    
    testWidgets('should have proper spacing between stat cards on mobile', (WidgetTester tester) async {
      // Act
      await pumpReportsScreen(
        tester,
        screenSize: const Size(400, 800), // Mobile size
      );
      
      // Find stat cards and verify spacing
      final cards = find.byType(Card);
      expect(cards, findsNWidgets(4));
      
      // Get the positions of all cards
      final firstCardPosition = tester.getTopLeft(cards.first);
      final lastCardPosition = tester.getTopLeft(cards.last);
      
      // Verify vertical spacing between rows (should be 16.0 as per default padding)
      expect(lastCardPosition.dy - firstCardPosition.dy, greaterThan(100.0)); // Approximate card height + spacing
    });
  });
  
  group('Date Range Tests', () {
    testWidgets('should show correct date range in the header', (WidgetTester tester) async {
      // Arrange - Set specific dates
      final startDate = DateTime(2023, 1, 1);
      final endDate = DateTime(2023, 1, 31);
      when(() => mockNotifier.state).thenReturn(
        testState.copyWith(
          startDate: startDate,
          endDate: endDate,
        ),
      );
      
      // Act
      await pumpReportsScreen(
        tester,
        screenSize: const Size(400, 800),
      );
      
      // Assert
      expect(find.text('01/01/2023 - 31/01/2023'), findsOneWidget);
    });
  });
}
