import 'dart:io';

import 'package:city_pos/core/services/product_cache_service.dart';
import 'package:city_pos/data/models/category.dart';
import 'package:city_pos/data/models/product.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';

void main() {
  group('ProductCacheService Tests', () {
    setUpAll(() async {
      // Initialize Hive for testing with temporary directory
      final tempDir = Directory.systemTemp.createTempSync('hive_test');
      Hive.init(tempDir.path);

      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(ProductAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(CategoryAdapter());
      }

      await ProductCacheService.initialize();
    });

    tearDownAll(() async {
      // Clean up after tests
      await ProductCacheService.clearCache();
      await Hive.close();
    });

    setUp(() async {
      // Clear cache before each test
      await ProductCacheService.clearCache();
    });

    group('Product Cache Tests', () {
      test('should cache and retrieve products', () async {
        // Arrange
        final testProducts = [
          Product(
            id: '1',
            name: 'Test Product 1',
            price: 100.0,
            cost: 50.0,
            stockQuantity: 10,
            minStockLevel: 5,
          ),
          Product(
            id: '2',
            name: 'Test Product 2',
            price: 200.0,
            cost: 100.0,
            stockQuantity: 20,
            minStockLevel: 10,
          ),
        ];

        // Act
        await ProductCacheService.cacheProducts(testProducts);
        final cachedProducts = await ProductCacheService.getCachedProducts();

        // Assert
        expect(cachedProducts.length, equals(2));
        expect(cachedProducts[0].name, equals('Test Product 1'));
        expect(cachedProducts[1].name, equals('Test Product 2'));
      });

      test('should filter cached products by search', () async {
        // Arrange
        final testProducts = [
          Product(
            id: '1',
            name: 'Coffee',
            sku: 'CF001',
            price: 100.0,
            cost: 50.0,
            stockQuantity: 10,
            minStockLevel: 5,
          ),
          Product(
            id: '2',
            name: 'Tea',
            sku: 'TE001',
            price: 200.0,
            cost: 100.0,
            stockQuantity: 20,
            minStockLevel: 10,
          ),
        ];

        await ProductCacheService.cacheProducts(testProducts);

        // Act
        final coffeeProducts = await ProductCacheService.getCachedProducts(
          search: 'Coffee',
        );
        final teaProducts = await ProductCacheService.getCachedProducts(
          search: 'TE001',
        );

        // Assert
        expect(coffeeProducts.length, equals(1));
        expect(coffeeProducts[0].name, equals('Coffee'));
        expect(teaProducts.length, equals(1));
        expect(teaProducts[0].name, equals('Tea'));
      });

      test('should get product by ID', () async {
        // Arrange
        final testProduct = Product(
          id: 'test-id',
          name: 'Test Product',
          price: 100.0,
          cost: 50.0,
          stockQuantity: 10,
          minStockLevel: 5,
        );

        await ProductCacheService.cacheProducts([testProduct]);

        // Act
        final cachedProduct = await ProductCacheService.getCachedProductById(
          'test-id',
        );

        // Assert
        expect(cachedProduct, isNotNull);
        expect(cachedProduct!.name, equals('Test Product'));
      });

      test('should get product by SKU', () async {
        // Arrange
        final testProduct = Product(
          id: '1',
          name: 'Test Product',
          sku: 'TEST001',
          price: 100.0,
          cost: 50.0,
          stockQuantity: 10,
          minStockLevel: 5,
        );

        await ProductCacheService.cacheProducts([testProduct]);

        // Act
        final cachedProduct = await ProductCacheService.getCachedProductBySku(
          'TEST001',
        );

        // Assert
        expect(cachedProduct, isNotNull);
        expect(cachedProduct!.name, equals('Test Product'));
      });

      test('should get product by barcode', () async {
        // Arrange
        final testProduct = Product(
          id: '1',
          name: 'Test Product',
          barcode: '123456789',
          price: 100.0,
          cost: 50.0,
          stockQuantity: 10,
          minStockLevel: 5,
        );

        await ProductCacheService.cacheProducts([testProduct]);

        // Act
        final cachedProduct =
            await ProductCacheService.getCachedProductByBarcode('123456789');

        // Assert
        expect(cachedProduct, isNotNull);
        expect(cachedProduct!.name, equals('Test Product'));
      });

      test('should update cached product', () async {
        // Arrange
        final originalProduct = Product(
          id: '1',
          name: 'Original Product',
          price: 100.0,
          cost: 50.0,
          stockQuantity: 10,
          minStockLevel: 5,
        );

        await ProductCacheService.cacheProducts([originalProduct]);

        final updatedProduct = originalProduct.copyWith(
          name: 'Updated Product',
          price: 150.0,
        );

        // Act
        await ProductCacheService.updateCachedProduct(updatedProduct);
        final cachedProduct = await ProductCacheService.getCachedProductById(
          '1',
        );

        // Assert
        expect(cachedProduct, isNotNull);
        expect(cachedProduct!.name, equals('Updated Product'));
        expect(cachedProduct.price, equals(150.0));
      });

      test('should add product to cache', () async {
        // Arrange
        final newProduct = Product(
          id: 'new-id',
          name: 'New Product',
          price: 100.0,
          cost: 50.0,
          stockQuantity: 10,
          minStockLevel: 5,
        );

        // Act
        await ProductCacheService.addProductToCache(newProduct);
        final cachedProduct = await ProductCacheService.getCachedProductById(
          'new-id',
        );

        // Assert
        expect(cachedProduct, isNotNull);
        expect(cachedProduct!.name, equals('New Product'));
      });

      test('should remove product from cache', () async {
        // Arrange
        final testProduct = Product(
          id: 'remove-me',
          name: 'Remove Me',
          price: 100.0,
          cost: 50.0,
          stockQuantity: 10,
          minStockLevel: 5,
        );

        await ProductCacheService.cacheProducts([testProduct]);

        // Act
        await ProductCacheService.removeProductFromCache('remove-me');
        final cachedProduct = await ProductCacheService.getCachedProductById(
          'remove-me',
        );

        // Assert
        expect(cachedProduct, isNull);
      });
    });

    group('Category Cache Tests', () {
      test('should cache and retrieve categories', () async {
        // Arrange
        final testCategories = [
          Category(
            id: '1',
            name: 'Test Category 1',
            color: '#FF0000',
            icon: 'test1',
          ),
          Category(
            id: '2',
            name: 'Test Category 2',
            color: '#00FF00',
            icon: 'test2',
          ),
        ];

        // Act
        await ProductCacheService.cacheCategories(testCategories);
        final cachedCategories =
            await ProductCacheService.getCachedCategories();

        // Assert
        expect(cachedCategories.length, equals(2));
        expect(cachedCategories[0].name, equals('Test Category 1'));
        expect(cachedCategories[1].name, equals('Test Category 2'));
      });

      test('should get category by ID', () async {
        // Arrange
        final testCategory = Category(
          id: 'test-cat-id',
          name: 'Test Category',
          color: '#FF0000',
          icon: 'test',
        );

        await ProductCacheService.cacheCategories([testCategory]);

        // Act
        final cachedCategory = await ProductCacheService.getCachedCategoryById(
          'test-cat-id',
        );

        // Assert
        expect(cachedCategory, isNotNull);
        expect(cachedCategory!.name, equals('Test Category'));
      });
    });

    group('Cache Metadata Tests', () {
      test('should track last sync timestamp', () async {
        // Arrange
        final testProducts = [
          Product(
            id: '1',
            name: 'Test Product',
            price: 100.0,
            cost: 50.0,
            stockQuantity: 10,
            minStockLevel: 5,
          ),
        ];

        // Act
        await ProductCacheService.cacheProducts(testProducts);
        final lastSync = await ProductCacheService.getLastSyncTimestamp();

        // Assert
        expect(lastSync, isNotNull);
        expect(
          lastSync!.isBefore(DateTime.now().add(Duration(seconds: 1))),
          isTrue,
        );
      });

      test('should check if cache needs refresh', () async {
        // Act
        final needsRefresh = await ProductCacheService.needsRefresh();

        // Assert - should need refresh when no data cached
        expect(needsRefresh, isTrue);
      });

      test('should check if cache is empty', () async {
        // Act
        final isEmpty = await ProductCacheService.isEmpty();

        // Assert
        expect(isEmpty, isTrue);

        // Add some data
        final testProduct = Product(
          id: '1',
          name: 'Test Product',
          price: 100.0,
          cost: 50.0,
          stockQuantity: 10,
          minStockLevel: 5,
        );

        await ProductCacheService.cacheProducts([testProduct]);
        final isEmptyAfter = await ProductCacheService.isEmpty();

        expect(isEmptyAfter, isFalse);
      });

      test('should get cache statistics', () async {
        // Arrange
        final testProduct = Product(
          id: '1',
          name: 'Test Product',
          price: 100.0,
          cost: 50.0,
          stockQuantity: 10,
          minStockLevel: 5,
        );

        final testCategory = Category(
          id: '1',
          name: 'Test Category',
          color: '#FF0000',
          icon: 'test',
        );

        await ProductCacheService.cacheProducts([testProduct]);
        await ProductCacheService.cacheCategories([testCategory]);

        // Act
        final stats = await ProductCacheService.getCacheStats();

        // Assert
        expect(stats['productsCount'], equals(1));
        expect(stats['categoriesCount'], equals(1));
        expect(stats['lastSync'], isNotNull);
        expect(stats['isEmpty'], isFalse);
      });
    });
  });
}
