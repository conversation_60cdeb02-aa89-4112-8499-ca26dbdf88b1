import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock data class
class InventoryItem {
  final String name;
  final int stock;
  final double price;

  InventoryItem({required this.name, required this.stock, required this.price});
}

// Mock callback
class MockOnEdit extends Mock {
  void call();
}

void main() {
  late MockOnEdit mockOnEdit;

  setUp(() {
    mockOnEdit = MockOnEdit();
  });

  Future<void> pumpInventoryItemWidget(WidgetTester tester, {required InventoryItem item}) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: InventoryItemWidget(
            item: item,
            onEdit: mockOnEdit.call,
          ),
        ),
      ),
    );
    await tester.pump();
  }

  group('InventoryItemWidget UI Tests', () {
    testWidgets('renders inventory item widget with correct details', (WidgetTester tester) async {
      final item = InventoryItem(name: 'Product 1', stock: 50, price: 10.5);
      await pumpInventoryItemWidget(tester, item: item);

      expect(find.text('Product 1'), findsOneWidget);
      expect(find.text('Stock: 50'), findsOneWidget);
      expect(find.byIcon(Icons.edit), findsOneWidget);
    });

    testWidgets('tapping edit icon calls onEdit', (WidgetTester tester) async {
      final item = InventoryItem(name: 'Product 1', stock: 50, price: 10.5);
      await pumpInventoryItemWidget(tester, item: item);

      await tester.tap(find.byIcon(Icons.edit));
      await tester.pump();

      verify(() => mockOnEdit.call()).called(1);
    });
  });
}

class InventoryItemWidget extends StatelessWidget {
  final InventoryItem item;
  final Function() onEdit;

  const InventoryItemWidget({
    Key? key,
    required this.item,
    required this.onEdit,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(item.name),
      subtitle: Text('Stock: ${item.stock}'),
      trailing: IconButton(
        icon: Icon(Icons.edit),
        onPressed: onEdit,
      ),
    );
  }
}
