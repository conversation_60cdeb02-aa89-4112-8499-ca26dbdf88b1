import 'package:flutter_test/flutter_test.dart';
import 'package:city_pos/core/providers/invoice_settings_provider.dart';

void main() {
  group('Payment Flow Debug Tests', () {
    test('invoice settings should default to true', () {
      final settings = InvoiceSettings();
      expect(settings.showInvoiceAfterPayment, isTrue);
    });

    test('invoice settings notifier should load with default true', () {
      final notifier = InvoiceSettingsNotifier();
      // Should default to true even without SharedPreferences
      expect(notifier.state.showInvoiceAfterPayment, isTrue);
    });

    test('invoice settings should toggle correctly in memory', () {
      final notifier = InvoiceSettingsNotifier();
      
      // Initial state should be true
      expect(notifier.state.showInvoiceAfterPayment, isTrue);
      
      // Update state directly (without SharedPreferences)
      notifier.state = notifier.state.copyWith(showInvoiceAfterPayment: false);
      expect(notifier.state.showInvoiceAfterPayment, isFalse);
      
      // Update back to true
      notifier.state = notifier.state.copyWith(showInvoiceAfterPayment: true);
      expect(notifier.state.showInvoiceAfterPayment, isTrue);
    });
  });
}
