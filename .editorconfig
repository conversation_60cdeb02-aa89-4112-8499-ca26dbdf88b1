# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Dart files
[*.dart]
indent_style = space
indent_size = 2
max_line_length = 80

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# XML files
[*.xml]
indent_style = space
indent_size = 2

# Gradle files
[*.gradle]
indent_style = space
indent_size = 4

# Kotlin files
[*.{kt,kts}]
indent_style = space
indent_size = 4

# Swift files
[*.swift]
indent_style = space
indent_size = 4

# JavaScript/TypeScript files
[*.{js,ts}]
indent_style = space
indent_size = 2

# CSS files
[*.css]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# Makefile
[Makefile]
indent_style = tab

# CMake files
[CMakeLists.txt]
indent_style = space
indent_size = 2

# Shell scripts
[*.sh]
indent_style = space
indent_size = 2
