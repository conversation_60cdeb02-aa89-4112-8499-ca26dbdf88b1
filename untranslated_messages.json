{"ja": ["category", "noCategoriesYet", "noCategoriesFound", "addFirstCategory", "tryDifferent<PERSON><PERSON><PERSON>", "addCategory", "editCategory", "categoryName", "categoryDescription", "addStockTransaction", "salesCategories", "serviceCategories", "interestCategories", "purchaseCategories", "salaryCategories", "rentCategories", "utilitiesCategories", "marketingCategories", "noNotifications", "allNotificationsWillAppearHere", "stockTransactionsTitle", "weeklyRevenueReport", "weeklyRevenueReportReady", "viewReport", "<PERSON><PERSON><PERSON><PERSON>", "freshMilk", "customerName", "<PERSON><PERSON><PERSON><PERSON>", "alreadyHaveAccountLogin", "loginNowLink", "posTitle", "cartTitle", "clearAllTooltip", "invoiceListTitle", "invoicesCountLabel", "financeTitle", "markAllAsRead", "deleteAllRead", "deleteAllReadConfirm", "deleteAllReadMessage", "notificationSettingsTitle", "notificationSettingsMessage", "productsCount", "viewCart", "clearCart", "itemTotal", "noInvoicesFound", "createFirstInvoiceToStart", "posScreenTitle", "posScreenEnhancedTitle", "clearCartTooltip", "scanBarcode", "blackCoffeeDemo", "freshMilkDemo", "customerNameDemo", "weeklyRevenueReportTitle", "weeklyRevenueReportMessage", "issueDate", "dueDate", "paidAmountShort", "remainingAmount", "statusDraft", "statusSent", "statusPaid", "statusOverdue", "statusCancelled", "cashFlowDetailTitle", "editEntry", "deleteEntry", "saveChanges", "cancelEdit", "entryUpdatedSuccessfully", "errorUpdatingEntry", "confirmDeleteEntry", "deleteEntryMessage", "entryDeletedSuccessfully", "errorDeletingEntry", "stockTransactionsScreen", "addStockTransactionButton", "stockInType", "stockOutType", "stockAdjustmentType", "noTransactionsFound", "createFirstTransaction", "totalTransactions", "stockFilters", "transactionTypeFilter", "allTransactionTypes", "stockDateRange", "stockStartDate", "stockEndDate", "stockQuickFilters", "stockToday", "stockThisWeek", "stockThisMonth", "stockClearFilters", "createStockTransaction", "selectTransactionType", "partner<PERSON>ame", "enterPartnerName", "stockProducts", "addStockProduct", "stockProductName", "enterStockProductName", "pleaseAddAtLeastOneStockProduct", "pleaseEnterStockProductName", "pleaseEnterValidStockQuantity", "pleaseEnterValidStockPrice", "noStockTransactionsYet", "createFirstStockTransaction", "moreItems", "transactionInfo", "transactionNumber", "transactionTypeLabel", "createdDate", "cartWithCount", "emptyCartMessage", "selectProductsToAddToCart", "addStockTransactionTitle", "saveStockTransaction", "transactionCreatedSuccessfully", "errorCreatingTransaction", "stockInRadio", "stockOutRadio", "posSearchProducts", "posSearchProductsHint", "posProductCode", "posStockQuantityShort", "posNoProductsFound", "posNoProductsYet", "posSubtotal", "posTax", "posDiscount", "posGrandTotal", "posSaveDraft", "pos<PERSON><PERSON><PERSON><PERSON>", "posDraftSaved", "posScanBarcodeTitle", "posEnterBarcode", "posScanOrEnterBarcode", "posUseCameraToScan", "addStockTransactionScreenTitle", "saveTransactionButton", "stockInLabel", "stockOutLabel", "stockTransactionCreatedSuccess", "errorCreatingStockTransaction", "createStockTransactionDialogTitle", "createTransactionButton", "paymentTitle", "enterAmountReceived", "paymentFeatureInDevelopment", "closeButton", "completeButton", "orderCreatedSuccess", "demoProduct1", "demoProduct2", "demoProduct3", "productSectionTitle", "selectProductLabel", "pleaseSelectProduct", "quantityAndPriceTitle", "quantityLabel", "unitLabel", "pleaseEnterQuantity", "invalidQuantity", "unitPriceLabel", "invalidUnitPrice", "additionalInfoTitle", "referenceNumberLabel", "referenceNumberHint", "noteLabel", "partnerNameLabel", "partnerNameHint", "pleaseEnterPartnerName", "productListTitle", "addProductButton", "noteHint", "stockInRadioLabel", "stockOutRadioLabel", "noProductsYetMessage", "clickAddProductToStart", "dialogProductNameLabel", "dialogPleaseEnterProductName", "quantityMustBeGreaterThanZero", "pleaseEnterUnitPrice", "unitPriceMustBeGreaterThanZero", "pleaseAddAtLeastOneProduct", "inventoryNoProductsYet", "inventoryAddFirstProduct", "partnerManagement", "addPartnerTooltip", "refreshTooltip", "totalPartners", "activePartners", "customersLabel", "suppliersLabel", "searchPartners", "searchPartnerHint", "noPartnersFound", "noPartnersYet", "tryDifferentKeywords", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addPartnerButton", "posMenuTitle", "notificationsMenuTitle", "stockTransactionsMenuTitle", "transactionNotFound", "transactionDetails", "transactionEditTooltip", "transactionSaveChanges", "deleteTransactionButton", "confirmDeleteTransaction", "deleteTransactionMessage", "systemErrorOccurred", "tryAgainB<PERSON>on", "<PERSON><PERSON><PERSON><PERSON>", "created<PERSON>y", "noteFieldLabel", "enterNoteHint", "noNoteAvailable", "productListLabel", "productCountLabel", "totalQuantityLabel", "totalValueLabel", "summaryLabel", "cancelEditButton", "saveChangesButton", "transactionUpdatedSuccess", "errorUpdatingTransaction", "transactionDeletedSuccess", "errorDeletingTransaction", "addTransactionTitle", "noTransactionsYet", "addFirstTransaction", "noChartData", "addTransactionForChart", "financialOverview", "searchCategoriesHint", "transactionDetailsTitle", "descriptionLabel", "typeLabel", "categoryLabel", "amountLabel", "incomeTypeLabel", "expenseTypeLabel", "orderDetailsLabel", "subtotalLabel", "createNewButton", "viewDetails", "copy", "print", "export", "import", "upload", "download", "sync", "refreshButton", "restore", "backup", "options", "filtersButton", "sortButton", "searchButton", "results", "page", "next", "previous", "first", "last", "trueValue", "falseValue", "on", "off", "invoiceTitle", "salesInvoice", "invoiceNumber", "taxLabel", "discountLabel", "totalAmountLabel", "paymentMethodLabel", "thankYouMessage", "closeDialogButton", "printInvoiceTitle", "printInvoiceQuestion", "noButton", "printInvoiceButton", "confirmationTitle", "clearCartConfirmation", "paymentProcessingError", "invoiceDetailTitle", "invoiceInformation", "invoiceNumberLabel", "createdDateLabel", "statusLabel", "createdByLabel", "cashPayment", "creditCardPayment", "bankTransferPayment", "eWalletPayment", "otherPayment", "paidStatus", "unpaidStatus", "processingStatus", "cancelledStatus", "completedStatus", "pendingConfirmationStatus", "confirmedStatus", "shippingStatus", "deliveredStatus", "returnedS<PERSON>us", "refundedStatus", "errorStatus", "successStatus", "warningStatus", "infoStatus", "confirmStatus", "rejectStatus", "showLabel", "<PERSON><PERSON><PERSON><PERSON>", "openLabel", "startLabel", "endLabel", "<PERSON><PERSON><PERSON><PERSON>", "continueLabel", "stopLabel", "restartLabel", "updateLabel", "reloadLabel", "pasteLabel", "<PERSON><PERSON><PERSON><PERSON>", "undo<PERSON><PERSON>l", "redoLabel", "groupLabel", "classifyLabel", "tagLabel", "labelLabel", "colorLabel", "sizeLabel", "weight<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "widthLabel", "lengthLabel", "areaLabel", "volumeLabel", "quantityFieldLabel", "unitPriceFieldLabel", "totalPriceLabel", "grandTotalLabel", "changeLabel", "<PERSON><PERSON><PERSON><PERSON>", "feeLabel", "promotionLabel", "voucherLabel", "discountCodeLabel", "loyaltyPointsLabel", "accumulate<PERSON><PERSON><PERSON>", "exchangeLabel", "convertLabel", "exchangeRateLabel", "unitFieldLabel", "packageLabel", "boxLabel", "cartonLabel", "bag<PERSON>abel", "bottleLabel", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pieceLabel", "itemLabel", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "kilogramLabel", "gramLabel", "literLabel", "milliliterLabel", "meterLabel", "centimeterLabel", "millimeterLabel", "inchLabel", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "customerNameNote", "customerPhoneNote", "salesScreenTitle", "newSaleButton", "salesAnalytics", "ordersListTitle", "orderDetailsTitle", "orderStatusField", "orderDate", "orderTime", "customerInfo", "customerNameField", "customerPhoneField", "customerAddressField", "orderNotesField", "paymentMethodField", "amountPaidField", "changeAmountField", "vatAmountField", "discountAmountField", "promotionCodeField", "loyaltyPointsField", "membershipField", "<PERSON><PERSON><PERSON><PERSON>", "orderStatusPaid", "orderStatusUnpaid", "orderStatusProcessing", "orderStatusReturned", "orderStatusRefunded", "printInvoiceAction", "sendInvoiceAction", "saveOrderAction", "cancelOrderAction", "confirmPaymentAction", "undoAction", "refreshAction", "reloadAction", "syncAction", "backupAction", "restoreAction", "searchProductsHint", "scanBarcodeAction", "addToCartAction", "removeFromCartAction", "updateQuantityAction", "clearAllCartAction", "continueShoppingAction", "checkoutNowAction", "selectCustomerAction", "addNewCustomerAction", "applyDiscountAction", "calculateTaxAction", "previewInvoiceAction", "salesReportsTitle", "salesStatisticsTitle", "salesHistoryTitle", "transactionHistoryTitle", "revenueLabel", "profitLabel", "quantitySoldLabel", "bestSellingProductsLabel", "loyalCustomersLabel", "productCatalogTitle", "productSelectionTitle", "cartManagementTitle", "checkoutScreenTitle", "receiptPrintingTitle", "scanBarcodeTitle", "enterBarcodeLabel", "scanOrEnterBarcodeHint", "orUseCameraToScan", "addedToCartMessage", "productNotFoundMessage", "cashMethodLabel", "creditCardMethodLabel", "bankTransferMethodLabel", "eWalletMethodLabel", "otherMethodLabel", "amountReceivedLabel", "changeAmountLabel", "enterAmountReceivedHint", "exactAmountButton", "processPaymentButton", "processingPaymentLabel", "printReceiptDialogTitle", "printReceiptDialogContent", "noButtonLabel", "printReceiptButtonLabel", "taxReceiptLabel", "discountReceiptLabel", "lineItemTotalLabel", "checkoutButtonLabel", "saveDraftButtonLabel", "customerInfoTooltip", "discountTooltip", "customerInfoDialogTitle", "customerNameFieldLabel", "customerPhoneFieldLabel", "saveButtonLabel", "discountDialogTitle", "discountAmountFieldLabel", "subtotalDisplayLabel", "applyButtonLabel", "checkoutDialogTitle", "subtotalCheckoutLabel", "taxCheckoutLabel", "discountCheckoutLabel", "returningToDashboard", "appResumed", "dataUpdated", "manualRefresh", "refreshData", "errorLoadingData", "retryAfterError", "barcodeDialogTitle", "barcodeDialogMessage", "barcodeDialogScanButton", "barcodeDialogCancelLabel", "totalCheckoutLabel", "paymentMethodCheckoutLabel", "customerCashLabel", "changeCheckoutLabel", "completeButtonLabel", "insufficientCashMessage", "orderCreatedSuccessMessage", "itemCountLabel", "revenue", "profit", "orderCount", "inventoryValue", "dateRange<PERSON>abel", "overview", "salesReportTitle", "totalCost", "grossProfit", "profitMargin", "inventoryReportTitle", "totalProducts", "inventoryWorth", "lowStockItems", "outOfStockItems", "financeReportTitle", "totalIncome", "totalExpense", "netCashFlow", "endingBalance", "sold", "inventoryAlerts", "allProductsInStock", "stockInfo", "outOfStock", "lowStock", "receiptFromOrder", "errorSendingPaymentNotification", "paymentMethods", "card", "transfer", "searchInvoice", "invoiceType", "sale", "return_", "draft", "sent", "overdue", "income", "bankTransfer", "creditCard", "debitCard", "eWallet", "cashBalance", "totalInvoices", "paidInvoices", "pendingInvoices", "overdueInvoices", "totalValue", "collectedAmount", "overdueAmount", "collectionRate", "createNewInvoice"]}