Chức năng liên quan:
+ <PERSON><PERSON> hàng
+ Thêm mới hàng hoá
+ chi tiết sản phẩm
Yêu cầu:
+ Làm một cái cơ chế cache bằng hive cho hàng hoá, khi call api từ supabase để lấy danh sách hàng hoá về rồi lưu lại vào hive sau đó ở các màn hình bán hàng,... 
+ <PERSON><PERSON><PERSON> màn hình liên quan đến hàng hoá thì sẽ lấy từ db cache trên app ra để hiển thị lại, 
+ Khi app được mở lần đầu tiên thì sẽ call api từ supabase để lấy danh sách hàng hoá về và lưu vào hive, các lần sau thì sẽ lấy từ hive ra để hiển thị lại, 
+ Khi có sự thay đổi dữ liệu trên supabase thì sẽ call api từ supabase để lấy danh sách hàng hoá về và lưu lại vào hive để cập nhật dữ liệu mới nhất, 
<PERSON><PERSON>u ý quan trọng: 
+ Chỉ sửa trong phạm vi đã yêu cầu. 
+ Không chỉnh sửa giao diện, tính năng, phương thức không liên quan đến yêu cầu.
+ Phải viết code tối ưu, điều này bắt buộc phải kiểm tra code để không 