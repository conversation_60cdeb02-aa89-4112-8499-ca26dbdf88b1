Chức năng liên quan:
+ <PERSON>h toán
+ Dối tác
Yêu cầu:
+ Tại màn hình thanh toán ở mục phương thức thanh toán hãy xoá 2 item phương thức thành toán là: ví điện tử, kết hợp.
+ Tại mục thông tin khách hàng hãy điều chỉnh lại phương thức chọn khách hàng, hãy tạo một thanh tìm kiếm và khi bấm vào còn có thể 
xổ xuống danh sách khách hàng, đối tác để bấm chọn, giá trị mặc định của nó sẽ là khách lẻ (khách hàng ở đây phải hiển thị đúng với danh sách khách hàng đối tác đã thêm trên app).
+ Điều này đồng nghĩa với việc ở màn hình hoá đơn và cả hoá đơn pdf được chia sẻ cũng phải hiển thị đúng với khách hàng được chọn
+ Tại màn hình thanh toán cũng cần có logic tính công nợ cho khách hàng khi tiền khách thanh toán nhỏ hơn số tiền của đơn hàng, công nợ này phải được cập nhật để khi mở màn hình chi tiết khách hàng phải được thể hiện ở dòng công nợ hiện tại
Lưu ý quan trọng: 
+ Chỉ sửa trong phạm vi đã yêu cầu. 
+ Không chỉnh sửa giao diện, tính năng, phương thức không liên quan đến yêu cầu.