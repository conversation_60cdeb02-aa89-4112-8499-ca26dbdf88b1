Chức năng liên quan:
+ <PERSON>ài đặt
+ <PERSON><PERSON> hàng

Yêu cầu:
Phát triển tính năng in hoá đơn
+ T<PERSON><PERSON> màn hình setting tạo một menu thiết lập máy in, bấm vào chuyển đến màn hình thiết lập máy in trong màn hình này sẽ có danh sách máy in và button thêm mới máy in (máy in được thêm mới sẽ hiển thị ở danh sách máy in), thiết lập máy in qua lan với địa chỉ ip của máy in, sẽ có mục nhập tên máy in, ip máy in, port, (phần kỹ thuật là logic code thì mày phải tự làm để khi kết nối nó có thể in được)
+ Tại màn hình bán hàng khi thanh toán thành công sẽ hiển thị hoá đơn có button in ở bottom màn hình, bấm vào button in sẽ thực hiện in hoá đơn
+ Luồng như sau: Tạ<PERSON> màn hình setting có menu thiết lập máy in -> Bấm vào menu thiết lập máy in -> chuyển đến màn hình thiết lập máy in -> thiết lập máy in thành công -> Tạo đơn hàng tại màn hình bán hàng -> thực hiện thanh toán -> Thanh toán thành công hiển thị hoá đơn có button in ở bottom màn hình, bấm vào button in sẽ thực hiện in hoá đơn

Gơi ý phương án triển khai:
---

## ✅ 1. **In hóa đơn bằng máy in Bluetooth (máy in bill mini)** – dùng cho Android/iOS

### 📌 Dùng trong tiệm, cửa hàng để in bill POS qua máy in như: XPrinter, Epson, Goojprt...

### 🔧 Gợi ý thư viện Flutter:

* [`blue_thermal_printer`](https://pub.dev/packages/blue_thermal_printer) – Android only.
* [`esc_pos_bluetooth`](https://pub.dev/packages/esc_pos_bluetooth) – Android, in theo chuẩn ESC/POS.
* [`esc_pos_printer`](https://pub.dev/packages/esc_pos_printer) – cho in qua IP LAN.
* [`flutter_bluetooth_serial`](https://pub.dev/packages/flutter_bluetooth_serial) – custom sâu hơn nếu cần.

### 🧠 Cách triển khai:

1. Scan & connect với máy in Bluetooth.
2. Gửi dữ liệu hóa đơn dưới dạng lệnh ESC/POS hoặc chuỗi văn bản.
3. Format hoá đơn bằng widget hoặc chuyển về plain text.

### 💡 Ví dụ code dùng `esc_pos_bluetooth`:

```dart
final profile = await CapabilityProfile.load();
final printer = BluetoothPrinterManager();

printer.connect(device);
printer.printTicket((Ticket ticket) {
  ticket.text('City POS Store');
  ticket.text('---');
  ticket.text('Mặt hàng 1      50.000đ');
  ticket.text('Mặt hàng 2      30.000đ');
  ticket.text('---');
  ticket.text('Tổng:         80.000đ');
  return ticket;
});
```

---

## ✅ 2. **In ra file PDF và in qua hệ thống OS (AirPrint, Android Print)**

### 📌 Dùng khi muốn lưu bản in, gửi qua Zalo/email hoặc in qua máy in văn phòng.

### 🔧 Gợi ý thư viện:

* [`pdf`](https://pub.dev/packages/pdf) – tạo PDF file (cực mạnh).
* [`printing`](https://pub.dev/packages/printing) – hỗ trợ in từ PDF, in qua hệ thống (AirPrint, Google Print).
* [`open_filex`](https://pub.dev/packages/open_filex) – mở file PDF sau khi in.

### 🧠 Cách triển khai:

1. Dùng `pdf` để generate PDF hoá đơn.
2. Dùng `printing.printPdf()` để gửi lệnh in.
3. Dùng `savePdf()` nếu muốn lưu file.

### 💡 Ví dụ:

```dart
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

final pdf = pw.Document();

pdf.addPage(
  pw.Page(
    build: (pw.Context context) => pw.Column(
      children: [
        pw.Text('City POS Hóa đơn'),
        pw.Text('Mặt hàng A - 50.000đ'),
        pw.Text('Tổng cộng - 100.000đ'),
      ],
    ),
  ),
);

await Printing.layoutPdf(
  onLayout: (PdfPageFormat format) async => pdf.save(),
);
```

---

## ✅ 3. **In qua máy in mạng (LAN hoặc WiFi)**

### 📌 Dùng khi máy in kết nối cùng mạng LAN hoặc có địa chỉ IP riêng.

### 🔧 Gợi ý thư viện:

* [`esc_pos_printer`](https://pub.dev/packages/esc_pos_printer) – gửi lệnh ESC/POS qua IP.
* [`network_printer`](https://pub.dev/packages/network_printer) – in qua IP.

### 💡 Triển khai:

* Gửi lệnh trực tiếp đến máy in qua IP\:Port (thường là 192.168.x.x:9100).

---

## ✅ 4. **In từ Web hoặc Admin Panel**

Nếu bạn có admin panel (web), có thể cho in từ đó bằng JavaScript hoặc tạo link tải PDF. Flutter app chỉ cần gọi API tạo hóa đơn → tải về/in.

---

## ✅ Tóm tắt các phương án:

| Nhu cầu in hóa đơn         | Gợi ý thư viện                              | Độ dễ triển khai    |
| -------------------------- | ------------------------------------------- | ------------------- |
| In Bluetooth (máy in bill) | `esc_pos_bluetooth`, `blue_thermal_printer` | Trung bình          |
| In PDF + hệ thống in OS    | `pdf`, `printing`                           | Rất dễ, đa nền tảng |
| In qua mạng LAN (IP)       | `esc_pos_printer`, `network_printer`        | Trung bình          |
| In từ web / backend        | Generate PDF ở server, gửi link             | Dễ quản lý          |

---

## 👉 Gợi ý chiến lược cho app Flutter của bạn:

1. **Dùng `pdf + printing` để làm chuẩn in hóa đơn → ai cũng dùng được.**
2. **Tuỳ biến thêm nếu người dùng có máy in Bluetooth/LAN → cho cấu hình trong app.**
3. Nếu cần mình có thể viết giúp bạn:

   * **Template hóa đơn PDF cho Flutter.**
   * **Code mẫu in bằng máy in Bluetooth.**
   * **Cấu hình đa dạng cho máy in.**

Bạn muốn in kiểu nào trước (Bluetooth, PDF, IP)? Mình sẽ viết code gợi ý ngay.

Lưu ý quan trọng: 
+ Chỉ sửa trong phạm vi đã yêu cầu. 
+ Không chỉnh sửa giao diện, tính năng, phương thức không liên quan đến yêu cầu.
+ Phải viết code tối ưu, điều này bắt buộc phải kiểm tra code để không lặp code hay các phương thúc đã có.