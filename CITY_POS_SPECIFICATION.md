# ĐẶC TẢ TÍNH NĂNG ỨNG DỤNG CITY POS

## 📋 **THÔNG TIN TỔNG QUAN**

### **Tên ứng dụng**: City POS (Point of Sale)
### **Phiên bản**: 1.0.0
### **Nền tảng**: Flutter (Android, iOS, Web)
### **Backend**: Supabase (PostgreSQL + Auth + Storage + Realtime)
### **State Management**: Riverpod
### **Ngôn ngữ hỗ trợ**: Ti<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tiếng Nhật

---

## 🎯 **MỤC TIÊU ỨNG DỤNG**

Xây dựng ứng dụng đa nền tảng (mobile/tablet/web) phục vụ cho việc quản lý toàn bộ quy trình bán hàng, tồ<PERSON> kho, xu<PERSON><PERSON> nhập, hóa đơn, khách hàng và báo cáo tài chính. <PERSON><PERSON> thống dễ mở rộng, giao diện thân thiện với người dùng, bảo mật cao và hiệu suất ổn định.

---

## 🏗️ **KIẾN TRÚC HỆ THỐNG**

### **Frontend**: 
- **Flutter SDK**: 3.32.0 (locked version)
- **Dart SDK**: 3.8.0
- **Responsive Design**: Hỗ trợ mobile, tablet, desktop
- **State Management**: Riverpod
- **Local Storage**: Hive (offline caching)
- **Routing**: GoRouter

### **Backend**:
- **Database**: PostgreSQL (qua Supabase)
- **Authentication**: Supabase Auth (Email/Password)
- **File Storage**: Supabase Storage
- **Realtime**: Supabase Realtime Channels
- **Security**: Row Level Security (RLS)

### **Tích hợp bên ngoài**:
- **Push Notifications**: Firebase Cloud Messaging
- **PDF Generation**: pdf package
- **Network Printing**: printing package
- **Barcode Scanning**: mobile_scanner
- **Charts**: fl_chart

---

## 📱 **CÁC TÍNH NĂNG CHÍNH**

## 1. **🔐 HỆ THỐNG XÁC THỰC (AUTHENTICATION)**

### **1.1 Đăng nhập**
- **Phương thức**: Email + Password
- **Tính năng "Nhớ đăng nhập"**: Lưu thông tin đăng nhập local
- **Tự động tạo tài khoản**: Nếu user chưa tồn tại khi đăng nhập
- **Demo Mode**: Hỗ trợ chế độ demo không cần internet

### **1.2 Đăng ký**
- **Thông tin bắt buộc**: Email, Password, Họ tên
- **Thông tin tùy chọn**: Số điện thoại
- **Xác thực email**: Tự động qua Supabase Auth
- **Tạo profile**: Tự động tạo user profile sau khi đăng ký

### **1.3 Quản lý phiên đăng nhập**
- **Session Management**: Tự động refresh token
- **Logout**: Xóa session và chuyển về màn hình đăng nhập
- **Security**: JWT token validation

### **1.4 Phân quyền người dùng**
- **Admin**: Toàn quyền quản lý hệ thống
- **Manager**: Quản lý sản phẩm, đơn hàng, báo cáo
- **User**: Chỉ sử dụng POS và xem báo cáo cơ bản

---

## 2. **📊 DASHBOARD (TRANG CHÍNH)**

### **2.1 Thống kê tổng quan**
- **Doanh thu hôm nay**: Tổng tiền từ đơn hàng hoàn thành
- **Số đơn hàng hôm nay**: Đếm đơn hàng trong ngày
- **Sản phẩm sắp hết hàng**: Số lượng sản phẩm dưới mức tối thiểu
- **Tổng số khách hàng**: Số lượng partners loại customer

### **2.2 Biểu đồ và thống kê**
- **Biểu đồ doanh thu**: Theo ngày/tuần/tháng
- **Top sản phẩm bán chạy**: Danh sách sản phẩm có doanh số cao
- **Thống kê tồn kho**: Cảnh báo hàng sắp hết
- **Hoạt động gần đây**: Lịch sử đơn hàng, giao dịch

### **2.3 Quick Actions**
- **Tạo đơn hàng mới**: Chuyển đến POS
- **Thêm sản phẩm**: Chuyển đến màn hình thêm sản phẩm
- **Xem báo cáo**: Chuyển đến màn hình báo cáo
- **Quản lý tồn kho**: Chuyển đến màn hình inventory

### **2.4 Realtime Updates**
- **Auto-refresh**: Tự động cập nhật khi có thay đổi
- **Pull-to-refresh**: Làm mới thủ công
- **Background sync**: Đồng bộ dữ liệu khi app resume

---

## 3. **🛒 HỆ THỐNG BÁN HÀNG (POS - POINT OF SALE)**

### **3.1 Giao diện POS**
- **Multi-tab support**: Hỗ trợ nhiều đơn hàng cùng lúc
- **Product grid**: Hiển thị sản phẩm dạng lưới với hình ảnh
- **Category filter**: Lọc sản phẩm theo danh mục
- **Search**: Tìm kiếm sản phẩm theo tên, SKU, barcode
- **Barcode scanner**: Quét mã vạch để thêm sản phẩm

### **3.2 Giỏ hàng (Cart)**
- **Add to cart**: Thêm sản phẩm vào giỏ
- **Quantity adjustment**: Tăng/giảm số lượng
- **Remove items**: Xóa sản phẩm khỏi giỏ
- **Auto-remove zero quantity**: Tự động xóa sản phẩm có số lượng 0
- **Cart persistence**: Lưu giỏ hàng tạm thời (chỉ đơn chưa thanh toán)

### **3.3 Tính toán giá**
- **Subtotal**: Tổng tiền trước thuế và giảm giá
- **Discount**: Giảm giá theo số tiền hoặc phần trăm
- **Tax**: Tính thuế (nếu có)
- **Total**: Tổng tiền cuối cùng
- **Real-time calculation**: Cập nhật ngay khi thay đổi

### **3.4 Thanh toán**
- **Payment methods**: Tiền mặt, Chuyển khoản, Thẻ
- **Customer info**: Thông tin khách hàng (tùy chọn)
- **Amount paid**: Số tiền khách trả
- **Change calculation**: Tính tiền thừa
- **Payment validation**: Kiểm tra số tiền hợp lệ

### **3.5 Xử lý đơn hàng**
- **Order creation**: Tạo đơn hàng trong database
- **Inventory update**: Tự động trừ tồn kho
- **Stock validation**: Kiểm tra đủ hàng trước khi bán
- **Order numbering**: Tự động tạo mã đơn hàng
- **Multi-user support**: Nhiều user có thể bán cùng lúc

### **3.6 In hóa đơn**
- **Invoice display**: Hiển thị hóa đơn sau thanh toán
- **PDF generation**: Tạo file PDF hóa đơn
- **Network printing**: In qua máy in mạng LAN
- **Share invoice**: Chia sẻ hóa đơn qua email/social

---

## 4. **📦 QUẢN LÝ TỒN KHO (INVENTORY)**

### **4.1 Quản lý sản phẩm**
- **Product CRUD**: Thêm, sửa, xóa, xem sản phẩm
- **Product information**: Tên, mô tả, SKU, barcode, giá, giá vốn
- **Category management**: Phân loại sản phẩm theo danh mục
- **Image upload**: Upload và quản lý hình ảnh sản phẩm
- **Stock tracking**: Theo dõi số lượng tồn kho

### **4.2 Quản lý danh mục**
- **Category CRUD**: Thêm, sửa, xóa danh mục
- **Color coding**: Mã màu cho từng danh mục
- **Icon selection**: Chọn icon cho danh mục
- **Hierarchical structure**: Hỗ trợ danh mục con (tùy chọn)

### **4.3 Quản lý tồn kho**
- **Stock levels**: Mức tồn kho hiện tại, tối thiểu, tối đa
- **Low stock alerts**: Cảnh báo hàng sắp hết
- **Stock adjustments**: Điều chỉnh tồn kho thủ công
- **Stock transactions**: Lịch sử xuất nhập kho
- **Inventory reports**: Báo cáo tồn kho

### **4.4 Xuất nhập kho**
- **Stock transaction types**: Nhập kho, xuất kho, điều chỉnh, kiểm kê
- **Batch operations**: Xử lý nhiều sản phẩm cùng lúc
- **Supplier integration**: Liên kết với nhà cung cấp
- **Cost tracking**: Theo dõi giá vốn
- **Expiry date management**: Quản lý hạn sử dụng (tùy chọn)

---

## 5. **💰 QUẢN LÝ TÀI CHÍNH (FINANCE)**

### **5.1 Sổ quỹ (Cashbook)**
- **Cash flow tracking**: Theo dõi dòng tiền vào/ra
- **Transaction types**: Thu, chi, chuyển khoản
- **Categories**: Phân loại giao dịch (bán hàng, mua hàng, chi phí...)
- **Balance calculation**: Tính số dư tự động
- **Multi-currency**: Hỗ trợ nhiều loại tiền tệ (tùy chọn)

### **5.2 Báo cáo tài chính**
- **Revenue reports**: Báo cáo doanh thu theo thời gian
- **Expense reports**: Báo cáo chi phí
- **Profit/Loss**: Báo cáo lãi/lỗ
- **Cash flow statements**: Báo cáo dòng tiền
- **Tax reports**: Báo cáo thuế

### **5.3 Quản lý thanh toán**
- **Payment tracking**: Theo dõi các khoản thanh toán
- **Outstanding payments**: Công nợ phải thu/phải trả
- **Payment reminders**: Nhắc nhở thanh toán
- **Payment methods**: Quản lý phương thức thanh toán
- **Bank reconciliation**: Đối soát ngân hàng (tùy chọn)

---

## 6. **🤝 QUẢN LÝ ĐỐI TÁC (PARTNERS)**

### **6.1 Quản lý khách hàng**
- **Customer database**: Cơ sở dữ liệu khách hàng
- **Customer information**: Tên, email, phone, địa chỉ
- **Purchase history**: Lịch sử mua hàng
- **Credit management**: Quản lý công nợ
- **Customer loyalty**: Chương trình khách hàng thân thiết (tùy chọn)

### **6.2 Quản lý nhà cung cấp**
- **Supplier database**: Cơ sở dữ liệu nhà cung cấp
- **Supplier information**: Thông tin chi tiết nhà cung cấp
- **Purchase orders**: Đơn đặt hàng
- **Payment terms**: Điều khoản thanh toán
- **Supplier performance**: Đánh giá hiệu suất nhà cung cấp

### **6.3 Tính năng chung**
- **Partner types**: Khách hàng, nhà cung cấp, hoặc cả hai
- **Contact management**: Quản lý thông tin liên lạc
- **Transaction history**: Lịch sử giao dịch
- **Credit limits**: Hạn mức tín dụng
- **Partner reports**: Báo cáo đối tác

---

## 7. **📋 QUẢN LÝ HÓA ĐƠN (INVOICES)**

### **7.1 Tạo hóa đơn**
- **Auto-generation**: Tự động tạo từ đơn hàng
- **Manual creation**: Tạo hóa đơn thủ công
- **Invoice numbering**: Đánh số hóa đơn tự động
- **Template customization**: Tùy chỉnh mẫu hóa đơn
- **Multi-language**: Hóa đơn đa ngôn ngữ

### **7.2 Quản lý hóa đơn**
- **Invoice status**: Trạng thái hóa đơn (draft, sent, paid, overdue)
- **Payment tracking**: Theo dõi thanh toán
- **Invoice history**: Lịch sử hóa đơn
- **Search and filter**: Tìm kiếm và lọc hóa đơn
- **Bulk operations**: Xử lý hàng loạt

### **7.3 Xuất hóa đơn**
- **PDF export**: Xuất PDF
- **Email sending**: Gửi email hóa đơn
- **Print support**: In hóa đơn
- **Digital signature**: Chữ ký số (tùy chọn)
- **E-invoice**: Hóa đơn điện tử (tùy chọn)

---

## 8. **📊 BÁO CÁO (REPORTS)**

### **8.1 Báo cáo bán hàng**
- **Sales summary**: Tổng quan bán hàng
- **Sales by period**: Bán hàng theo thời gian
- **Sales by product**: Bán hàng theo sản phẩm
- **Sales by customer**: Bán hàng theo khách hàng
- **Sales trends**: Xu hướng bán hàng

### **8.2 Báo cáo tồn kho**
- **Stock levels**: Mức tồn kho
- **Stock movements**: Biến động tồn kho
- **Low stock report**: Báo cáo hàng sắp hết
- **Dead stock**: Hàng tồn kho chết
- **Inventory valuation**: Định giá tồn kho

### **8.3 Báo cáo tài chính**
- **Revenue reports**: Báo cáo doanh thu
- **Expense reports**: Báo cáo chi phí
- **Profit/Loss**: Lãi/lỗ
- **Cash flow**: Dòng tiền
- **Balance sheet**: Bảng cân đối kế toán (tùy chọn)

### **8.4 Xuất báo cáo**
- **PDF export**: Xuất PDF
- **Excel export**: Xuất Excel
- **Chart visualization**: Biểu đồ trực quan
- **Email reports**: Gửi báo cáo qua email
- **Scheduled reports**: Báo cáo định kỳ (tùy chọn)

---

## 9. **🔔 HỆ THỐNG THÔNG BÁO (NOTIFICATIONS)**

### **9.1 Thông báo trong app**
- **Real-time notifications**: Thông báo thời gian thực
- **Notification types**: Info, warning, error, success
- **Notification center**: Trung tâm thông báo
- **Mark as read**: Đánh dấu đã đọc
- **Notification history**: Lịch sử thông báo

### **9.2 Push notifications**
- **Mobile push**: Thông báo đẩy trên mobile
- **Order notifications**: Thông báo đơn hàng mới
- **Stock alerts**: Cảnh báo tồn kho
- **Payment reminders**: Nhắc nhở thanh toán
- **System updates**: Thông báo cập nhật hệ thống

### **9.3 Cài đặt thông báo**
- **Notification preferences**: Tùy chọn thông báo
- **Enable/disable**: Bật/tắt từng loại thông báo
- **Notification timing**: Thời gian thông báo
- **Sound settings**: Cài đặt âm thanh
- **Vibration settings**: Cài đặt rung

---

## 10. **🖨️ HỆ THỐNG IN ẤN (PRINTING)**

### **10.1 Thiết lập máy in**
- **Printer management**: Quản lý máy in
- **Network printers**: Máy in mạng LAN/WiFi
- **Printer configuration**: Cấu hình IP, port
- **Default printer**: Máy in mặc định
- **Connection testing**: Kiểm tra kết nối

### **10.2 In hóa đơn**
- **Invoice printing**: In hóa đơn sau thanh toán
- **PDF generation**: Tạo PDF trước khi in
- **Print preview**: Xem trước khi in
- **Print settings**: Cài đặt in (size, orientation)
- **Batch printing**: In hàng loạt

### **10.3 Quản lý in ấn**
- **Print queue**: Hàng đợi in
- **Print history**: Lịch sử in
- **Print status**: Trạng thái in
- **Error handling**: Xử lý lỗi in
- **Print templates**: Mẫu in tùy chỉnh

---

## 11. **⚙️ CÀI ĐẶT HỆ THỐNG (SETTINGS)**

### **11.1 Cài đặt ứng dụng**
- **Language selection**: Chọn ngôn ngữ (VI/EN/JP)
- **Theme settings**: Cài đặt giao diện
- **Currency settings**: Cài đặt tiền tệ
- **Date/time format**: Định dạng ngày/giờ
- **Number format**: Định dạng số

### **11.2 Cài đặt hóa đơn**
- **Invoice templates**: Mẫu hóa đơn
- **Company information**: Thông tin công ty
- **Logo upload**: Upload logo công ty
- **Invoice numbering**: Đánh số hóa đơn
- **Auto-show invoice**: Tự động hiển thị hóa đơn sau thanh toán

### **11.3 Cài đặt máy in**
- **Printer setup**: Thiết lập máy in
- **Print preferences**: Tùy chọn in
- **Paper size**: Kích thước giấy
- **Print quality**: Chất lượng in
- **Auto-print**: Tự động in

### **11.4 Cài đặt bảo mật**
- **Password change**: Đổi mật khẩu
- **Session timeout**: Thời gian hết phiên
- **Data backup**: Sao lưu dữ liệu
- **Data export**: Xuất dữ liệu
- **Privacy settings**: Cài đặt riêng tư

---

## 12. **🌐 ĐA NGÔN NGỮ (LOCALIZATION)**

### **12.1 Ngôn ngữ hỗ trợ**
- **Tiếng Việt (vi_VN)**: Ngôn ngữ chính
- **Tiếng Anh (en_US)**: Ngôn ngữ quốc tế
- **Tiếng Nhật (ja_JP)**: Mở rộng thị trường

### **12.2 Tính năng đa ngôn ngữ**
- **Dynamic language switching**: Chuyển ngôn ngữ động
- **Complete translation**: Dịch toàn bộ giao diện
- **Number formatting**: Định dạng số theo locale
- **Date formatting**: Định dạng ngày theo locale
- **Currency formatting**: Định dạng tiền tệ theo locale

### **12.3 Font support**
- **International fonts**: Font hỗ trợ ký tự quốc tế
- **Vietnamese fonts**: Font tiếng Việt
- **Japanese fonts**: Font tiếng Nhật
- **Fallback fonts**: Font dự phòng
- **Custom fonts**: Font tùy chỉnh

---

## 13. **📱 RESPONSIVE DESIGN**

### **13.1 Hỗ trợ thiết bị**
- **Mobile phones**: Điện thoại di động
- **Tablets**: Máy tính bảng
- **Desktop**: Máy tính để bàn
- **Web browsers**: Trình duyệt web
- **Cross-platform**: Đa nền tảng

### **13.2 Adaptive UI**
- **Breakpoint system**: Hệ thống breakpoint
- **Flexible layouts**: Layout linh hoạt
- **Responsive grids**: Lưới responsive
- **Adaptive navigation**: Navigation thích ứng
- **Touch-friendly**: Thân thiện với cảm ứng

### **13.3 Mobile optimizations**
- **Hamburger menu**: Menu hamburger trên mobile
- **Swipe gestures**: Cử chỉ vuốt
- **Pull-to-refresh**: Kéo để làm mới
- **Infinite scroll**: Cuộn vô hạn
- **Offline support**: Hỗ trợ offline

---

## 14. **🔒 BẢO MẬT VÀ QUYỀN RIÊNG TƯ**

### **14.1 Xác thực và phân quyền**
- **JWT authentication**: Xác thực JWT
- **Role-based access**: Phân quyền theo vai trò
- **Session management**: Quản lý phiên
- **Password security**: Bảo mật mật khẩu
- **Two-factor auth**: Xác thực 2 yếu tố (tùy chọn)

### **14.2 Bảo mật dữ liệu**
- **Data encryption**: Mã hóa dữ liệu
- **Secure transmission**: Truyền tải bảo mật
- **Row Level Security**: Bảo mật cấp dòng
- **Data isolation**: Cách ly dữ liệu multi-tenant
- **Audit logs**: Nhật ký kiểm toán

### **14.3 Quyền riêng tư**
- **Data privacy**: Quyền riêng tư dữ liệu
- **GDPR compliance**: Tuân thủ GDPR
- **Data retention**: Lưu trữ dữ liệu
- **Data deletion**: Xóa dữ liệu
- **Privacy policy**: Chính sách riêng tư

---

## 15. **⚡ HIỆU SUẤT VÀ TỐI ƯU**

### **15.1 Hiệu suất ứng dụng**
- **Fast loading**: Tải nhanh
- **Smooth animations**: Hoạt ảnh mượt
- **Memory optimization**: Tối ưu bộ nhớ
- **Battery optimization**: Tối ưu pin
- **Network optimization**: Tối ưu mạng

### **15.2 Caching và offline**
- **Local caching**: Cache local
- **Offline support**: Hỗ trợ offline
- **Data synchronization**: Đồng bộ dữ liệu
- **Background sync**: Đồng bộ nền
- **Conflict resolution**: Giải quyết xung đột

### **15.3 Database optimization**
- **Query optimization**: Tối ưu truy vấn
- **Indexing**: Đánh chỉ mục
- **Connection pooling**: Pool kết nối
- **Lazy loading**: Tải lazy
- **Pagination**: Phân trang

---

## 16. **🧪 TESTING VÀ QUALITY ASSURANCE**

### **16.1 Testing strategy**
- **Unit tests**: Test đơn vị
- **Widget tests**: Test widget
- **Integration tests**: Test tích hợp
- **End-to-end tests**: Test đầu cuối
- **Performance tests**: Test hiệu suất

### **16.2 Code quality**
- **Code analysis**: Phân tích code
- **Linting**: Kiểm tra lint
- **Code coverage**: Độ bao phủ code
- **Documentation**: Tài liệu hóa
- **Code review**: Review code

### **16.3 Error handling**
- **Exception handling**: Xử lý ngoại lệ
- **Error logging**: Ghi log lỗi
- **Crash reporting**: Báo cáo crash
- **User feedback**: Phản hồi người dùng
- **Error recovery**: Khôi phục lỗi

---

## 17. **🚀 DEPLOYMENT VÀ DEVOPS**

### **17.1 Build và deployment**
- **Automated builds**: Build tự động
- **CI/CD pipeline**: Pipeline CI/CD
- **Environment management**: Quản lý môi trường
- **Version control**: Kiểm soát phiên bản
- **Release management**: Quản lý phát hành

### **17.2 Monitoring và logging**
- **Application monitoring**: Giám sát ứng dụng
- **Performance monitoring**: Giám sát hiệu suất
- **Error tracking**: Theo dõi lỗi
- **User analytics**: Phân tích người dùng
- **Business metrics**: Metrics kinh doanh

### **17.3 Maintenance**
- **Regular updates**: Cập nhật định kỳ
- **Security patches**: Vá bảo mật
- **Bug fixes**: Sửa lỗi
- **Feature updates**: Cập nhật tính năng
- **Database maintenance**: Bảo trì database

---

## 📋 **KẾT LUẬN**

Ứng dụng City POS là một hệ thống quản lý bán hàng toàn diện, được thiết kế để đáp ứng nhu cầu của các doanh nghiệp từ nhỏ đến vừa. Với kiến trúc hiện đại, giao diện thân thiện và tính năng đa dạng, ứng dụng cung cấp giải pháp hoàn chỉnh cho việc quản lý bán hàng, tồn kho, tài chính và báo cáo.

### **Điểm mạnh chính**:
- **Đa nền tảng**: Hoạt động trên mobile, tablet, web
- **Đa ngôn ngữ**: Hỗ trợ Việt, Anh, Nhật
- **Responsive**: Giao diện thích ứng mọi thiết bị
- **Realtime**: Cập nhật dữ liệu thời gian thực
- **Bảo mật**: Hệ thống bảo mật đa lớp
- **Mở rộng**: Kiến trúc dễ mở rộng và bảo trì

Ứng dụng được xây dựng với công nghệ hiện đại (Flutter + Supabase) đảm bảo hiệu suất cao, bảo mật tốt và khả năng mở rộng trong tương lai.
