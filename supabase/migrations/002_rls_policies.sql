-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.partners ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cashbook_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stock_transactions ENABLE ROW LEVEL SECURITY;

-- Helper function to check if user is admin
CREATE OR R<PERSON>LACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() AND role = 'admin' AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is manager or admin
CREATE OR REPLACE FUNCTION is_manager_or_admin()
<PERSON><PERSON><PERSON>NS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() AND role IN ('admin', 'manager') AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is active
CREATE OR REPLACE FUNCTION is_active_user()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON public.users
    FOR SELECT USING (is_admin());

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can update all users" ON public.users
    FOR UPDATE USING (is_admin());

CREATE POLICY "Admins can insert users" ON public.users
    FOR INSERT WITH CHECK (is_admin());

-- Categories table policies
CREATE POLICY "Active users can view categories" ON public.categories
    FOR SELECT USING (is_active_user());

CREATE POLICY "Managers can manage categories" ON public.categories
    FOR ALL USING (is_manager_or_admin());

-- Products table policies
CREATE POLICY "Active users can view products" ON public.products
    FOR SELECT USING (is_active_user());

CREATE POLICY "Active users can insert products" ON public.products
    FOR INSERT WITH CHECK (is_active_user());

CREATE POLICY "Active users can update products" ON public.products
    FOR UPDATE USING (is_active_user());

CREATE POLICY "Managers can delete products" ON public.products
    FOR DELETE USING (is_manager_or_admin());

-- Partners table policies
CREATE POLICY "Active users can view partners" ON public.partners
    FOR SELECT USING (is_active_user());

CREATE POLICY "Active users can insert partners" ON public.partners
    FOR INSERT WITH CHECK (is_active_user());

CREATE POLICY "Active users can update partners" ON public.partners
    FOR UPDATE USING (is_active_user());

CREATE POLICY "Managers can delete partners" ON public.partners
    FOR DELETE USING (is_manager_or_admin());

-- Invoices table policies
CREATE POLICY "Active users can view invoices" ON public.invoices
    FOR SELECT USING (is_active_user());

CREATE POLICY "Active users can insert invoices" ON public.invoices
    FOR INSERT WITH CHECK (is_active_user());

CREATE POLICY "Active users can update invoices" ON public.invoices
    FOR UPDATE USING (is_active_user());

CREATE POLICY "Managers can delete invoices" ON public.invoices
    FOR DELETE USING (is_manager_or_admin());

-- Invoice items table policies
CREATE POLICY "Active users can view invoice items" ON public.invoice_items
    FOR SELECT USING (is_active_user());

CREATE POLICY "Active users can manage invoice items" ON public.invoice_items
    FOR ALL USING (is_active_user());

-- Cashbook entries table policies
CREATE POLICY "Active users can view cashbook entries" ON public.cashbook_entries
    FOR SELECT USING (is_active_user());

CREATE POLICY "Active users can insert cashbook entries" ON public.cashbook_entries
    FOR INSERT WITH CHECK (is_active_user());

CREATE POLICY "Active users can update cashbook entries" ON public.cashbook_entries
    FOR UPDATE USING (is_active_user());

CREATE POLICY "Managers can delete cashbook entries" ON public.cashbook_entries
    FOR DELETE USING (is_manager_or_admin());

-- Stock transactions table policies
CREATE POLICY "Active users can view stock transactions" ON public.stock_transactions
    FOR SELECT USING (is_active_user());

CREATE POLICY "Active users can insert stock transactions" ON public.stock_transactions
    FOR INSERT WITH CHECK (is_active_user());

CREATE POLICY "Managers can update stock transactions" ON public.stock_transactions
    FOR UPDATE USING (is_manager_or_admin());

CREATE POLICY "Managers can delete stock transactions" ON public.stock_transactions
    FOR DELETE USING (is_manager_or_admin());
