-- Insert sample categories
INSERT INTO public.categories (id, name, description, color) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', '<PERSON><PERSON><PERSON><PERSON> tử', '<PERSON><PERSON><PERSON><PERSON> bị điện tử', '#2196F3'),
    ('550e8400-e29b-41d4-a716-446655440002', 'Thời trang', '<PERSON><PERSON><PERSON><PERSON>o, phụ kiện', '#E91E63'),
    ('550e8400-e29b-41d4-a716-446655440003', '<PERSON>ia dụng', '<PERSON><PERSON> gia dụng', '#4CAF50'),
    ('550e8400-e29b-41d4-a716-446655440004', '<PERSON>h<PERSON><PERSON> phẩm', '<PERSON><PERSON><PERSON><PERSON> ph<PERSON>m và đồ uống', '#FF9800'),
    ('550e8400-e29b-41d4-a716-446655440005', 'Sách', '<PERSON><PERSON><PERSON> và văn phòng phẩm', '#9C27B0');

-- Insert sample products
INSERT INTO public.products (id, name, code, description, category_id, unit, cost_price, selling_price, stock_quantity, min_stock_level) VALUES
    ('660e8400-e29b-41d4-a716-446655440001', 'iPhone 15 Pro', 'IP15P', 'iPhone 15 Pro 128GB', '550e8400-e29b-41d4-a716-446655440001', 'chiếc', 25000000, 28000000, 10, 5),
    ('660e8400-e29b-41d4-a716-446655440002', 'Samsung Galaxy S24', 'SGS24', 'Samsung Galaxy S24 256GB', '550e8400-e29b-41d4-a716-446655440001', 'chiếc', 18000000, 20000000, 15, 5),
    ('660e8400-e29b-41d4-a716-446655440003', 'MacBook Air M3', 'MBA-M3', 'MacBook Air M3 13 inch', '550e8400-e29b-41d4-a716-446655440001', 'chiếc', 28000000, 32000000, 8, 3),
    ('660e8400-e29b-41d4-a716-446655440004', 'Áo thun nam', 'ATN001', 'Áo thun cotton nam', '550e8400-e29b-41d4-a716-446655440002', 'chiếc', 80000, 150000, 50, 20),
    ('660e8400-e29b-41d4-a716-446655440005', 'Quần jeans nữ', 'QJN001', 'Quần jeans nữ skinny', '550e8400-e29b-41d4-a716-446655440002', 'chiếc', 200000, 350000, 30, 15),
    ('660e8400-e29b-41d4-a716-446655440006', 'Nồi cơm điện', 'NCD001', 'Nồi cơm điện 1.8L', '550e8400-e29b-41d4-a716-446655440003', 'chiếc', 800000, 1200000, 25, 10),
    ('660e8400-e29b-41d4-a716-446655440007', 'Bàn ủi hơi nước', 'BU001', 'Bàn ủi hơi nước Philips', '550e8400-e29b-41d4-a716-446655440003', 'chiếc', 600000, 900000, 20, 8),
    ('660e8400-e29b-41d4-a716-446655440008', 'Cà phê rang xay', 'CF001', 'Cà phê Arabica rang xay 500g', '550e8400-e29b-41d4-a716-446655440004', 'gói', 120000, 180000, 100, 50),
    ('660e8400-e29b-41d4-a716-446655440009', 'Trà xanh', 'TX001', 'Trà xanh Thái Nguyên 200g', '550e8400-e29b-41d4-a716-446655440004', 'hộp', 80000, 120000, 80, 30),
    ('660e8400-e29b-41d4-a716-446655440010', 'Sách lập trình', 'SLT001', 'Clean Code - Robert Martin', '550e8400-e29b-41d4-a716-446655440005', 'cuốn', 200000, 300000, 40, 20);

-- Insert sample partners
INSERT INTO public.partners (id, name, code, type, email, phone, address, city, district, ward, credit_limit, current_debt) VALUES
    ('770e8400-e29b-41d4-a716-446655440001', 'Nguyễn Văn An', 'KH001', 'customer', '<EMAIL>', '0901234567', '123 Đường ABC', 'Hồ Chí Minh', 'Quận 1', 'Phường Bến Nghé', 10000000, 2500000),
    ('770e8400-e29b-41d4-a716-446655440002', 'Trần Thị Bình', 'KH002', 'customer', '<EMAIL>', '0912345678', '456 Đường XYZ', 'Hà Nội', 'Quận Ba Đình', 'Phường Điện Biên', 5000000, 0),
    ('770e8400-e29b-41d4-a716-446655440003', 'Công ty TNHH ABC', 'NCC001', 'supplier', '<EMAIL>', '0281234567', '789 Đường DEF', 'Hồ Chí Minh', 'Quận 3', 'Phường 1', 0, 0),
    ('770e8400-e29b-41d4-a716-446655440004', 'Nhà cung cấp XYZ', 'NCC002', 'supplier', '<EMAIL>', '0291234567', '321 Đường GHI', 'Đà Nẵng', 'Quận Hải Châu', 'Phường Hải Châu 1', 0, 0),
    ('770e8400-e29b-41d4-a716-446655440005', 'Doanh nghiệp DEF', 'DN001', 'both', '<EMAIL>', '0301234567', '654 Đường JKL', 'Cần Thơ', 'Quận Ninh Kiều', 'Phường An Cư', 15000000, 5000000),
    ('770e8400-e29b-41d4-a716-446655440006', 'Võ Thị Giang', 'KH003', 'customer', '<EMAIL>', '0956789012', '987 Đường MNO', 'Hồ Chí Minh', 'Quận 7', 'Phường Tân Phú', 3000000, 1200000);

-- Insert sample invoices
INSERT INTO public.invoices (id, invoice_number, partner_id, partner_name, invoice_date, subtotal, discount_amount, total_amount, paid_amount, status) VALUES
    ('880e8400-e29b-41d4-a716-446655440001', 'INV-2024-001', '770e8400-e29b-41d4-a716-446655440001', 'Nguyễn Văn An', '2024-01-15 10:30:00+07', 28000000, 0, 28000000, 25500000, 'sent'),
    ('880e8400-e29b-41d4-a716-446655440002', 'INV-2024-002', '770e8400-e29b-41d4-a716-446655440002', 'Trần Thị Bình', '2024-01-20 14:15:00+07', 40000000, 2000000, 38000000, 38000000, 'paid'),
    ('880e8400-e29b-41d4-a716-446655440003', 'INV-2024-003', '770e8400-e29b-41d4-a716-446655440005', 'Doanh nghiệp DEF', '2024-01-25 09:45:00+07', 1500000, 150000, 1350000, 0, 'draft'),
    ('880e8400-e29b-41d4-a716-446655440004', 'INV-2024-004', '770e8400-e29b-41d4-a716-446655440001', 'Nguyễn Văn An', '2024-02-01 16:20:00+07', 2400000, 0, 2400000, 2400000, 'paid'),
    ('880e8400-e29b-41d4-a716-446655440005', 'INV-2024-005', '770e8400-e29b-41d4-a716-446655440006', 'Võ Thị Giang', '2024-02-05 11:10:00+07', 1800000, 180000, 1620000, 420000, 'sent');

-- Insert sample invoice items
INSERT INTO public.invoice_items (invoice_id, product_id, product_name, quantity, unit_price, total_price) VALUES
    ('880e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'iPhone 15 Pro', 1, 28000000, 28000000),
    ('880e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440002', 'Samsung Galaxy S24', 2, 20000000, 40000000),
    ('880e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440004', 'Áo thun nam', 10, 150000, 1500000),
    ('880e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440006', 'Nồi cơm điện', 2, 1200000, 2400000),
    ('880e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440008', 'Cà phê rang xay', 10, 180000, 1800000);

-- Insert sample cashbook entries
INSERT INTO public.cashbook_entries (id, entry_number, type, category, amount, description, partner_id, partner_name, entry_date) VALUES
    ('990e8400-e29b-41d4-a716-446655440001', 'THU-2024-001', 'receipt', 'Bán hàng', 25500000, 'Thu tiền bán iPhone 15 Pro', '770e8400-e29b-41d4-a716-446655440001', 'Nguyễn Văn An', '2024-01-15 11:00:00+07'),
    ('990e8400-e29b-41d4-a716-446655440002', 'THU-2024-002', 'receipt', 'Bán hàng', 38000000, 'Thu tiền bán Samsung Galaxy S24', '770e8400-e29b-41d4-a716-446655440002', 'Trần Thị Bình', '2024-01-20 15:00:00+07'),
    ('990e8400-e29b-41d4-a716-446655440003', 'CHI-2024-001', 'payment', 'Nhập hàng', 50000000, 'Chi tiền nhập hàng điện tử', '770e8400-e29b-41d4-a716-446655440003', 'Công ty TNHH ABC', '2024-01-22 10:30:00+07'),
    ('990e8400-e29b-41d4-a716-446655440004', 'THU-2024-003', 'receipt', 'Bán hàng', 2400000, 'Thu tiền bán nồi cơm điện', '770e8400-e29b-41d4-a716-446655440001', 'Nguyễn Văn An', '2024-02-01 16:30:00+07'),
    ('990e8400-e29b-41d4-a716-446655440005', 'CHI-2024-002', 'payment', 'Vận hành', 5000000, 'Chi phí thuê mặt bằng tháng 2', NULL, NULL, '2024-02-01 09:00:00+07'),
    ('990e8400-e29b-41d4-a716-446655440006', 'THU-2024-004', 'receipt', 'Bán hàng', 420000, 'Thu tiền bán cà phê (trả trước)', '770e8400-e29b-41d4-a716-446655440006', 'Võ Thị Giang', '2024-02-05 11:30:00+07');

-- Insert sample stock transactions
INSERT INTO public.stock_transactions (id, transaction_number, type, product_id, product_name, quantity, unit_cost, total_cost, reference_type, note, transaction_date) VALUES
    ('aa0e8400-e29b-41d4-a716-446655440001', 'NK-2024-001', 'in', '660e8400-e29b-41d4-a716-446655440001', 'iPhone 15 Pro', 20, 25000000, 500000000, 'purchase', 'Nhập kho iPhone 15 Pro', '2024-01-10 08:00:00+07'),
    ('aa0e8400-e29b-41d4-a716-446655440002', 'XK-2024-001', 'out', '660e8400-e29b-41d4-a716-446655440001', 'iPhone 15 Pro', 1, 25000000, 25000000, 'invoice', 'Xuất kho bán hàng', '2024-01-15 10:30:00+07'),
    ('aa0e8400-e29b-41d4-a716-446655440003', 'NK-2024-002', 'in', '660e8400-e29b-41d4-a716-446655440002', 'Samsung Galaxy S24', 30, 18000000, 540000000, 'purchase', 'Nhập kho Samsung Galaxy S24', '2024-01-12 09:30:00+07'),
    ('aa0e8400-e29b-41d4-a716-446655440004', 'XK-2024-002', 'out', '660e8400-e29b-41d4-a716-446655440002', 'Samsung Galaxy S24', 2, 18000000, 36000000, 'invoice', 'Xuất kho bán hàng', '2024-01-20 14:15:00+07'),
    ('aa0e8400-e29b-41d4-a716-446655440005', 'NK-2024-003', 'in', '660e8400-e29b-41d4-a716-446655440004', 'Áo thun nam', 100, 80000, 8000000, 'purchase', 'Nhập kho áo thun nam', '2024-01-18 14:00:00+07'),
    ('aa0e8400-e29b-41d4-a716-446655440006', 'XK-2024-003', 'out', '660e8400-e29b-41d4-a716-446655440004', 'Áo thun nam', 10, 80000, 800000, 'invoice', 'Xuất kho bán hàng', '2024-01-25 09:45:00+07');
