name: city_pos
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # Backend & Database
  supabase_flutter: ^2.5.6

  # Navigation
  go_router: ^14.2.7

  # Environment & Configuration
  flutter_dotenv: ^5.1.0

  # Localization
  flutter_localizations:
    sdk: flutter

  # Storage
  flutter_secure_storage: ^9.2.2
  shared_preferences: ^2.5.3

  # Networking
  dio: ^5.4.3+1
  connectivity_plus: ^6.0.3

  # Utils
  intl: ^0.20.2
  uuid: ^4.4.0

  # Charts & Reports
  fl_chart: ^0.68.0
  printing: ^5.12.0
  excel: ^4.0.3

  # Barcode & QR
  qr_flutter: ^4.1.0
  mobile_scanner: ^7.0.0

  # Image & File
  image_picker: ^1.1.2
  file_picker: ^8.0.6

  # Local Database (for offline)
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Validation
  formz: ^0.7.0

  # Loading & UI
  loading_animation_widget: ^1.2.1
  shimmer: ^3.0.0

  # Date & Time
  table_calendar: ^3.1.2

  # JSON Serialization
  json_annotation: ^4.9.0

  # Push Notifications
  flutter_local_notifications: ^17.2.2
  permission_handler: ^11.3.1

  # Sharing & PDF
  share_plus: ^10.1.2
  path_provider: ^2.1.4
  pdf: ^3.11.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting & Code Quality
  flutter_lints: ^5.0.0
  very_good_analysis: ^6.0.0

  # Code Generation
  build_runner: ^2.4.9
  riverpod_generator: ^2.4.0
  hive_generator: ^2.0.1
  json_serializable: ^6.8.0
  flutter_gen_runner: ^5.4.0

  # Testing
  mocktail: ^1.0.3
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/translations/
    - assets/fonts/
    - .env

  # Generate localization
  generate: true

  # Fonts
  fonts:
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package