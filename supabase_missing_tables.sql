-- =====================================================
-- MISSING TABLES FOR CITY POS APPLICATION
-- =====================================================

-- 1. INVOICES TABLE
-- For storing invoices created from POS orders
CREATE TABLE IF NOT EXISTS invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for invoices
CREATE INDEX IF NOT EXISTS idx_invoices_order_id ON invoices(order_id);
CREATE INDEX IF NOT EXISTS idx_invoices_invoice_number ON invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_created_at ON invoices(created_at);

-- 2. CASH_FLOW TABLE  
-- For storing finance transactions (receipts/expenses)
CREATE TABLE IF NOT EXISTS cash_flow (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(20) NOT NULL, -- 'income' or 'expense'
    category VARCHAR(50) NOT NULL, -- 'sales', 'purchase', 'other'
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    payment_method VARCHAR(20), -- 'cash', 'card', 'transfer', 'other'
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for cash_flow
CREATE INDEX IF NOT EXISTS idx_cash_flow_type ON cash_flow(type);
CREATE INDEX IF NOT EXISTS idx_cash_flow_category ON cash_flow(category);
CREATE INDEX IF NOT EXISTS idx_cash_flow_order_id ON cash_flow(order_id);
CREATE INDEX IF NOT EXISTS idx_cash_flow_created_at ON cash_flow(created_at);

-- 3. UPDATE TRIGGERS FOR UPDATED_AT
-- Auto-update updated_at timestamp

-- Trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to invoices
DROP TRIGGER IF EXISTS update_invoices_updated_at ON invoices;
CREATE TRIGGER update_invoices_updated_at
    BEFORE UPDATE ON invoices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Apply triggers to cash_flow
DROP TRIGGER IF EXISTS update_cash_flow_updated_at ON cash_flow;
CREATE TRIGGER update_cash_flow_updated_at
    BEFORE UPDATE ON cash_flow
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 4. ROW LEVEL SECURITY (RLS)
-- Enable RLS for security

ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE cash_flow ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users
CREATE POLICY "Allow all operations for authenticated users" ON invoices
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON cash_flow
    FOR ALL USING (auth.role() = 'authenticated');

-- 5. SAMPLE DATA (OPTIONAL)
-- Insert some sample data for testing

-- Sample invoice
INSERT INTO invoices (invoice_number, subtotal, tax_amount, discount_amount, total_amount, status, notes)
VALUES ('INV20250602-001', 10000, 1000, 0, 11000, 'paid', 'Sample invoice from POS')
ON CONFLICT (invoice_number) DO NOTHING;

-- Sample cash flow entries
INSERT INTO cash_flow (type, category, amount, description, payment_method)
VALUES 
    ('income', 'sales', 11000, 'Thu từ bán hàng POS', 'cash'),
    ('expense', 'purchase', 5000, 'Mua hàng nhập kho', 'transfer')
ON CONFLICT DO NOTHING;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if tables were created successfully
SELECT 'invoices' as table_name, COUNT(*) as record_count FROM invoices
UNION ALL
SELECT 'cash_flow' as table_name, COUNT(*) as record_count FROM cash_flow;

-- Check table structures
SELECT table_name, column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name IN ('invoices', 'cash_flow')
ORDER BY table_name, ordinal_position;
