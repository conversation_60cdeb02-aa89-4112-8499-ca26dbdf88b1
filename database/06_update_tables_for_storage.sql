-- =====================================================
-- CITY POS - UPDATE EXISTING TABLES FOR STORAGE INTEGRATION
-- =====================================================
-- This file updates existing tables to use storage_files instead of direct URLs
-- Run this script after 05_storage_files.sql

-- =====================================================
-- UPDATE USERS TABLE
-- =====================================================

-- Add foreign key to storage_files for avatar
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS avatar_file_id UUID REFERENCES public.storage_files(id) ON DELETE SET NULL;

-- Create index for avatar_file_id
CREATE INDEX IF NOT EXISTS idx_users_avatar_file ON public.users(avatar_file_id);

-- =====================================================
-- UPDATE PRODUCTS TABLE  
-- =====================================================

-- Add foreign key to storage_files for product image
ALTER TABLE public.products 
ADD COLUMN IF NOT EXISTS image_file_id UUID REFERENCES public.storage_files(id) ON DELETE SET NULL;

-- Create index for image_file_id
CREATE INDEX IF NOT EXISTS idx_products_image_file ON public.products(image_file_id);

-- =====================================================
-- CREATE VIEWS FOR BACKWARD COMPATIBILITY
-- =====================================================

-- View for users with avatar URLs (backward compatibility)
CREATE OR REPLACE VIEW public.users_with_avatar AS
SELECT 
  u.*,
  CASE 
    WHEN u.avatar_file_id IS NOT NULL THEN get_file_url(u.avatar_file_id)
    ELSE u.avatar_url
  END as avatar_url_computed,
  sf.file_name as avatar_file_name,
  sf.file_size as avatar_file_size,
  sf.mime_type as avatar_mime_type
FROM public.users u
LEFT JOIN public.storage_files sf ON u.avatar_file_id = sf.id;

-- View for products with image URLs (backward compatibility)  
CREATE OR REPLACE VIEW public.products_with_images AS
SELECT 
  p.*,
  CASE 
    WHEN p.image_file_id IS NOT NULL THEN get_file_url(p.image_file_id)
    ELSE p.image_url
  END as image_url_computed,
  sf.file_name as image_file_name,
  sf.file_size as image_file_size,
  sf.mime_type as image_mime_type,
  sf.width as image_width,
  sf.height as image_height,
  sf.alt_text as image_alt_text
FROM public.products p
LEFT JOIN public.storage_files sf ON p.image_file_id = sf.id;

-- =====================================================
-- HELPER FUNCTIONS FOR FILE MANAGEMENT
-- =====================================================

-- Function to set user avatar
CREATE OR REPLACE FUNCTION set_user_avatar(
  p_user_id UUID,
  p_file_name TEXT,
  p_file_size BIGINT DEFAULT NULL,
  p_mime_type TEXT DEFAULT 'image/jpeg',
  p_width INTEGER DEFAULT NULL,
  p_height INTEGER DEFAULT NULL,
  p_alt_text TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  file_id UUID;
  file_extension TEXT;
BEGIN
  -- Extract file extension
  file_extension := '.' || split_part(p_file_name, '.', array_length(string_to_array(p_file_name, '.'), 1));
  
  -- Create storage file record
  INSERT INTO public.storage_files (
    file_name, file_type, file_category, mime_type, file_size,
    file_extension, width, height, entity_type, entity_id,
    is_public, alt_text, uploaded_by
  ) VALUES (
    p_file_name, 'image', 'avatar', p_mime_type, p_file_size,
    file_extension, p_width, p_height, 'user', p_user_id,
    true, p_alt_text, p_user_id
  ) RETURNING id INTO file_id;
  
  -- Update user avatar_file_id
  UPDATE public.users 
  SET avatar_file_id = file_id 
  WHERE id = p_user_id;
  
  RETURN file_id;
END;
$$ language 'plpgsql';

-- Function to set product image
CREATE OR REPLACE FUNCTION set_product_image(
  p_product_id UUID,
  p_file_name TEXT,
  p_file_size BIGINT DEFAULT NULL,
  p_mime_type TEXT DEFAULT 'image/jpeg',
  p_width INTEGER DEFAULT NULL,
  p_height INTEGER DEFAULT NULL,
  p_alt_text TEXT DEFAULT NULL,
  p_uploaded_by UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  file_id UUID;
  file_extension TEXT;
  uploader_id UUID;
BEGIN
  -- Get uploader ID
  uploader_id := COALESCE(p_uploaded_by, auth.uid());
  
  -- Extract file extension
  file_extension := '.' || split_part(p_file_name, '.', array_length(string_to_array(p_file_name, '.'), 1));
  
  -- Create storage file record
  INSERT INTO public.storage_files (
    file_name, file_type, file_category, mime_type, file_size,
    file_extension, width, height, entity_type, entity_id,
    is_public, alt_text, uploaded_by
  ) VALUES (
    p_file_name, 'image', 'product', p_mime_type, p_file_size,
    file_extension, p_width, p_height, 'product', p_product_id,
    true, p_alt_text, uploader_id
  ) RETURNING id INTO file_id;
  
  -- Update product image_file_id
  UPDATE public.products 
  SET image_file_id = file_id 
  WHERE id = p_product_id;
  
  RETURN file_id;
END;
$$ language 'plpgsql';

-- Function to remove user avatar
CREATE OR REPLACE FUNCTION remove_user_avatar(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  old_file_id UUID;
BEGIN
  -- Get current avatar file ID
  SELECT avatar_file_id INTO old_file_id 
  FROM public.users 
  WHERE id = p_user_id;
  
  -- Remove reference from user
  UPDATE public.users 
  SET avatar_file_id = NULL 
  WHERE id = p_user_id;
  
  -- Delete storage file record
  IF old_file_id IS NOT NULL THEN
    DELETE FROM public.storage_files 
    WHERE id = old_file_id;
  END IF;
  
  RETURN true;
END;
$$ language 'plpgsql';

-- Function to remove product image
CREATE OR REPLACE FUNCTION remove_product_image(p_product_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  old_file_id UUID;
BEGIN
  -- Get current image file ID
  SELECT image_file_id INTO old_file_id 
  FROM public.products 
  WHERE id = p_product_id;
  
  -- Remove reference from product
  UPDATE public.products 
  SET image_file_id = NULL 
  WHERE id = p_product_id;
  
  -- Delete storage file record
  IF old_file_id IS NOT NULL THEN
    DELETE FROM public.storage_files 
    WHERE id = old_file_id;
  END IF;
  
  RETURN true;
END;
$$ language 'plpgsql';

-- =====================================================
-- MIGRATION FUNCTIONS
-- =====================================================

-- Function to migrate existing avatar URLs to storage_files
CREATE OR REPLACE FUNCTION migrate_avatar_urls()
RETURNS INTEGER AS $$
DECLARE
  user_record RECORD;
  migrated_count INTEGER := 0;
  file_id UUID;
BEGIN
  FOR user_record IN 
    SELECT id, avatar_url, full_name 
    FROM public.users 
    WHERE avatar_url IS NOT NULL 
      AND avatar_url != ''
      AND avatar_file_id IS NULL
  LOOP
    -- Create storage file record for existing URL
    INSERT INTO public.storage_files (
      file_name, file_type, file_category, mime_type,
      entity_type, entity_id, is_public, access_url,
      alt_text, uploaded_by
    ) VALUES (
      'migrated-avatar-' || user_record.id || '.jpg',
      'image', 'avatar', 'image/jpeg',
      'user', user_record.id, true, user_record.avatar_url,
      'Avatar for ' || user_record.full_name,
      user_record.id
    ) RETURNING id INTO file_id;
    
    -- Update user with new file_id
    UPDATE public.users 
    SET avatar_file_id = file_id 
    WHERE id = user_record.id;
    
    migrated_count := migrated_count + 1;
  END LOOP;
  
  RETURN migrated_count;
END;
$$ language 'plpgsql';

-- Function to migrate existing product image URLs to storage_files
CREATE OR REPLACE FUNCTION migrate_product_image_urls()
RETURNS INTEGER AS $$
DECLARE
  product_record RECORD;
  migrated_count INTEGER := 0;
  file_id UUID;
BEGIN
  FOR product_record IN 
    SELECT id, image_url, name, created_by 
    FROM public.products 
    WHERE image_url IS NOT NULL 
      AND image_url != ''
      AND image_file_id IS NULL
  LOOP
    -- Create storage file record for existing URL
    INSERT INTO public.storage_files (
      file_name, file_type, file_category, mime_type,
      entity_type, entity_id, is_public, access_url,
      alt_text, uploaded_by
    ) VALUES (
      'migrated-product-' || product_record.id || '.jpg',
      'image', 'product', 'image/jpeg',
      'product', product_record.id, true, product_record.image_url,
      'Image for ' || product_record.name,
      COALESCE(product_record.created_by, (SELECT id FROM public.users LIMIT 1))
    ) RETURNING id INTO file_id;
    
    -- Update product with new file_id
    UPDATE public.products 
    SET image_file_id = file_id 
    WHERE id = product_record.id;
    
    migrated_count := migrated_count + 1;
  END LOOP;
  
  RETURN migrated_count;
END;
$$ language 'plpgsql';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check migration status
SELECT 
  'Users with avatar_url but no avatar_file_id' as description,
  COUNT(*) as count
FROM public.users 
WHERE avatar_url IS NOT NULL AND avatar_file_id IS NULL

UNION ALL

SELECT 
  'Products with image_url but no image_file_id' as description,
  COUNT(*) as count
FROM public.products 
WHERE image_url IS NOT NULL AND image_file_id IS NULL

UNION ALL

SELECT 
  'Total storage files' as description,
  COUNT(*) as count
FROM public.storage_files;
