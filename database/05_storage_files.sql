-- =====================================================
-- CITY POS - STORAGE FILES TABLE
-- =====================================================
-- This file creates storage_files table for managing images and files
-- with Supabase Storage integration
-- Run this script after the main schema setup

-- Create custom types for storage
CREATE TYPE file_type AS ENUM ('image', 'document', 'video', 'audio', 'other');
CREATE TYPE file_category AS ENUM ('avatar', 'product', 'invoice', 'receipt', 'document', 'other');

-- =====================================================
-- STORAGE FILES TABLE
-- =====================================================

-- Storage files table for managing uploaded files
CREATE TABLE IF NOT EXISTS public.storage_files (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- File identification
  file_name VARCHAR(255) NOT NULL, -- Original file name
  file_path TEXT NOT NULL, -- Full path in Supabase Storage
  bucket_name VARCHAR(100) NOT NULL DEFAULT 'city-pos', -- Storage bucket name
  
  -- File metadata
  file_type file_type NOT NULL,
  file_category file_category NOT NULL,
  mime_type VARCHAR(100), -- image/jpeg, image/png, etc.
  file_size BIGINT, -- File size in bytes
  file_extension VARCHAR(10), -- .jpg, .png, .pdf, etc.
  
  -- Dimensions for images
  width INTEGER, -- Image width in pixels
  height INTEGER, -- Image height in pixels
  
  -- Relationships
  entity_type VARCHAR(50), -- 'user', 'product', 'invoice', etc.
  entity_id UUID, -- ID of the related entity
  
  -- Access control
  is_public BOOLEAN DEFAULT false, -- Public access or private
  access_url TEXT, -- Public URL if is_public = true
  
  -- Additional metadata
  alt_text TEXT, -- Alt text for images (accessibility)
  description TEXT, -- File description
  tags TEXT[], -- Array of tags for searching
  
  -- Audit fields
  uploaded_by UUID REFERENCES public.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR STORAGE FILES
-- =====================================================

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_storage_files_entity ON public.storage_files(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_storage_files_bucket ON public.storage_files(bucket_name);
CREATE INDEX IF NOT EXISTS idx_storage_files_type ON public.storage_files(file_type);
CREATE INDEX IF NOT EXISTS idx_storage_files_category ON public.storage_files(file_category);
CREATE INDEX IF NOT EXISTS idx_storage_files_uploader ON public.storage_files(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_storage_files_created ON public.storage_files(created_at);
CREATE INDEX IF NOT EXISTS idx_storage_files_path ON public.storage_files(file_path);

-- Full-text search index for file names and descriptions
CREATE INDEX IF NOT EXISTS idx_storage_files_search ON public.storage_files 
USING gin(to_tsvector('english', file_name || ' ' || COALESCE(description, '')));

-- =====================================================
-- RLS POLICIES FOR STORAGE FILES
-- =====================================================

-- Enable RLS
ALTER TABLE public.storage_files ENABLE ROW LEVEL SECURITY;

-- Users can view files they uploaded
CREATE POLICY "Users can view own files" ON public.storage_files
  FOR SELECT USING (uploaded_by = auth.uid());

-- Users can view public files
CREATE POLICY "Users can view public files" ON public.storage_files
  FOR SELECT USING (is_public = true);

-- Users can upload files
CREATE POLICY "Users can upload files" ON public.storage_files
  FOR INSERT WITH CHECK (uploaded_by = auth.uid());

-- Users can update own files
CREATE POLICY "Users can update own files" ON public.storage_files
  FOR UPDATE USING (uploaded_by = auth.uid());

-- Users can delete own files
CREATE POLICY "Users can delete own files" ON public.storage_files
  FOR DELETE USING (uploaded_by = auth.uid());

-- Admins can manage all files
CREATE POLICY "Admins can manage all files" ON public.storage_files
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- =====================================================
-- TRIGGERS FOR STORAGE FILES
-- =====================================================

-- Add updated_at trigger
CREATE TRIGGER update_storage_files_updated_at 
  BEFORE UPDATE ON public.storage_files
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to generate file path
CREATE OR REPLACE FUNCTION generate_file_path()
RETURNS TRIGGER AS $$
BEGIN
  -- Generate organized file path: bucket/category/year/month/uuid.extension
  IF NEW.file_path IS NULL OR NEW.file_path = '' THEN
    NEW.file_path := NEW.bucket_name || '/' || 
                     NEW.file_category || '/' ||
                     EXTRACT(YEAR FROM NOW()) || '/' ||
                     LPAD(EXTRACT(MONTH FROM NOW())::TEXT, 2, '0') || '/' ||
                     NEW.id || COALESCE(NEW.file_extension, '');
  END IF;
  
  -- Generate access URL for public files
  IF NEW.is_public = true THEN
    NEW.access_url := 'https://your-project.supabase.co/storage/v1/object/public/' || NEW.file_path;
  ELSE
    NEW.access_url := NULL;
  END IF;
  
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply file path generation trigger
CREATE TRIGGER generate_file_path_trigger 
  BEFORE INSERT OR UPDATE ON public.storage_files
  FOR EACH ROW EXECUTE FUNCTION generate_file_path();

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to get file URL (public or signed)
CREATE OR REPLACE FUNCTION get_file_url(file_id UUID)
RETURNS TEXT AS $$
DECLARE
  file_record public.storage_files%ROWTYPE;
  signed_url TEXT;
BEGIN
  SELECT * INTO file_record FROM public.storage_files WHERE id = file_id;
  
  IF NOT FOUND THEN
    RETURN NULL;
  END IF;
  
  -- Return public URL if file is public
  IF file_record.is_public THEN
    RETURN file_record.access_url;
  END IF;
  
  -- For private files, you would generate a signed URL
  -- This is a placeholder - actual implementation depends on your setup
  RETURN 'https://your-project.supabase.co/storage/v1/object/sign/' || file_record.file_path;
END;
$$ language 'plpgsql';

-- Function to clean up orphaned files
CREATE OR REPLACE FUNCTION cleanup_orphaned_files()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
BEGIN
  -- Delete files that reference non-existent entities
  -- Users
  DELETE FROM public.storage_files 
  WHERE entity_type = 'user' 
    AND entity_id NOT IN (SELECT id FROM public.users);
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Products  
  DELETE FROM public.storage_files 
  WHERE entity_type = 'product' 
    AND entity_id NOT IN (SELECT id FROM public.products);
  
  GET DIAGNOSTICS deleted_count = deleted_count + ROW_COUNT;
  
  -- Add more entity types as needed
  
  RETURN deleted_count;
END;
$$ language 'plpgsql';

-- =====================================================
-- SAMPLE DATA FOR STORAGE FILES
-- =====================================================

-- Note: This is just structure - actual files need to be uploaded via Supabase Storage API
-- Sample storage file entries (after you have actual files uploaded)
/*
INSERT INTO public.storage_files (
  file_name, bucket_name, file_type, file_category, mime_type, 
  file_size, file_extension, entity_type, entity_id, 
  is_public, uploaded_by
) VALUES 
(
  'default-avatar.png', 'city-pos', 'image', 'avatar', 'image/png',
  15360, '.png', 'user', NULL, true,
  (SELECT id FROM public.users LIMIT 1)
),
(
  'coffee-product.jpg', 'city-pos', 'image', 'product', 'image/jpeg',
  245760, '.jpg', 'product', 
  (SELECT id FROM public.products WHERE name LIKE '%cà phê%' LIMIT 1),
  true, (SELECT id FROM public.users LIMIT 1)
);
*/

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if storage_files table was created
SELECT 'storage_files' as table_name, COUNT(*) as record_count 
FROM public.storage_files;

-- Check storage file types
SELECT 
  unnest(enum_range(NULL::file_type)) as file_type,
  unnest(enum_range(NULL::file_category)) as file_category;

-- Check indexes
SELECT indexname, tablename 
FROM pg_indexes 
WHERE tablename = 'storage_files' 
  AND schemaname = 'public';
