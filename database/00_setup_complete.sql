-- =====================================================
-- CITY POS - COMPLETE DATABASE SETUP
-- =====================================================
-- This file sets up the complete database for City POS
-- Run this script in Supabase SQL Editor

-- =====================================================
-- STEP 1: CREATE TABLES AND SCHEMA
-- =====================================================

\i 01_create_tables.sql

-- =====================================================
-- STEP 2: SETUP ROW LEVEL SECURITY
-- =====================================================

\i 02_rls_policies.sql

-- =====================================================
-- STEP 3: CREATE TRIGGERS AND FUNCTIONS
-- =====================================================

\i 03_triggers_functions.sql

-- =====================================================
-- STEP 4: INSERT SAMPLE DATA (OPTIONAL)
-- =====================================================

\i 04_sample_data.sql

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if all tables were created
SELECT 
    schemaname,
    tablename,
    tableowner,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Check if all custom types were created
SELECT 
    typname as type_name,
    typtype as type_type
FROM pg_type 
WHERE typname IN (
    'partner_type', 
    'order_type', 
    'order_status', 
    'payment_status', 
    'invoice_status', 
    'payment_type', 
    'stock_transaction_type', 
    'notification_type'
);

-- Check if all sequences were created
SELECT 
    sequencename,
    start_value,
    increment_by
FROM pg_sequences 
WHERE schemaname = 'public';

-- Check if all functions were created
SELECT 
    proname as function_name,
    pronargs as num_args
FROM pg_proc 
WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
AND proname IN (
    'update_updated_at_column',
    'generate_order_number',
    'generate_invoice_number', 
    'generate_payment_number',
    'create_notification',
    'update_product_stock',
    'create_stock_transaction_from_order',
    'notify_order_completed',
    'notify_invoice_created'
);

-- Check if all triggers were created
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_timing
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
ORDER BY event_object_table, trigger_name;

-- Check if RLS is enabled on all tables
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
AND rowsecurity = true
ORDER BY tablename;

-- Check sample data counts
SELECT 'categories' as table_name, COUNT(*) as record_count FROM public.categories
UNION ALL
SELECT 'products' as table_name, COUNT(*) as record_count FROM public.products
UNION ALL
SELECT 'partners' as table_name, COUNT(*) as record_count FROM public.partners
UNION ALL
SELECT 'users' as table_name, COUNT(*) as record_count FROM public.users
UNION ALL
SELECT 'notifications' as table_name, COUNT(*) as record_count FROM public.notifications
UNION ALL
SELECT 'orders' as table_name, COUNT(*) as record_count FROM public.orders
UNION ALL
SELECT 'order_items' as table_name, COUNT(*) as record_count FROM public.order_items
UNION ALL
SELECT 'invoices' as table_name, COUNT(*) as record_count FROM public.invoices
UNION ALL
SELECT 'payments' as table_name, COUNT(*) as record_count FROM public.payments
UNION ALL
SELECT 'stock_transactions' as table_name, COUNT(*) as record_count FROM public.stock_transactions;

-- =====================================================
-- SETUP COMPLETE
-- =====================================================

SELECT 'City POS Database Setup Complete!' as status;
