# City POS Database Schema

This folder contains the complete database schema for City POS application using Supabase.

## Files Structure

### Core Schema Files
- **`00_setup_complete.sql`** - Complete setup script that runs all other files
- **`01_create_tables.sql`** - Main database schema with all tables, types, and indexes
- **`02_rls_policies.sql`** - Row Level Security policies for data isolation
- **`03_triggers_functions.sql`** - Database triggers and functions for business logic
- **`04_sample_data.sql`** - Sample data for testing and development

### Individual Table Files (Optional)
- **`notifications_table.sql`** - Standalone notifications table (already included in main schema)

## Database Tables

### Core Tables
1. **users** - User management (extends Supabase auth.users)
2. **categories** - Product categories
3. **products** - Product catalog with inventory
4. **partners** - Customers and suppliers
5. **notifications** - In-app notifications

### Business Tables
6. **orders** - Sales/purchase orders
7. **order_items** - Order line items
8. **invoices** - Invoice management
9. **payments** - Finance/cashbook entries
10. **stock_transactions** - Inventory movements

## Setup Instructions

### Option 1: Complete Setup (Recommended)
Run the complete setup script in Supabase SQL Editor:
```sql
-- Copy and paste the content of 00_setup_complete.sql
```

### Option 2: Step by Step Setup
1. **Create Tables and Schema**
   ```sql
   -- Copy and paste content of 01_create_tables.sql
   ```

2. **Setup Row Level Security**
   ```sql
   -- Copy and paste content of 02_rls_policies.sql
   ```

3. **Create Triggers and Functions**
   ```sql
   -- Copy and paste content of 03_triggers_functions.sql
   ```

4. **Insert Sample Data (Optional)**
   ```sql
   -- Copy and paste content of 04_sample_data.sql
   ```

## Features

### Security
- **Row Level Security (RLS)** enabled on all tables
- **Multi-tenant data isolation** - users can only see their own data
- **Role-based access control** (admin, manager, user)

### Auto-numbering
- **Order numbers**: ORD-YYYYMMDD-0001
- **Invoice numbers**: INV-YYYYMMDD-0001  
- **Payment numbers**: PAY-YYYYMMDD-0001

### Business Logic
- **Automatic stock updates** when orders are created
- **Automatic notifications** for completed orders and invoices
- **Updated_at timestamps** automatically maintained

### Data Types
- **Custom ENUM types** for consistent data validation
- **UUID primary keys** for all tables
- **Proper foreign key relationships**

## Verification

After setup, the script will show verification results including:
- All tables created
- Custom types created
- Sequences created
- Functions created
- Triggers created
- RLS policies enabled
- Sample data counts

## Notes

1. **Authentication Required**: You need at least one user in `auth.users` before running sample data
2. **Supabase Integration**: Schema is optimized for Supabase with proper RLS and auth integration
3. **Multi-language Support**: Ready for Vietnamese, English, and Japanese localization
4. **Scalable Design**: Proper indexing and relationships for performance

## Troubleshooting

If you encounter errors:
1. Make sure you're running scripts in Supabase SQL Editor
2. Check that UUID extension is enabled
3. Ensure you have proper permissions
4. Run scripts in the correct order (01 → 02 → 03 → 04)

## Migration from Old Schema

If you have existing data, backup first and then:
1. Export existing data
2. Run new schema
3. Import data with proper transformations
4. Update application code to use new table names

## Support

For issues or questions about the database schema, check:
- Table relationships in the schema files
- RLS policies for access control
- Trigger functions for business logic
