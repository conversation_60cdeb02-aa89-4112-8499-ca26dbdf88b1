# Hệ Thống Cơ Sở Dữ Liệu City POS

Thư mục này chứa toàn bộ schema cơ sở dữ liệu cho ứng dụng City POS sử dụng Supabase.

## Cấu Trúc Files

### Files Schema Chính
- **`00_setup_complete.sql`** - Script setup hoàn chỉnh chạy tất cả files khác
- **`01_create_tables.sql`** - Schema chính với tất cả bảng, types và indexes
- **`02_rls_policies.sql`** - Ch<PERSON>h sách Row Level Security cho phân quyền dữ liệu
- **`03_triggers_functions.sql`** - Triggers và functions cho logic nghiệp vụ
- **`04_sample_data.sql`** - Dữ liệu mẫu cho testing và development
- **`05_storage_files.sql`** - Bảng quản lý files và hình ảnh với Supabase Storage
- **`06_update_tables_for_storage.sql`** - <PERSON><PERSON><PERSON> nhật bảng hiện tại để tích hợp storage
- **`07_supabase_storage_setup.sql`** - Setup buckets và policies cho Supabase Storage

### Files Hướng Dẫn
- **`STORAGE_GUIDE.md`** - Hướng dẫn chi tiết sử dụng storage system

## Chi Tiết Các Bảng Dữ Liệu

### 1. Bảng `users` - Quản Lý Người Dùng
**Mục đích**: Mở rộng từ `auth.users` của Supabase để lưu thông tin bổ sung

**Cấu trúc**:
```sql
- id (UUID, PK) - Liên kết với auth.users(id)
- email (VARCHAR) - Email đăng nhập (unique)
- full_name (VARCHAR) - Họ tên đầy đủ
- phone (VARCHAR) - Số điện thoại
- password (VARCHAR) - Mật khẩu cho tính năng "nhớ đăng nhập"
- role (VARCHAR) - Vai trò: 'admin', 'manager', 'user'
- is_active (BOOLEAN) - Trạng thái hoạt động
- avatar_url (TEXT) - URL ảnh đại diện
- created_at, updated_at (TIMESTAMP)
```

**Quan hệ**:
- Là bảng gốc cho tất cả foreign keys `created_by`
- Liên kết 1-1 với `auth.users`

### 2. Bảng `categories` - Danh Mục Sản Phẩm
**Mục đích**: Phân loại sản phẩm theo danh mục

**Cấu trúc**:
```sql
- id (UUID, PK) - Khóa chính
- name (VARCHAR) - Tên danh mục
- description (TEXT) - Mô tả chi tiết
- color (VARCHAR) - Mã màu hiển thị (#3B82F6)
- icon (VARCHAR) - Tên icon hiển thị
- is_active (BOOLEAN) - Trạng thái hoạt động
- created_by (UUID, FK) - Người tạo
- created_at, updated_at (TIMESTAMP)
```

**Quan hệ**:
- 1-N với `products` (một danh mục có nhiều sản phẩm)
- N-1 với `users` (nhiều danh mục được tạo bởi một user)

### 3. Bảng `products` - Catalog Sản Phẩm
**Mục đích**: Lưu trữ thông tin sản phẩm và quản lý tồn kho

**Cấu trúc**:
```sql
- id (UUID, PK) - Khóa chính
- name (VARCHAR) - Tên sản phẩm
- description (TEXT) - Mô tả sản phẩm
- sku (VARCHAR, UNIQUE) - Mã sản phẩm
- barcode (VARCHAR, UNIQUE) - Mã vạch
- category_id (UUID, FK) - Liên kết danh mục
- price (DECIMAL) - Giá bán
- cost (DECIMAL) - Giá vốn
- stock_quantity (INTEGER) - Số lượng tồn kho
- min_stock_level (INTEGER) - Mức tồn kho tối thiểu
- max_stock_level (INTEGER) - Mức tồn kho tối đa
- unit (VARCHAR) - Đơn vị tính (cái, ly, gói...)
- image_url (TEXT) - URL hình ảnh
- is_active (BOOLEAN) - Trạng thái hoạt động
- created_by (UUID, FK) - Người tạo
- created_at, updated_at (TIMESTAMP)
```

**Quan hệ**:
- N-1 với `categories`
- 1-N với `order_items`
- 1-N với `stock_transactions`

### 4. Bảng `partners` - Khách Hàng & Nhà Cung Cấp
**Mục đích**: Quản lý thông tin khách hàng và nhà cung cấp

**Cấu trúc**:
```sql
- id (UUID, PK) - Khóa chính
- name (VARCHAR) - Tên đối tác
- type (partner_type ENUM) - Loại: 'customer', 'supplier', 'both'
- email (VARCHAR) - Email liên hệ
- phone (VARCHAR) - Số điện thoại
- address (TEXT) - Địa chỉ đầy đủ
- tax_number (VARCHAR) - Mã số thuế
- credit_limit (DECIMAL) - Hạn mức tín dụng
- current_balance (DECIMAL) - Số dư hiện tại
- is_active (BOOLEAN) - Trạng thái hoạt động
- notes (TEXT) - Ghi chú
- created_by (UUID, FK) - Người tạo
- created_at, updated_at (TIMESTAMP)
```

**Quan hệ**:
- 1-N với `orders`
- 1-N với `invoices`
- 1-N với `payments`

### 5. Bảng `notifications` - Thông Báo Trong App
**Mục đích**: Hệ thống thông báo real-time cho người dùng

**Cấu trúc**:
```sql
- id (UUID, PK) - Khóa chính
- user_id (UUID, FK) - Người nhận thông báo
- title (VARCHAR) - Tiêu đề thông báo
- message (TEXT) - Nội dung thông báo
- type (notification_type ENUM) - Loại: 'info', 'warning', 'error', 'success'
- is_read (BOOLEAN) - Trạng thái đã đọc
- data (JSONB) - Dữ liệu bổ sung (order_id, invoice_id...)
- created_at (TIMESTAMP) - Thời gian tạo
```

**Quan hệ**:
- N-1 với `users` (nhiều thông báo cho một user)

### 6. Bảng `storage_files` - Quản Lý Files và Hình Ảnh
**Mục đích**: Quản lý metadata của files được upload lên Supabase Storage

**Cấu trúc**:
```sql
- id (UUID, PK) - Khóa chính
- file_name (VARCHAR) - Tên file gốc
- file_path (TEXT) - Đường dẫn đầy đủ trong Supabase Storage
- bucket_name (VARCHAR) - Tên bucket storage
- file_type (file_type ENUM) - Loại: 'image', 'document', 'video', 'audio', 'other'
- file_category (file_category ENUM) - Danh mục: 'avatar', 'product', 'invoice', 'receipt', 'document', 'other'
- mime_type (VARCHAR) - Loại MIME (image/jpeg, image/png...)
- file_size (BIGINT) - Kích thước file (bytes)
- file_extension (VARCHAR) - Phần mở rộng (.jpg, .png, .pdf...)
- width, height (INTEGER) - Kích thước hình ảnh (pixels)
- entity_type (VARCHAR) - Loại entity liên kết ('user', 'product', 'invoice'...)
- entity_id (UUID) - ID của entity liên kết
- is_public (BOOLEAN) - File công khai hay riêng tư
- access_url (TEXT) - URL truy cập công khai
- alt_text (TEXT) - Alt text cho hình ảnh (accessibility)
- description (TEXT) - Mô tả file
- tags (TEXT[]) - Array tags để tìm kiếm
- uploaded_by (UUID, FK) - Người upload
- created_at, updated_at (TIMESTAMP)
```

**Quan hệ**:
- N-1 với `users` (uploaded_by)
- 1-1 với `users` (avatar_file_id)
- 1-1 với `products` (image_file_id)
- Có thể mở rộng cho các bảng khác

### 7. Bảng `orders` - Đơn Hàng
**Mục đích**: Quản lý đơn hàng bán/mua/trả hàng

**Cấu trúc**:
```sql
- id (UUID, PK) - Khóa chính
- order_number (VARCHAR, UNIQUE) - Số đơn hàng (auto: ORD-YYYYMMDD-0001)
- type (order_type ENUM) - Loại: 'sale', 'purchase', 'return'
- status (order_status ENUM) - Trạng thái: 'pending', 'confirmed', 'completed', 'cancelled'
- partner_id (UUID, FK) - Khách hàng/nhà cung cấp
- subtotal (DECIMAL) - Tổng tiền hàng
- tax_amount (DECIMAL) - Tiền thuế
- discount_amount (DECIMAL) - Tiền giảm giá
- total_amount (DECIMAL) - Tổng cộng
- payment_status (payment_status ENUM) - TT thanh toán: 'pending', 'partial', 'paid', 'refunded'
- payment_method (VARCHAR) - Phương thức thanh toán
- notes (TEXT) - Ghi chú
- created_by (UUID, FK) - Người tạo
- created_at, updated_at (TIMESTAMP)
```

**Quan hệ**:
- N-1 với `partners`
- 1-N với `order_items`
- 1-1 với `invoices` (tùy chọn)
- 1-N với `payments`
- 1-N với `stock_transactions`

### 8. Bảng `order_items` - Chi Tiết Đơn Hàng
**Mục đích**: Lưu từng sản phẩm trong đơn hàng

**Cấu trúc**:
```sql
- id (UUID, PK) - Khóa chính
- order_id (UUID, FK) - Liên kết đơn hàng
- product_id (UUID, FK) - Sản phẩm
- quantity (INTEGER) - Số lượng
- unit_price (DECIMAL) - Đơn giá
- discount_amount (DECIMAL) - Giảm giá
- total_amount (DECIMAL) - Thành tiền
- created_at (TIMESTAMP)
```

**Quan hệ**:
- N-1 với `orders`
- N-1 với `products`

### 9. Bảng `invoices` - Hóa Đơn
**Mục đích**: Quản lý hóa đơn xuất từ đơn hàng

**Cấu trúc**:
```sql
- id (UUID, PK) - Khóa chính
- invoice_number (VARCHAR, UNIQUE) - Số hóa đơn (auto: INV-YYYYMMDD-0001)
- order_id (UUID, FK) - Liên kết đơn hàng (tùy chọn)
- partner_id (UUID, FK) - Khách hàng
- issue_date (DATE) - Ngày xuất hóa đơn
- due_date (DATE) - Ngày đến hạn
- subtotal (DECIMAL) - Tổng tiền hàng
- tax_amount (DECIMAL) - Tiền thuế
- discount_amount (DECIMAL) - Tiền giảm giá
- total_amount (DECIMAL) - Tổng cộng
- paid_amount (DECIMAL) - Đã thanh toán
- status (invoice_status ENUM) - Trạng thái: 'draft', 'sent', 'paid', 'overdue', 'cancelled'
- notes (TEXT) - Ghi chú
- created_by (UUID, FK) - Người tạo
- created_at, updated_at (TIMESTAMP)
```

**Quan hệ**:
- N-1 với `orders` (tùy chọn)
- N-1 với `partners`
- 1-N với `payments`

### 10. Bảng `payments` - Sổ Quỹ/Thanh Toán
**Mục đích**: Quản lý thu chi và thanh toán

**Cấu trúc**:
```sql
- id (UUID, PK) - Khóa chính
- payment_number (VARCHAR, UNIQUE) - Số phiếu (auto: PAY-YYYYMMDD-0001)
- type (payment_type ENUM) - Loại: 'income', 'expense'
- category (VARCHAR) - Danh mục thu/chi
- amount (DECIMAL) - Số tiền
- payment_method (VARCHAR) - Phương thức: 'cash', 'card', 'transfer'
- reference_type (VARCHAR) - Loại tham chiếu: 'order', 'invoice', 'manual'
- reference_id (UUID) - ID tham chiếu
- partner_id (UUID, FK) - Đối tác (tùy chọn)
- description (TEXT) - Mô tả
- payment_date (DATE) - Ngày thanh toán
- created_by (UUID, FK) - Người tạo
- created_at, updated_at (TIMESTAMP)
```

**Quan hệ**:
- N-1 với `partners` (tùy chọn)
- N-1 với `orders` (qua reference_id)
- N-1 với `invoices` (qua reference_id)

### 11. Bảng `stock_transactions` - Giao Dịch Kho
**Mục đích**: Theo dõi xuất nhập tồn kho

**Cấu trúc**:
```sql
- id (UUID, PK) - Khóa chính
- product_id (UUID, FK) - Sản phẩm
- type (stock_transaction_type ENUM) - Loại: 'in', 'out', 'adjustment'
- quantity (INTEGER) - Số lượng (+/-)
- unit_cost (DECIMAL) - Đơn giá
- reference_type (VARCHAR) - Loại tham chiếu: 'order', 'adjustment', 'initial'
- reference_id (UUID) - ID tham chiếu
- notes (TEXT) - Ghi chú
- created_by (UUID, FK) - Người tạo
- created_at (TIMESTAMP)
```

**Quan hệ**:
- N-1 với `products`
- N-1 với `orders` (qua reference_id)

## Hướng Dẫn Cài Đặt

### Cách 1: Cài Đặt Hoàn Chỉnh (Khuyến Nghị)
Chạy script setup hoàn chỉnh trong Supabase SQL Editor:
```sql
-- Copy và paste nội dung file 00_setup_complete.sql
```

### Cách 2: Cài Đặt Từng Bước
1. **Tạo Bảng và Schema**
   ```sql
   -- Copy và paste nội dung file 01_create_tables.sql
   ```

2. **Thiết Lập Row Level Security**
   ```sql
   -- Copy và paste nội dung file 02_rls_policies.sql
   ```

3. **Tạo Triggers và Functions**
   ```sql
   -- Copy và paste nội dung file 03_triggers_functions.sql
   ```

4. **Chèn Dữ Liệu Mẫu (Tùy Chọn)**
   ```sql
   -- Copy và paste nội dung file 04_sample_data.sql
   ```

## Chi Tiết Row Level Security (RLS)

### Nguyên Tắc Phân Quyền
- **Multi-tenant**: Mỗi user chỉ thấy dữ liệu của mình
- **Role-based**: Phân quyền theo vai trò (admin, manager, user)
- **Secure by default**: Tất cả bảng đều bật RLS

### Chính Sách RLS Chi Tiết

#### 1. Bảng `users`
```sql
-- User chỉ xem được profile của mình
"Users can view own profile" - SELECT WHERE auth.uid() = id

-- User chỉ sửa được profile của mình
"Users can update own profile" - UPDATE WHERE auth.uid() = id

-- Admin xem được tất cả users
"Admins can view all users" - SELECT WHERE user.role = 'admin'

-- Admin quản lý được tất cả users
"Admins can manage all users" - ALL WHERE user.role = 'admin'
```

#### 2. Bảng `categories`, `products`, `partners`
```sql
-- Tất cả user đã đăng nhập đều xem được
"All authenticated users can view" - SELECT WHERE auth.role() = 'authenticated'

-- Chỉ Manager và Admin mới tạo/sửa/xóa được
"Managers and admins can manage" - ALL WHERE user.role IN ('admin', 'manager')
```

#### 3. Bảng `notifications`
```sql
-- User chỉ xem thông báo của mình
"Users can view own notifications" - SELECT WHERE user_id = auth.uid()

-- User chỉ cập nhật thông báo của mình (đánh dấu đã đọc)
"Users can update own notifications" - UPDATE WHERE user_id = auth.uid()

-- Hệ thống có thể tạo thông báo
"System can create notifications" - INSERT WHERE auth.role() = 'authenticated'
```

#### 4. Bảng `orders`, `invoices`, `payments`
```sql
-- Tất cả user xem được
"All authenticated users can view" - SELECT WHERE auth.role() = 'authenticated'

-- Tất cả user tạo được
"All authenticated users can create" - INSERT WHERE auth.role() = 'authenticated'

-- User chỉ sửa được record do mình tạo
"Users can update own records" - UPDATE WHERE created_by = auth.uid()

-- Manager/Admin quản lý được tất cả
"Managers and admins can manage all" - ALL WHERE user.role IN ('admin', 'manager')
```

#### 5. Bảng `order_items`, `stock_transactions`
```sql
-- Tất cả user đã đăng nhập đều có thể thao tác
"All authenticated users can manage" - ALL WHERE auth.role() = 'authenticated'
```

## Chi Tiết Triggers và Functions

### 1. Auto-numbering Functions
**Mục đích**: Tự động tạo số thứ tự cho các loại chứng từ

#### `generate_order_number()`
- **Kích hoạt**: BEFORE INSERT trên bảng `orders`
- **Format**: ORD-YYYYMMDD-0001
- **Logic**: Sử dụng sequence `order_number_seq` để đảm bảo unique

#### `generate_invoice_number()`
- **Kích hoạt**: BEFORE INSERT trên bảng `invoices`
- **Format**: INV-YYYYMMDD-0001
- **Logic**: Sử dụng sequence `invoice_number_seq`

#### `generate_payment_number()`
- **Kích hoạt**: BEFORE INSERT trên bảng `payments`
- **Format**: PAY-YYYYMMDD-0001
- **Logic**: Sử dụng sequence `payment_number_seq`

### 2. Stock Management Functions

#### `update_product_stock()`
- **Kích hoạt**: AFTER INSERT trên bảng `stock_transactions`
- **Logic**:
  - `type = 'in'`: Cộng vào tồn kho
  - `type = 'out'`: Trừ khỏi tồn kho
  - `type = 'adjustment'`: Điều chỉnh tồn kho về số lượng mới

#### `create_stock_transaction_from_order()`
- **Kích hoạt**: AFTER INSERT trên bảng `order_items`
- **Logic**: Tự động tạo giao dịch xuất kho khi có đơn bán hàng
- **Điều kiện**: Chỉ áp dụng cho `order.type = 'sale'`

### 3. Notification Functions

#### `create_notification()`
- **Loại**: Function có thể gọi trực tiếp
- **Tham số**:
  - `p_user_id`: ID người nhận
  - `p_title`: Tiêu đề thông báo
  - `p_message`: Nội dung
  - `p_type`: Loại thông báo
  - `p_data`: Dữ liệu JSON bổ sung

#### `notify_order_completed()`
- **Kích hoạt**: AFTER UPDATE trên bảng `orders`
- **Logic**: Tạo thông báo khi đơn hàng chuyển sang trạng thái 'completed'

#### `notify_invoice_created()`
- **Kích hoạt**: AFTER INSERT trên bảng `invoices`
- **Logic**: Tạo thông báo khi có hóa đơn mới

### 4. Utility Functions

#### `update_updated_at_column()`
- **Kích hoạt**: BEFORE UPDATE trên tất cả bảng có cột `updated_at`
- **Logic**: Tự động cập nhật timestamp khi record được sửa

## Quan Hệ Giữa Các Bảng

### Sơ Đồ Quan Hệ Chính
```
users (1) -----> (N) categories
users (1) -----> (N) products
users (1) -----> (N) partners
users (1) -----> (N) orders
users (1) -----> (N) invoices
users (1) -----> (N) payments
users (1) -----> (N) notifications

categories (1) -----> (N) products

partners (1) -----> (N) orders
partners (1) -----> (N) invoices
partners (1) -----> (N) payments

orders (1) -----> (N) order_items
orders (1) -----> (1) invoices (optional)
orders (1) -----> (N) payments
orders (1) -----> (N) stock_transactions

products (1) -----> (N) order_items
products (1) -----> (N) stock_transactions

invoices (1) -----> (N) payments
```

### Luồng Nghiệp Vụ Chính

#### 1. Bán Hàng POS
```
1. Tạo order (type='sale') → Trigger tạo order_number
2. Thêm order_items → Trigger tạo stock_transactions (type='out')
3. Stock_transactions → Trigger cập nhật product.stock_quantity
4. Cập nhật order.status='completed' → Trigger tạo notification
5. Tạo payment (type='income') → Trigger tạo payment_number
6. Tạo invoice (optional) → Trigger tạo invoice_number + notification
```

#### 2. Nhập Hàng
```
1. Tạo order (type='purchase')
2. Thêm order_items
3. Tạo stock_transactions (type='in') → Cập nhật tồn kho
4. Tạo payment (type='expense')
```

#### 3. Điều Chỉnh Kho
```
1. Tạo stock_transactions (type='adjustment')
2. Trigger cập nhật product.stock_quantity = quantity mới
```

## Tính Năng Nổi Bật

### Bảo Mật
- **Row Level Security (RLS)** trên tất cả bảng
- **Multi-tenant data isolation** - user chỉ thấy dữ liệu của mình
- **Role-based access control** (admin, manager, user)

### Tự Động Hóa
- **Đánh số tự động**: ORD-YYYYMMDD-0001, INV-YYYYMMDD-0001, PAY-YYYYMMDD-0001
- **Cập nhật tồn kho tự động** khi có đơn hàng
- **Thông báo tự động** khi hoàn thành đơn hàng và tạo hóa đơn
- **Timestamp tự động** cho updated_at

### Kiểu Dữ Liệu
- **Custom ENUM types** để validate dữ liệu nhất quán
- **UUID primary keys** cho tất cả bảng
- **Foreign key relationships** đầy đủ

## Kiểm Tra Sau Cài Đặt

Script sẽ hiển thị kết quả kiểm tra bao gồm:
- Tất cả bảng đã được tạo
- Custom types đã được tạo
- Sequences đã được tạo
- Functions đã được tạo
- Triggers đã được tạo
- RLS policies đã được bật
- Số lượng dữ liệu mẫu

## Lưu Ý Quan Trọng

1. **Yêu Cầu Authentication**: Cần có ít nhất một user trong `auth.users` trước khi chạy sample data
2. **Tích Hợp Supabase**: Schema được tối ưu cho Supabase với RLS và auth integration
3. **Hỗ Trợ Đa Ngôn Ngữ**: Sẵn sàng cho localization Tiếng Việt, Tiếng Anh, Tiếng Nhật
4. **Thiết Kế Mở Rộng**: Indexing và relationships phù hợp cho performance

## Xử Lý Sự Cố

Nếu gặp lỗi:
1. Đảm bảo chạy scripts trong Supabase SQL Editor
2. Kiểm tra UUID extension đã được bật
3. Đảm bảo có quyền thích hợp
4. Chạy scripts theo đúng thứ tự (01 → 02 → 03 → 04)

## Migration Từ Schema Cũ

Nếu có dữ liệu cũ, backup trước rồi:
1. Export dữ liệu hiện tại
2. Chạy schema mới
3. Import dữ liệu với transformations phù hợp
4. Cập nhật code ứng dụng để sử dụng tên bảng mới

## Hỗ Trợ

Để được hỗ trợ về database schema, kiểm tra:
- Quan hệ bảng trong các file schema
- RLS policies cho access control
- Trigger functions cho business logic
