-- =====================================================
-- CITY POS COMPLETE DATABASE SETUP
-- =====================================================
-- Copy toàn bộ script này và paste vào Supabase SQL Editor
-- Project: esxocouzllrxblgeylxu

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. CREATE TABLES
-- =====================================================

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('admin', 'manager', 'user')),
  is_active BOOLEAN DEFAULT true,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  color VARCHAR(7) DEFAULT '#3B82F6',
  icon VARCHAR(50) DEFAULT 'category',
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  sku VARCHAR(100) UNIQUE,
  barcode VARCHAR(100) UNIQUE,
  category_id UUID REFERENCES categories(id),
  price DECIMAL(10,2) NOT NULL DEFAULT 0,
  cost DECIMAL(10,2) DEFAULT 0,
  stock_quantity INTEGER DEFAULT 0,
  min_stock_level INTEGER DEFAULT 0,
  max_stock_level INTEGER,
  unit VARCHAR(50) DEFAULT 'pcs',
  image_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Partners table (customers and suppliers)
CREATE TABLE IF NOT EXISTS partners (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  type VARCHAR(20) NOT NULL CHECK (type IN ('customer', 'supplier', 'both')),
  email VARCHAR(255),
  phone VARCHAR(20),
  address TEXT,
  tax_number VARCHAR(50),
  credit_limit DECIMAL(10,2) DEFAULT 0,
  current_balance DECIMAL(10,2) DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  notes TEXT,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_number VARCHAR(50) UNIQUE NOT NULL,
  type VARCHAR(20) NOT NULL CHECK (type IN ('sale', 'purchase', 'return')),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'completed', 'cancelled')),
  partner_id UUID REFERENCES partners(id),
  subtotal DECIMAL(10,2) DEFAULT 0,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) DEFAULT 0,
  payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'partial', 'paid', 'refunded')),
  payment_method VARCHAR(50),
  notes TEXT,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order Items table
CREATE TABLE IF NOT EXISTS order_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id),
  quantity INTEGER NOT NULL DEFAULT 1,
  unit_price DECIMAL(10,2) NOT NULL,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Invoices table
CREATE TABLE IF NOT EXISTS invoices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_number VARCHAR(50) UNIQUE NOT NULL,
  order_id UUID REFERENCES orders(id),
  partner_id UUID REFERENCES partners(id),
  issue_date DATE DEFAULT CURRENT_DATE,
  due_date DATE,
  subtotal DECIMAL(10,2) DEFAULT 0,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) DEFAULT 0,
  paid_amount DECIMAL(10,2) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
  notes TEXT,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  payment_number VARCHAR(50) UNIQUE NOT NULL,
  type VARCHAR(20) NOT NULL CHECK (type IN ('income', 'expense')),
  category VARCHAR(50) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  payment_method VARCHAR(50) NOT NULL,
  reference_type VARCHAR(20) CHECK (reference_type IN ('order', 'invoice', 'manual')),
  reference_id UUID,
  partner_id UUID REFERENCES partners(id),
  description TEXT,
  payment_date DATE DEFAULT CURRENT_DATE,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stock Transactions table
CREATE TABLE IF NOT EXISTS stock_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES products(id),
  type VARCHAR(20) NOT NULL CHECK (type IN ('in', 'out', 'adjustment')),
  quantity INTEGER NOT NULL,
  unit_cost DECIMAL(10,2),
  reference_type VARCHAR(20) CHECK (reference_type IN ('order', 'adjustment', 'initial')),
  reference_id UUID,
  notes TEXT,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE partners ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_transactions ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 3. CREATE RLS POLICIES (SIMPLIFIED FOR TESTING)
-- =====================================================

-- Users policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Simple policies for all authenticated users (can be refined later)
CREATE POLICY "Authenticated users can view categories" ON categories
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage categories" ON categories
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view products" ON products
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage products" ON products
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view partners" ON partners
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage partners" ON partners
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view orders" ON orders
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage orders" ON orders
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view order items" ON order_items
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage order items" ON order_items
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view invoices" ON invoices
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage invoices" ON invoices
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view payments" ON payments
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage payments" ON payments
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view stock transactions" ON stock_transactions
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage stock transactions" ON stock_transactions
  FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- 4. INSERT SAMPLE DATA
-- =====================================================

-- Insert sample categories
INSERT INTO categories (name, description, color, icon) VALUES
('Đồ uống', 'Các loại nước uống, cà phê, trà', '#10B981', 'local_drink'),
('Thực phẩm', 'Bánh kẹo, snack, thực phẩm khô', '#F59E0B', 'fastfood'),
('Văn phòng phẩm', 'Bút, giấy, dụng cụ văn phòng', '#3B82F6', 'edit'),
('Điện tử', 'Phụ kiện điện tử, sạc, tai nghe', '#8B5CF6', 'devices'),
('Gia dụng', 'Đồ dùng gia đình, nhà bếp', '#EF4444', 'home');

-- Insert sample products
INSERT INTO products (name, description, sku, barcode, category_id, price, cost, stock_quantity, min_stock_level, unit) VALUES
('Cà phê đen', 'Cà phê đen truyền thống', 'CF001', '1234567890123',
 (SELECT id FROM categories WHERE name = 'Đồ uống' LIMIT 1), 25000, 15000, 50, 10, 'ly'),
('Cà phê sữa', 'Cà phê sữa đá', 'CF002', '1234567890124',
 (SELECT id FROM categories WHERE name = 'Đồ uống' LIMIT 1), 30000, 18000, 45, 10, 'ly'),
('Trà đá', 'Trà đá chanh', 'TR001', '1234567890125',
 (SELECT id FROM categories WHERE name = 'Đồ uống' LIMIT 1), 15000, 8000, 30, 5, 'ly'),
('Bánh mì', 'Bánh mì thịt nướng', 'BM001', '1234567890126',
 (SELECT id FROM categories WHERE name = 'Thực phẩm' LIMIT 1), 20000, 12000, 25, 5, 'cái'),
('Kẹo cao su', 'Kẹo cao su không đường', 'KC001', '1234567890127',
 (SELECT id FROM categories WHERE name = 'Thực phẩm' LIMIT 1), 5000, 3000, 100, 20, 'gói'),
('Bút bi', 'Bút bi xanh', 'BB001', '1234567890128',
 (SELECT id FROM categories WHERE name = 'Văn phòng phẩm' LIMIT 1), 8000, 5000, 80, 15, 'cái'),
('Sạc điện thoại', 'Sạc nhanh USB-C', 'SC001', '1234567890129',
 (SELECT id FROM categories WHERE name = 'Điện tử' LIMIT 1), 150000, 100000, 20, 5, 'cái'),
('Ly nhựa', 'Ly nhựa trong suốt', 'LY001', '1234567890130',
 (SELECT id FROM categories WHERE name = 'Gia dụng' LIMIT 1), 12000, 8000, 60, 10, 'cái');

-- Insert sample partners (customers and suppliers)
INSERT INTO partners (name, type, email, phone, address) VALUES
('Nguyễn Văn A', 'customer', '<EMAIL>', '0901234567', '123 Đường ABC, Quận 1, TP.HCM'),
('Trần Thị B', 'customer', '<EMAIL>', '0901234568', '456 Đường DEF, Quận 2, TP.HCM'),
('Lê Văn C', 'customer', '<EMAIL>', '0901234569', '789 Đường GHI, Quận 3, TP.HCM'),
('Công ty XYZ', 'supplier', '<EMAIL>', '0281234567', '100 Đường Công nghiệp, Quận 9, TP.HCM'),
('Nhà phân phối ABC', 'supplier', '<EMAIL>', '0281234568', '200 Đường Thương mại, Quận 7, TP.HCM');

-- =====================================================
-- 5. SETUP COMPLETE!
-- =====================================================
-- Database is now ready for City POS app
-- Tables: 9 main tables with proper relationships
-- Security: RLS enabled with policies for authenticated users
-- Data: Sample categories, products, and partners ready for testing
