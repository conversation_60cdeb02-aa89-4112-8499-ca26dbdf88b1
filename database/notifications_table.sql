-- Create notifications table for storing app notifications
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(50) NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
  category VARCHAR(50) NOT NULL CHECK (category IN ('system', 'inventory', 'sales', 'finance', 'user')),
  is_read BOOLEAN DEFAULT FALSE,
  is_important BOOLEAN DEFAULT FALSE,
  action_url VARCHAR(255),
  action_label VARCHAR(100),
  data JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  read_at TIMESTAMP,
  expires_at TIMESTAMP,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_category ON notifications(category);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);

-- Insert some sample notifications for testing
INSERT INTO notifications (title, message, type, category, is_read, action_url, action_label, data, user_id) VALUES
('Sản phẩm sắp hết hàng', 'Cà phê đen chỉ còn 5 sản phẩm trong kho (tối thiểu: 10)', 'warning', 'inventory', false, '/products', 'Xem sản phẩm', '{"product_id": "1", "current_stock": 5, "min_stock": 10}', NULL),
('Đơn hàng mới', 'Đơn hàng #HD20250603-001 đã được tạo với tổng tiền 125,000 VND', 'info', 'sales', false, '/orders', 'Xem đơn hàng', '{"order_id": "1", "total_amount": 125000}', NULL),
('Thanh toán thành công', 'Đã nhận thanh toán 125,000 VND cho đơn hàng #HD20250603-001', 'success', 'finance', false, '/finance', 'Xem giao dịch', '{"order_id": "1", "amount": 125000, "payment_method": "cash"}', NULL),
('Phiếu nhập kho', 'Đã tạo phiếu nhập kho với 5 sản phẩm', 'success', 'inventory', true, '/stock-transactions', 'Xem phiếu', '{"transaction_type": "import", "quantity": 5}', NULL),
('Cập nhật hệ thống', 'Hệ thống đã được cập nhật lên phiên bản 2.1.0', 'info', 'system', true, NULL, NULL, '{"version": "2.1.0"}', NULL);

-- Enable Row Level Security (RLS)
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid() OR user_id IS NULL);

CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (user_id = auth.uid() OR user_id IS NULL);

CREATE POLICY "System can create notifications" ON notifications
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can delete their own notifications" ON notifications
  FOR DELETE USING (user_id = auth.uid() OR user_id IS NULL);
