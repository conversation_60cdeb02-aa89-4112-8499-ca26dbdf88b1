# City POS

A Flutter-based Point of Sale (POS) application for city businesses.

## 🚀 Getting Started

### Prerequisites

- Flutter SDK 3.32.0 (locked version)
- Dart SDK 3.8.0
- Android Studio / VS Code
- Git

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd city_pos
   ```

2. **Install Flutter dependencies**
   ```bash
   flutter pub get
   ```

3. **Environment Configuration**
   ```bash
   # Copy environment template
   cp .env.example .env

   # Edit .env with your actual values
   nano .env
   ```

4. **Run the application**
   ```bash
   # Debug mode
   flutter run

   # Specific device
   flutter run -d <device-id>
   ```

### 🛠️ Development Guidelines

#### Code Style
- Follow Dart/Flutter conventions
- Use 2 spaces for indentation
- Maximum line length: 80 characters
- Format code before committing: `dart format .`

#### Git Workflow
1. Create feature branch from `main`
2. Make changes and commit with descriptive messages
3. Run tests: `flutter test`
4. Run analysis: `flutter analyze`
5. Create pull request

#### Environment Management
- **Never commit** `.env` files
- Use `.env.example` as template
- Update `.env.example` when adding new environment variables

### 📁 Project Structure

```
lib/
├── main.dart              # Application entry point
├── models/               # Data models
├── screens/              # UI screens
├── widgets/              # Reusable widgets
├── services/             # Business logic & API calls
├── utils/                # Utility functions
└── constants/            # App constants

test/                     # Unit tests
integration_test/         # Integration tests
android/                  # Android-specific code
ios/                      # iOS-specific code
```

### 🧪 Testing

```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/

# Run tests with coverage
flutter test --coverage
```

### 🏗️ Building

```bash
# Android APK
flutter build apk --release

# Android App Bundle
flutter build appbundle --release

# iOS
flutter build ios --release
```

### 📱 Supported Platforms

- ✅ Android (API 21+)
- ✅ iOS (iOS 12.0+)
- 🔄 Web (planned)
- 🔄 Desktop (planned)

### 🔧 VS Code Setup

Recommended extensions are automatically suggested when opening the project.
Key extensions:
- Dart & Flutter
- GitLens
- Prettier
- EditorConfig

### 📋 Available Scripts

Use VS Code Command Palette (Cmd/Ctrl + Shift + P) or terminal:

- `Flutter: Get Dependencies`
- `Flutter: Clean`
- `Flutter: Run Tests`
- `Flutter: Analyze`
- `Dart: Format`

### 🐛 Troubleshooting

#### Common Issues

1. **Dependencies issues**
   ```bash
   flutter clean
   flutter pub get
   ```

2. **Build issues**
   ```bash
   flutter clean
   flutter pub get
   cd ios && pod install && cd ..  # iOS only
   ```

3. **Version conflicts**
   - Ensure Flutter version matches `.flutter-version`
   - Use FVM for version management

### 🤝 Contributing

1. Follow the development setup
2. Create feature branch
3. Write tests for new features
4. Ensure all tests pass
5. Submit pull request

### 📄 License

This project is proprietary software. All rights reserved.

### 📞 Support

For development questions, contact the development team.

Dưới đây là **cấu trúc dự án** và **nghiệp vụ chi tiết** (có giải pháp – solution) cho một **dự án phần mềm quản lý bán hàng** sử dụng **Flutter** và **Supabase**, để bạn có thể ghi vào Redmine hoặc tài liệu đặc tả kỹ thuật nội bộ.

---

## 1. Triển khai dự án

---

## 2. **Mục tiêu**

Xây dựng ứng dụng đa nền tảng (mobile/tablet/web) phục vụ cho việc quản lý toàn bộ quy trình bán hàng, tồn kho, xuất nhập, hóa đơn, khách hàng và báo cáo tài chính. Hệ thống dễ mở rộng, giao diện thân thiện với người dùng, bảo mật cao và hiệu suất ổn định.

---

## 3. **Công nghệ sử dụng**

* **Frontend**: Flutter (hỗ trợ Android, iOS, Web)
* **Backend**: Supabase (PostgreSQL, Supabase Auth, Realtime, Edge Functions)
* **State Management**: Riverpod hoặc Bloc (tuỳ chọn)
* **Authentication**: Supabase Auth (Email/Password + Magic Link)
* **Database**: PostgreSQL (qua Supabase)
* **Realtime**: Supabase Realtime Channels (theo dõi thay đổi bảng)
* **File storage**: Supabase Storage
* **Reporting**: Export file (PDF, Excel), thống kê biểu đồ (charts)

---

## 4. **Cấu trúc thư mục Flutter (đề xuất)**

```
/lib
  /core                # Cấu hình chung: constants, themes, routers, utils
  /data
    /models            # Định nghĩa các model (Product, Invoice, etc.)
    /repositories      # Tương tác với Supabase (CRUD)
  /features
    /auth              # Đăng nhập, đăng ký
    /inventory         # Quản lý hàng hoá, xuất nhập
    /sales             # Màn hình bán hàng
    /invoices          # Quản lý hóa đơn
    /finance           # Sổ quỹ
    /partners          # Quản lý khách hàng và nhà cung cấp
    /reports           # Báo cáo tổng hợp
  /widgets             # Widget dùng chung
  main.dart            # Entry point
```

---

## 5. **Nghiệp vụ chi tiết**

---

### 1. **Quản lý hàng hoá**

**Mô tả**: Cho phép thêm, sửa, xóa, tìm kiếm sản phẩm theo mã hàng, tên hàng, nhóm hàng, đơn vị tính, tồn kho.

**Tính năng**:

* Danh sách hàng hoá
* Nhóm hàng
* Đơn vị tính
* Mã vạch (tự sinh hoặc quét)

**Solution**:

* Tạo bảng `products` trong Supabase:

```sql
id (uuid), name, sku, unit, category_id, price_import, price_sale, stock_qty, created_at
```

* Tạo bảng `product_categories` để phân loại hàng
* Flutter UI có ListView, SearchBar, Form thêm/sửa
* Tự động cập nhật tồn kho dựa vào phiếu nhập/xuất

---

### 2. **Quản lý xuất nhập hàng**

**Mô tả**: Ghi nhận các phiếu nhập kho (mua hàng) và phiếu xuất kho (trả hàng, hủy hàng).

**Tính năng**:

* Tạo phiếu nhập kho
* Tạo phiếu xuất kho
* Danh sách phiếu, lọc theo ngày/tháng

**Solution**:

* Bảng `stock_transactions`:

```sql
id, type (import/export), partner_id, created_by, total_amount, created_at
```

* Bảng `stock_transaction_items`:

```sql
id, transaction_id, product_id, qty, unit_price, total_price
```

* Supabase trigger: tự cập nhật `products.stock_qty` khi thêm phiếu

---

### 3. **Bán hàng**

**Mô tả**: Tạo đơn hàng trực tiếp, tính tổng tiền, chiết khấu, tiền khách đưa và tiền thối.

**Tính năng**:

* Giao diện chọn hàng nhanh
* Giỏ hàng
* In/hiển thị hóa đơn
* Lưu tạm đơn (draft)

**Solution**:

* Bảng `orders`:

```sql
id, customer_id, total_amount, discount, payment_method, created_at
```

* Bảng `order_items`:

```sql
id, order_id, product_id, qty, unit_price, total_price
```

* Supabase function để tính lại số lượng tồn ngay khi đơn hoàn tất
* Flutter hỗ trợ scan mã vạch + tìm nhanh bằng TextField + suggestion

---

### 4. **Quản lý hoá đơn**

**Mô tả**: Lưu trữ, hiển thị, tìm kiếm và in/xuất hóa đơn.

**Tính năng**:

* Danh sách hoá đơn
* Chi tiết từng hoá đơn
* Xuất PDF/Excel

**Solution**:

* Tái sử dụng bảng `orders`, `order_items`
* Dùng thư viện như `printing`, `pdf`, `excel` để xuất hóa đơn
* Supabase Role-based Auth để phân quyền ai được phép xoá/sửa hóa đơn

---

### 5. **Sổ quỹ**

**Mô tả**: Theo dõi thu - chi, công nợ, dòng tiền vào/ra.

**Tính năng**:

* Ghi phiếu thu, phiếu chi
* Liên kết công nợ khách hàng/nhà cung cấp
* Báo cáo tổng tiền tồn

**Solution**:

* Bảng `cashbooks`:

```sql
id, type (receipt/payment), amount, partner_id, description, created_at
```

* Liên kết với `orders`, `stock_transactions` để quản lý công nợ
* Dùng view hoặc trigger để tính số dư đầu/kết

---

### 6. **Quản lý khách hàng và nhà cung cấp**

**Mô tả**: Lưu thông tin đối tác (khách hàng, NCC), theo dõi công nợ và lịch sử giao dịch.

**Tính năng**:

* Thêm/sửa/xoá khách hàng
* Lịch sử giao dịch
* Công nợ hiện tại

**Solution**:

* Bảng `partners`:

```sql
id, name, phone, email, type (customer/supplier), debt, created_at
```

* Quan hệ tới `orders` và `stock_transactions`
* UI có tab riêng cho "Khách hàng" và "Nhà cung cấp"

---

### 7. **Báo cáo**

**Mô tả**: Hiển thị biểu đồ, bảng số liệu thống kê doanh thu, tồn kho, lợi nhuận, công nợ.

**Tính năng**:

* Doanh thu theo ngày/tháng
* Lợi nhuận theo sản phẩm
* Hàng tồn kho
* Công nợ theo đối tác

**Solution**:

* Supabase `RPC Functions` hoặc `Views` để tính toán server-side
* Flutter: Dùng thư viện như `fl_chart` để biểu đồ hóa
* Cho phép export báo cáo (CSV, Excel)

---

## 6. **Phân quyền người dùng**

**Roles**:

* Admin: toàn quyền
* Nhân viên bán hàng: bán, xem kho, không chỉnh sửa hệ thống
* Kế toán: xem sổ quỹ, hóa đơn
* Quản lý kho: nhập/xuất kho

**Solution**:

* Supabase Auth + RLS (Row Level Security)
* Bảng `users` + `roles`, gán role khi đăng ký
* Middleware kiểm tra quyền trên mỗi màn hình (trong Flutter)

---

## 7. **Realtime & Đồng bộ hoá**

* Sử dụng `Supabase Realtime` để tự động reload danh sách khi có thay đổi
* Tích hợp `Pull-to-refresh` trong Flutter để thủ công cập nhật
* Với thiết bị không mạng: cache local bằng `Hive` hoặc `Isar` (option nâng cao)

---

## 8. **Ghi chú triển khai**

* Dùng môi trường `.env` để cấu hình Supabase URL/Key
* Đảm bảo bảo mật token với `SecureStorage`
* Viết unit test cho các chức năng logic
* Tạo `seed_data.sql` để test

---

Nếu bạn muốn, mình có thể giúp bạn tạo **task breakdown cụ thể theo Redmine format**, ví dụ:

* Epic: "Quản lý hàng hoá"

  * Task 1: Thiết kế schema bảng `products`, `categories`
  * Task 2: API thêm/sửa/xoá sản phẩm
  * Task 3: Giao diện danh sách + form nhập liệu
  * Task 4: Tích hợp realtime và tìm kiếm

👉 Cho mình biết nếu bạn cần format này nhé.
