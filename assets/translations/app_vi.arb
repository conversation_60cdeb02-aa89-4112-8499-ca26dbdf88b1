{"@@locale": "vi", "appName": "City POS", "@appName": {"description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON>ng dụng"}, "appDescription": "<PERSON><PERSON> thống quản lý bán hàng", "@appDescription": {"description": "<PERSON><PERSON> tả ngắn gọn về ứng dụng"}, "login": "<PERSON><PERSON><PERSON>", "@login": {"description": "<PERSON><PERSON><PERSON> bản nút đăng nhập"}, "register": "<PERSON><PERSON><PERSON> ký", "@register": {"description": "<PERSON><PERSON><PERSON> bản nút đăng ký"}, "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "@logout": {"description": "<PERSON><PERSON><PERSON> bản nút đăng xuất"}, "email": "Email", "@email": {"description": "<PERSON>hãn trường email"}, "password": "<PERSON><PERSON><PERSON>", "@password": {"description": "<PERSON><PERSON><PERSON><PERSON> trư<PERSON><PERSON> mật kh<PERSON>u"}, "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "@confirmPassword": {"description": "<PERSON><PERSON><PERSON>n trường xác nhận mật khẩu"}, "name": "Họ và tên", "@name": {"description": "<PERSON><PERSON>ã<PERSON> trường họ và tên"}, "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "@phone": {"description": "<PERSON><PERSON><PERSON><PERSON> trường số điện thoại"}, "forgotPassword": "<PERSON>uên mật khẩu?", "@forgotPassword": {"description": "<PERSON><PERSON><PERSON> bản liên kết quên mật khẩu"}, "dontHaveAccount": "Chưa có tài k<PERSON>n?", "@dontHaveAccount": {"description": "<PERSON><PERSON><PERSON> bản hỏi người dùng chưa có tài khoản"}, "alreadyHaveAccount": "Đã có tài k<PERSON>n?", "@alreadyHaveAccount": {"description": "<PERSON><PERSON><PERSON> bản hỏi người dùng đã có tài khoản"}, "registerNow": "<PERSON><PERSON><PERSON> ký ngay", "@registerNow": {"description": "<PERSON><PERSON><PERSON> bản liên kết đăng ký ngay"}, "loginNow": "<PERSON><PERSON><PERSON> nh<PERSON> ngay", "@loginNow": {"description": "<PERSON><PERSON><PERSON> bản liên kết đăng nhập ngay"}, "dashboard": "Dashboard", "@dashboard": {"description": "Tiêu đề trang dashboard"}, "welcome": "Chào mừng trở lại!", "@welcome": {"description": "<PERSON>h<PERSON><PERSON> điệp chào mừng người dùng quay lại"}, "today": "<PERSON><PERSON><PERSON> nay", "@today": {"description": "<PERSON><PERSON><PERSON><PERSON> tố ngày hôm nay"}, "quickStats": "<PERSON><PERSON><PERSON><PERSON> kê nhanh", "@quickStats": {"description": "<PERSON>i<PERSON><PERSON> đề phần thống kê nhanh"}, "quickActions": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "@quickActions": {"description": "Ti<PERSON><PERSON> đề phần thao tác n<PERSON>h"}, "todayRevenue": "<PERSON><PERSON><PERSON> thu hôm nay", "@todayRevenue": {"description": "Nhãn chỉ số doanh thu hôm nay"}, "todayOrders": "<PERSON><PERSON><PERSON> hàng hôm nay", "@todayOrders": {"description": "Nhãn chỉ số đơn hàng hôm nay"}, "lowStockProducts": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> s<PERSON><PERSON> hết", "@lowStockProducts": {"description": "<PERSON><PERSON>ãn chỉ số sản phẩm sắp hết hàng"}, "totalCustomers": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng", "@totalCustomers": {"description": "Nhãn chỉ số tổng khách hàng"}, "sales": "<PERSON><PERSON>", "@sales": {"description": "Mục menu b<PERSON> h<PERSON>ng"}, "newSale": "<PERSON><PERSON> hàng mới", "@newSale": {"description": "<PERSON><PERSON><PERSON> thao tác bán hàng mới"}, "inventory": "<PERSON><PERSON><PERSON> h<PERSON>", "@inventory": {"description": "Mục <PERSON> hàng hóa"}, "addProduct": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "@addProduct": {"description": "<PERSON><PERSON><PERSON> thao tác thêm sản phẩm"}, "stockTransactions": "<PERSON><PERSON><PERSON><PERSON> kho", "@stockTransactions": {"description": "Mục menu xuất nhập kho"}, "addStock": "<PERSON><PERSON><PERSON><PERSON> kho", "@addStock": {"description": "<PERSON><PERSON><PERSON> thao tác nhập kho"}, "stockIn": "<PERSON><PERSON><PERSON><PERSON> kho", "@stockIn": {"description": "<PERSON><PERSON><PERSON> giao d<PERSON>ch nh<PERSON> kho"}, "stockOut": "<PERSON><PERSON><PERSON> kho", "@stockOut": {"description": "<PERSON><PERSON><PERSON> giao d<PERSON>ch xu<PERSON>t kho"}, "stockManagement": "<PERSON><PERSON><PERSON><PERSON> lý kho", "@stockManagement": {"description": "Mục menu quản lý kho"}, "invoices": "<PERSON><PERSON><PERSON>", "@invoices": {"description": "<PERSON><PERSON>c <PERSON> hóa đơn"}, "finance": "Sổ quỹ", "@finance": {"description": "Mục menu sổ quỹ"}, "partners": "<PERSON><PERSON><PERSON>", "@partners": {"description": "<PERSON>ục menu đối tác"}, "customers": "<PERSON><PERSON><PERSON><PERSON>", "@customers": {"description": "Mục menu khách hàng"}, "suppliers": "<PERSON><PERSON><PERSON> cung cấp", "@suppliers": {"description": "Mục menu nhà cung cấp"}, "reports": "Báo cáo", "@reports": {"description": "Mục menu báo cáo"}, "settings": "Cài đặt", "@settings": {"description": "Mục menu cài đặt"}, "profile": "<PERSON><PERSON> sơ", "@profile": {"description": "Mục <PERSON> hồ sơ"}, "users": "<PERSON><PERSON><PERSON><PERSON> dùng", "@users": {"description": "Mục menu người dùng"}, "language": "<PERSON><PERSON><PERSON>", "@language": {"description": "<PERSON><PERSON><PERSON>n cài đặt ngôn ngữ"}, "vietnamese": "Tiếng <PERSON>", "@vietnamese": {"description": "<PERSON><PERSON><PERSON> chọn ngôn ngữ tiếng Việt"}, "english": "English", "@english": {"description": "<PERSON><PERSON><PERSON> chọn ngôn ngữ tiếng <PERSON>h"}, "changeLanguage": "<PERSON>hay đ<PERSON>i ngôn ngữ", "@changeLanguage": {"description": "<PERSON><PERSON><PERSON> bản nút thay đổi ngôn ngữ"}, "selectLanguage": "<PERSON><PERSON><PERSON> ngôn ngữ", "@selectLanguage": {"description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> hộp thoại chọn ngôn ngữ"}, "selectYourPreferredLanguage": "<PERSON><PERSON><PERSON> ngôn ngữ <PERSON>a thích của bạn", "@selectYourPreferredLanguage": {"description": "<PERSON><PERSON><PERSON> bản hướng dẫn chọn ngôn ngữ"}, "languageChangedToVietnamese": "<PERSON><PERSON> sang tiếng Việt", "@languageChangedToVietnamese": {"description": "<PERSON><PERSON><PERSON><PERSON> báo xác nhận khi chuyển sang tiếng Việt"}, "languageChangedToEnglish": "<PERSON><PERSON> sang tiếng <PERSON>", "@languageChangedToEnglish": {"description": "<PERSON><PERSON><PERSON><PERSON> báo xác nhận khi chuyển sang tiếng <PERSON>h"}, "japanese": "日本語", "@japanese": {"description": "<PERSON><PERSON><PERSON> chọn ngôn ngữ tiếng <PERSON>"}, "languageChangedToJapanese": "<PERSON><PERSON> sang ti<PERSON><PERSON>", "@languageChangedToJapanese": {"description": "<PERSON><PERSON><PERSON><PERSON> báo x<PERSON>c nhận khi chuyển sang tiếng Nhật"}, "save": "<PERSON><PERSON><PERSON>", "@save": {"description": "<PERSON><PERSON><PERSON> bản n<PERSON> l<PERSON>u"}, "cancel": "<PERSON><PERSON><PERSON>", "@cancel": {"description": "<PERSON><PERSON><PERSON> bản n<PERSON> h<PERSON>"}, "delete": "Xóa", "@delete": {"description": "<PERSON><PERSON><PERSON> bản n<PERSON> x<PERSON>a"}, "edit": "Chỉnh sửa", "@edit": {"description": "<PERSON><PERSON><PERSON> bản nút chỉnh sửa"}, "add": "<PERSON><PERSON><PERSON><PERSON>", "@add": {"description": "<PERSON><PERSON><PERSON> bản nút thêm"}, "search": "<PERSON><PERSON><PERSON>", "@search": {"description": "<PERSON><PERSON><PERSON> bản nút tìm kiếm"}, "filter": "<PERSON><PERSON><PERSON>", "@filter": {"description": "<PERSON><PERSON><PERSON> bản n<PERSON>"}, "sort": "<PERSON><PERSON><PERSON>p", "@sort": {"description": "<PERSON><PERSON><PERSON> bản n<PERSON>t sắp xếp"}, "refresh": "<PERSON><PERSON><PERSON>", "@refresh": {"description": "<PERSON><PERSON><PERSON> bản nút làm mới"}, "success": "<PERSON><PERSON><PERSON><PERSON> công", "@success": {"description": "<PERSON><PERSON><PERSON> thông báo thành công"}, "error": "Lỗi", "@error": {"description": "<PERSON><PERSON><PERSON> thông báo lỗi"}, "warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "@warning": {"description": "<PERSON><PERSON><PERSON> thông báo cảnh báo"}, "info": "Thông tin", "@info": {"description": "<PERSON><PERSON><PERSON> thông báo thông tin"}, "loading": "<PERSON><PERSON> tả<PERSON>...", "@loading": {"description": "<PERSON><PERSON><PERSON> bản chỉ báo đang tải"}, "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "@noData": {"description": "<PERSON>hông báo không có dữ liệu"}, "retry": "<PERSON><PERSON><PERSON> lại", "@retry": {"description": "<PERSON><PERSON><PERSON> thử lại khi có lỗi"}, "enterEmail": "Nhập email c<PERSON><PERSON> bạn", "@enterEmail": {"description": "<PERSON><PERSON><PERSON> bản placeholder tr<PERSON><PERSON><PERSON> email"}, "enterPassword": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u của bạn", "@enterPassword": {"description": "<PERSON><PERSON>n bản placeholder t<PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON>u"}, "enterName": "<PERSON><PERSON><PERSON><PERSON> họ và tên của bạn", "@enterName": {"description": "<PERSON><PERSON><PERSON> bản placeholder tr<PERSON><PERSON><PERSON> tên"}, "enterPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại của bạn", "@enterPhone": {"description": "<PERSON><PERSON><PERSON> bản placeholder tr<PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> tho<PERSON>i"}, "confirmPasswordHint": "<PERSON><PERSON><PERSON><PERSON> lại mật khẩu của bạn", "@confirmPasswordHint": {"description": "<PERSON><PERSON>n bản placeholder t<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> nhận mật kh<PERSON>u"}, "emailRequired": "<PERSON><PERSON> kh<PERSON>ng đư<PERSON><PERSON> để trống", "@emailRequired": {"description": "Lỗi xác thực trường email"}, "passwordRequired": "<PERSON><PERSON><PERSON> kh<PERSON>u không đư<PERSON><PERSON> để trống", "@passwordRequired": {"description": "Lỗi xác thực trường mật khẩu"}, "nameRequired": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "@nameRequired": {"description": "Lỗi xác thực trường tên"}, "phoneRequired": "<PERSON><PERSON> điện tho<PERSON><PERSON> không đư<PERSON>c để trống", "@phoneRequired": {"description": "Lỗi xác thực trường điện thoại"}, "invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "@invalidEmail": {"description": "Lỗi xác thực định dạng email không hợp lệ"}, "passwordTooShort": "<PERSON><PERSON>t kh<PERSON>u ph<PERSON>i có ít nhất 6 ký tự", "@passwordTooShort": {"description": "Lỗi xác thực mật khẩu quá ngắn"}, "passwordMismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u xác nhận không khớp", "@passwordMismatch": {"description": "Lỗi xác thực mật khẩu không khớp"}, "invalidPhone": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ", "@invalidPhone": {"description": "Lỗi xác thực số điện thoại không hợp lệ"}, "loginSuccess": "<PERSON><PERSON><PERSON> nhập thành công!", "@loginSuccess": {"description": "<PERSON><PERSON><PERSON><PERSON> báo đăng nhập thành công"}, "loginFailed": "<PERSON><PERSON><PERSON> nh<PERSON>p thất bại", "@loginFailed": {"description": "<PERSON><PERSON><PERSON><PERSON> báo đăng nhập thất bại"}, "registerSuccess": "Đăng ký thành công! Vui lòng đăng nhập.", "@registerSuccess": {"description": "<PERSON><PERSON><PERSON><PERSON> báo đăng ký thành công"}, "registerFailed": "<PERSON><PERSON><PERSON> ký thất bại", "@registerFailed": {"description": "<PERSON><PERSON><PERSON><PERSON> báo đăng ký thất bại"}, "featureInDevelopment": "<PERSON><PERSON><PERSON> năng đang đư<PERSON> phát triển", "@featureInDevelopment": {"description": "<PERSON><PERSON><PERSON><PERSON> báo t<PERSON>h năng đang phát triển"}, "comingSoon": "S<PERSON><PERSON> ra mắt", "@comingSoon": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o sắp ra mắt"}, "version": "<PERSON><PERSON><PERSON>", "@version": {"description": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON>n bản"}, "notifications": "<PERSON><PERSON><PERSON><PERSON> báo", "@notifications": {"description": "Mục menu thông báo"}, "notificationCategory": "<PERSON><PERSON>", "@notificationCategory": {"description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>c danh mục thông báo"}, "notificationType": "<PERSON><PERSON><PERSON>", "@notificationType": {"description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> lo<PERSON>i thông báo"}, "notificationUnreadOnly": "Chỉ ch<PERSON>a đọc", "@notificationUnreadOnly": {"description": "<PERSON>ọc chỉ hiển thị thông báo chưa đọc"}, "notificationMarkAllRead": "<PERSON><PERSON><PERSON> dấu tất cả đã đọc", "@notificationMarkAllRead": {"description": "<PERSON><PERSON><PERSON> động đánh dấu tất cả thông báo đã đọc"}, "notificationDeleteAllRead": "<PERSON><PERSON><PERSON> tất cả đã đọc", "@notificationDeleteAllRead": {"description": "<PERSON><PERSON><PERSON> động xóa tất cả thông báo đã đọc"}, "notificationSettings": "Cài đặt thông báo", "@notificationSettings": {"description": "<PERSON><PERSON><PERSON> cài đặt thông báo"}, "notificationMarkAsRead": "<PERSON><PERSON><PERSON> dấu đã đọc", "@notificationMarkAsRead": {"description": "<PERSON><PERSON><PERSON> động đánh dấu thông báo đã đọc"}, "notificationDelete": "Xóa", "@notificationDelete": {"description": "<PERSON><PERSON><PERSON> động xóa thông báo"}, "notificationCategoryAll": "<PERSON><PERSON><PERSON> c<PERSON>", "@notificationCategoryAll": {"description": "<PERSON><PERSON><PERSON> chọn tất cả danh mục"}, "notificationCategorySystem": "<PERSON><PERSON> th<PERSON>", "@notificationCategorySystem": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n danh mục hệ thống"}, "notificationCategoryOrder": "<PERSON><PERSON><PERSON> hàng", "@notificationCategoryOrder": {"description": "<PERSON><PERSON><PERSON> chọn danh mục đơn hàng"}, "notificationCategoryInventory": "<PERSON><PERSON>", "@notificationCategoryInventory": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n danh mục kho hàng"}, "notificationCategoryFinance": "<PERSON><PERSON><PERSON>", "@notificationCategoryFinance": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n danh mục tài ch<PERSON>h"}, "notificationCategoryUser": "<PERSON><PERSON><PERSON><PERSON> dùng", "@notificationCategoryUser": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n danh mục người dùng"}, "notificationTypeAll": "<PERSON><PERSON><PERSON> c<PERSON>", "@notificationTypeAll": {"description": "<PERSON><PERSON><PERSON> chọn tất cả lo<PERSON>i"}, "notificationTypeInfo": "Thông tin", "@notificationTypeInfo": {"description": "<PERSON><PERSON><PERSON> chọn lo<PERSON>i thông tin"}, "notificationTypeWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "@notificationTypeWarning": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n lo<PERSON>i cảnh báo"}, "notificationTypeError": "Lỗi", "@notificationTypeError": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n lo<PERSON>i lỗi"}, "notificationTypeSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "@notificationTypeSuccess": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n lo<PERSON>i thành công"}, "profileSettings": "<PERSON>ài đặt hồ sơ", "@profileSettings": {"description": "Mục menu cài đặt hồ sơ"}, "profileSettingsComingSoon": "<PERSON><PERSON><PERSON> đặt hồ sơ sắp ra mắt", "@profileSettingsComingSoon": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o cài đặt hồ sơ sắp ra mắt"}, "userManagement": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "@userManagement": {"description": "Mục menu quản lý người dùng"}, "userManagementComingSoon": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON>i dùng sắp ra mắt", "@userManagementComingSoon": {"description": "<PERSON><PERSON><PERSON><PERSON> bá<PERSON> quản lý người dùng sắp ra mắt"}, "manageUsers": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "@manageUsers": {"description": "<PERSON><PERSON><PERSON> bản thao tác quản lý người dùng"}, "manageYourProfile": "<PERSON><PERSON><PERSON><PERSON> lý hồ sơ của bạn", "@manageYourProfile": {"description": "<PERSON><PERSON><PERSON> bản thao tác quản lý hồ sơ"}, "areYouSureLogout": "Bạn có chắc chắn muốn đăng xuất?", "@areYouSureLogout": {"description": "<PERSON><PERSON><PERSON><PERSON> báo xác nhận đăng xuất"}, "signOut": "<PERSON><PERSON><PERSON> xu<PERSON>", "@signOut": {"description": "<PERSON><PERSON><PERSON> bản nút đăng xuất"}, "signOutOfYourAccount": "<PERSON><PERSON><PERSON> xuất khỏi tài k<PERSON>n", "@signOutOfYourAccount": {"description": "<PERSON><PERSON><PERSON> bản mô tả đăng xuất"}, "appInformation": "Thông tin ứng dụng", "@appInformation": {"description": "<PERSON><PERSON><PERSON><PERSON> đề phần thông tin ứng dụng"}, "description": "<PERSON><PERSON>", "@description": {"description": "<PERSON><PERSON><PERSON><PERSON> mô tả"}, "otherSettings": "Cài đặt khác", "@otherSettings": {"description": "Ti<PERSON>u đề phần cài đặt khác"}, "salesReport": "<PERSON><PERSON><PERSON> c<PERSON>o b<PERSON> h<PERSON>ng", "@salesReport": {"description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> c<PERSON>o b<PERSON> hàng"}, "inventoryReport": "<PERSON><PERSON>o c<PERSON>o tồn kho", "@inventoryReport": {"description": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o tồn kho"}, "financeReport": "Báo c<PERSON>o tài ch<PERSON>h", "@financeReport": {"description": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o tài ch<PERSON>h"}, "profitReport": "<PERSON><PERSON><PERSON> c<PERSON>o lợ<PERSON> n<PERSON>n", "@profitReport": {"description": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o lợ<PERSON> n<PERSON>n"}, "cashFlowReport": "<PERSON><PERSON><PERSON> c<PERSON>o dòng tiền", "@cashFlowReport": {"description": "<PERSON><PERSON><PERSON> báo cáo dòng tiền"}, "daily": "<PERSON>", "@daily": {"description": "<PERSON><PERSON> báo c<PERSON>o theo ng<PERSON>y"}, "weekly": "<PERSON>", "@weekly": {"description": "<PERSON><PERSON> b<PERSON>o c<PERSON>o theo t<PERSON>"}, "monthly": "<PERSON>", "@monthly": {"description": "<PERSON><PERSON> báo c<PERSON>o theo tháng"}, "quarterly": "<PERSON>", "@quarterly": {"description": "<PERSON><PERSON> báo cáo theo quý"}, "yearly": "<PERSON>", "@yearly": {"description": "<PERSON><PERSON> b<PERSON>o c<PERSON>o theo n<PERSON>m"}, "custom": "<PERSON><PERSON><PERSON>", "@custom": {"description": "<PERSON><PERSON> b<PERSON>o c<PERSON>o tùy ch<PERSON>n"}, "reportWillBeAvailableSoon": "Báo cáo này sẽ có sẵn sớm", "@reportWillBeAvailableSoon": {"description": "<PERSON><PERSON><PERSON><PERSON> báo báo cáo sắp ra mắt"}, "pointOfSaleManagementSystem": "<PERSON><PERSON> thống quản lý bán hàng", "@pointOfSaleManagementSystem": {"description": "<PERSON><PERSON> tả đầy đủ về ứng dụng"}, "noOrdersFound": "<PERSON><PERSON><PERSON><PERSON> có đơn hàng nào", "@noOrdersFound": {"description": "<PERSON>h<PERSON><PERSON> báo không tìm thấy đơn hàng"}, "createFirstOrder": "T<PERSON><PERSON> đơn hàng đầu tiên để bắt đầu", "@createFirstOrder": {"description": "<PERSON>h<PERSON><PERSON> báo hướng dẫn tạo đơn hàng đầu tiên"}, "analytics": "<PERSON><PERSON> tích", "@analytics": {"description": "Mục menu phân tích"}, "orderStatusUpdated": "<PERSON><PERSON> cập nhật trạng thái đơn hàng", "@orderStatusUpdated": {"description": "<PERSON><PERSON><PERSON><PERSON> báo x<PERSON>c nhận cập nhật trạng thái đơn hàng"}, "paymentStatusUpdated": "<PERSON><PERSON> cập nhật trạng thái thanh toán", "@paymentStatusUpdated": {"description": "<PERSON>h<PERSON><PERSON> báo xác nhận cập nhật trạng thái thanh toán"}, "orderDeleted": "<PERSON><PERSON> x<PERSON>a đơn hàng", "@orderDeleted": {"description": "<PERSON><PERSON><PERSON><PERSON> báo xác nhận xóa đơn hàng"}, "deleteOrder": "<PERSON><PERSON><PERSON> đơn hàng", "@deleteOrder": {"description": "<PERSON><PERSON><PERSON> thao tác xóa đơn hàng"}, "confirmDeleteOrder": "Bạn có chắc chắn muốn xóa đơn hàng", "@confirmDeleteOrder": {"description": "<PERSON><PERSON><PERSON><PERSON> báo xác nhận xóa đơn hàng"}, "order": "<PERSON><PERSON><PERSON> hàng", "@order": {"description": "<PERSON><PERSON><PERSON><PERSON> đơn hàng"}, "customer": "<PERSON><PERSON><PERSON><PERSON>", "@customer": {"description": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng"}, "paymentStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "@paymentStatus": {"description": "<PERSON><PERSON>ã<PERSON> trạng thái thanh toán"}, "items": "s<PERSON><PERSON> p<PERSON>m", "@items": {"description": "<PERSON><PERSON>ã<PERSON> số lượng sản phẩm"}, "close": "Đ<PERSON><PERSON>", "@close": {"description": "<PERSON><PERSON><PERSON> bản nút đ<PERSON>g"}, "confirm": "<PERSON><PERSON><PERSON>", "@confirm": {"description": "<PERSON><PERSON><PERSON> bản nút x<PERSON>c nh<PERSON>n"}, "complete": "<PERSON><PERSON><PERSON> th<PERSON>", "@complete": {"description": "<PERSON><PERSON><PERSON> bản nút hoàn thành"}, "markAsPaid": "<PERSON><PERSON><PERSON> dấu đã thanh toán", "@markAsPaid": {"description": "<PERSON><PERSON><PERSON> thao tác đánh dấu đã thanh toán"}, "markAsPartial": "<PERSON><PERSON><PERSON> dấu thanh to<PERSON> một phần", "@markAsPartial": {"description": "<PERSON><PERSON><PERSON> thao tác đánh dấu thanh toán một phần"}, "payment": "<PERSON><PERSON><PERSON> chi", "@payment": {"description": "<PERSON><PERSON><PERSON><PERSON>h to<PERSON>"}, "salesOverview": "<PERSON><PERSON><PERSON> quan b<PERSON> hàng", "@salesOverview": {"description": "<PERSON><PERSON><PERSON><PERSON> đề phần tổng quan bán hàng"}, "totalRevenue": "<PERSON><PERSON>ng doanh thu", "@totalRevenue": {"description": "<PERSON><PERSON>ng doanh thu"}, "totalOrders": "<PERSON><PERSON><PERSON> đơn hàng", "@totalOrders": {"description": "Nhãn chỉ số tổng đơn hàng"}, "completedOrders": "<PERSON><PERSON><PERSON> hàng hoàn thành", "@completedOrders": {"description": "Nhãn chỉ số đơn hàng hoàn thành"}, "pendingOrders": "Đ<PERSON>n hàng chờ xử lý", "@pendingOrders": {"description": "Nhãn chỉ số đơn hàng chờ xử lý"}, "paidAmount": "Số tiền đã thanh toán", "@paidAmount": {"description": "Nhãn chỉ số số tiền đã thanh toán"}, "pendingAmount": "<PERSON><PERSON> tiền chờ thanh toán", "@pendingAmount": {"description": "Nhãn chỉ số số tiền chờ thanh toán"}, "averageOrderValue": "G<PERSON>á trị TB/đơn", "@averageOrderValue": {"description": "<PERSON><PERSON><PERSON> trị trung bình mỗi đơn"}, "conversionRate": "Tỷ lệ chuyển đổi", "@conversionRate": {"description": "Nhãn chỉ số tỷ lệ chuyển đổi"}, "filters": "<PERSON><PERSON> lọc", "@filters": {"description": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> bộ lọc"}, "clearFilters": "Xóa bộ lọc", "@clearFilters": {"description": "<PERSON><PERSON><PERSON> bản nút xóa tất cả bộ lọc"}, "clearFilter": "Xóa bộ lọc", "@clearFilter": {"description": "<PERSON><PERSON><PERSON> bản nút xóa một bộ lọc"}, "orderStatus": "<PERSON><PERSON><PERSON><PERSON> thái đơn hàng", "@orderStatus": {"description": "<PERSON><PERSON><PERSON><PERSON> bộ lọc trạng thái đơn hàng"}, "allStatuses": "<PERSON><PERSON><PERSON> cả trạng thái", "@allStatuses": {"description": "<PERSON><PERSON><PERSON> chọn bộ lọc tất cả trạng thái"}, "pending": "Chờ xử lý", "@pending": {"description": "<PERSON><PERSON><PERSON><PERSON> thái chờ xử lý"}, "confirmed": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "@confirmed": {"description": "<PERSON>r<PERSON>ng thái đã xác n<PERSON>n"}, "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "@completed": {"description": "<PERSON>r<PERSON><PERSON> thái hoàn thành"}, "cancelled": "<PERSON><PERSON> hủy", "@cancelled": {"description": "<PERSON>r<PERSON><PERSON> thái hóa đơn đã hủy"}, "allPaymentStatuses": "<PERSON><PERSON><PERSON> cả trạng thái thanh toán", "@allPaymentStatuses": {"description": "<PERSON><PERSON><PERSON> chọn bộ lọc tất cả trạng thái thanh toán"}, "pendingPayment": "Chờ thanh toán", "@pendingPayment": {"description": "Tr<PERSON>ng thái chờ thanh toán"}, "partialPayment": "<PERSON><PERSON> <PERSON><PERSON> một ph<PERSON>n", "@partialPayment": {"description": "<PERSON>r<PERSON><PERSON> thái thanh to<PERSON> một phần"}, "paid": "<PERSON><PERSON> thanh toán", "@paid": {"description": "Tr<PERSON>ng thái hóa đơn đã thanh toán"}, "refunded": "<PERSON><PERSON> hoàn tiền", "@refunded": {"description": "Tr<PERSON>ng thái đã hoàn tiền"}, "selectDateRange": "<PERSON><PERSON><PERSON> k<PERSON> thời gian", "@selectDateRange": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n k<PERSON>ng thời gian"}, "exportReports": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "@exportReports": {"description": "<PERSON><PERSON><PERSON> bản nút xuất báo cáo"}, "salesReportDescription": "<PERSON><PERSON> hiệu su<PERSON>t bán hàng và xu hướng doanh thu", "@salesReportDescription": {"description": "<PERSON><PERSON><PERSON> bản mô tả báo c<PERSON>o bán hàng"}, "inventoryReportDescription": "<PERSON> mức tồn kho và chuyển động hàng hóa", "@inventoryReportDescription": {"description": "<PERSON><PERSON><PERSON> bản mô tả báo cáo hàng hóa"}, "financeReportDescription": "<PERSON><PERSON><PERSON><PERSON> sát dòng tiền và tình hình tài ch<PERSON>h", "@financeReportDescription": {"description": "<PERSON><PERSON><PERSON> bản mô tả báo cáo tài ch<PERSON>h"}, "customerReportDescription": "<PERSON>ân tích hành vi và sở thích khách hàng", "@customerReportDescription": {"description": "<PERSON><PERSON><PERSON> bản mô tả báo cáo kh<PERSON>ch hàng"}, "productReportDescription": "<PERSON>em xét hiệu suất và độ phổ biến sản phẩm", "@productReportDescription": {"description": "<PERSON><PERSON><PERSON> bản mô tả báo cáo sản phẩm"}, "profitReportDescription": "<PERSON><PERSON><PERSON> toán biên lợi nhuận và khả năng sinh lời", "@profitReportDescription": {"description": "<PERSON><PERSON><PERSON> bản mô tả báo cáo lợi n<PERSON>n"}, "reportsComingSoon": "Báo c<PERSON>o nâng cao sắp ra mắt", "@reportsComingSoon": {"description": "<PERSON><PERSON><PERSON><PERSON> đề báo cáo nâng cao sắp ra mắt"}, "reportsComingSoonDescription": "<PERSON><PERSON> tích chi tiết và thông tin chi tiết sẽ có trong bản cập nhật tiếp theo", "@reportsComingSoonDescription": {"description": "<PERSON><PERSON> tả báo cáo nâng cao sắp ra mắt"}, "yesterday": "<PERSON><PERSON><PERSON> qua", "@yesterday": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n bộ lọc ngày hôm qua"}, "thisWeek": "<PERSON><PERSON><PERSON>", "@thisWeek": {"description": "<PERSON><PERSON><PERSON> chọn bộ lọc tuần này"}, "lastWeek": "<PERSON><PERSON><PERSON> tr<PERSON>", "@lastWeek": {"description": "<PERSON><PERSON><PERSON> chọn bộ lọc tuần trước"}, "thisMonth": "<PERSON><PERSON><PERSON><PERSON>", "@thisMonth": {"description": "<PERSON><PERSON><PERSON> chọn bộ lọc tháng này"}, "lastMonth": "<PERSON><PERSON><PERSON><PERSON>", "@lastMonth": {"description": "<PERSON><PERSON><PERSON> ch<PERSON> bộ lọc tháng trước"}, "selectCustomRange": "<PERSON><PERSON><PERSON> k<PERSON>ng tùy chỉnh", "@selectCustomRange": {"description": "<PERSON><PERSON><PERSON> chọn chọn khoảng thời gian tùy chỉnh"}, "clearSelection": "<PERSON>óa l<PERSON>a ch<PERSON>n", "@clearSelection": {"description": "<PERSON><PERSON><PERSON> bản nút xóa lựa chọn"}, "noProductsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "@noProductsFound": {"description": "<PERSON>hô<PERSON> báo không tìm thấy sản phẩm"}, "addFirstProduct": "Thêm sản phẩm đầu tiên để bắt đầu", "@addFirstProduct": {"description": "<PERSON>h<PERSON><PERSON> báo hướng dẫn thêm sản phẩm đầu tiên"}, "searchProducts": "<PERSON><PERSON><PERSON> kiếm sản phẩm...", "@searchProducts": {"description": "<PERSON><PERSON>n bản placeholder t<PERSON><PERSON> ki<PERSON>m sản phẩm"}, "allCategories": "<PERSON><PERSON><PERSON> cả danh mục", "@allCategories": {"description": "<PERSON><PERSON><PERSON> chọn bộ lọc tất cả danh mục"}, "productName": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "@productName": {"description": "<PERSON><PERSON><PERSON><PERSON> trường tên sản phẩm"}, "enterProductName": "<PERSON><PERSON><PERSON><PERSON> tên sản phẩm", "@enterProductName": {"description": "Placeholder tr<PERSON><PERSON><PERSON> tên sản phẩm"}, "pleaseEnterProductName": "<PERSON><PERSON> lòng nhập tên sản phẩm", "@pleaseEnterProductName": {"description": "Lỗi xác thực tên sản phẩm"}, "enterDescription": "<PERSON><PERSON><PERSON><PERSON> mô tả", "@enterDescription": {"description": "Placeholder tr<PERSON><PERSON><PERSON> mô tả"}, "enterSku": "Nhập mã SKU", "@enterSku": {"description": "Placeholder tr<PERSON><PERSON>ng SKU"}, "barcode": "Mã vạch", "@barcode": {"description": "Nhãn trường mã vạch"}, "enterBarcode": "<PERSON>hậ<PERSON> mã vạch", "@enterBarcode": {"description": "Placeholder tr<PERSON><PERSON><PERSON> mã vạch"}, "category": "<PERSON><PERSON>", "@category": {"description": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> danh mục"}, "categories": "<PERSON><PERSON>", "@categories": {"description": "Mục menu danh mục"}, "categoryManagement": "<PERSON><PERSON><PERSON><PERSON> lý danh mục", "@categoryManagement": {"description": "Mục menu quản lý danh mục"}, "cost": "<PERSON><PERSON><PERSON>n", "@cost": {"description": "<PERSON><PERSON>ãn trường giá vốn"}, "pleaseEnterPrice": "<PERSON><PERSON> lòng nh<PERSON>p giá", "@pleaseEnterPrice": {"description": "Lỗi xác thực giá"}, "pleaseEnterValidPrice": "<PERSON><PERSON> lòng nh<PERSON>p gi<PERSON> hợp lệ", "@pleaseEnterValidPrice": {"description": "Lỗi xác thực giá hợp lệ"}, "stockQuantity": "Số lư<PERSON>ng tồn kho", "@stockQuantity": {"description": "Nhãn trường số lượng tồn kho"}, "pleaseEnterStock": "<PERSON><PERSON> lòng nhập số lượng tồn kho", "@pleaseEnterStock": {"description": "Lỗi xác thực số lượng tồn kho"}, "pleaseEnterValidStock": "<PERSON><PERSON> lòng nhập số lượng tồn kho hợp lệ", "@pleaseEnterValidStock": {"description": "Lỗi xác thực số lượng tồn kho hợp lệ"}, "unit": "Đơn vị", "@unit": {"description": "<PERSON>hãn trường đơn vị"}, "minStock": "<PERSON><PERSON><PERSON> kho tối thiểu", "@minStock": {"description": "<PERSON><PERSON>ãn trư<PERSON><PERSON> tồn kho tối thiểu"}, "maxStock": "<PERSON><PERSON><PERSON> kho tối đa", "@maxStock": {"description": "<PERSON><PERSON>ãn trườ<PERSON> tồn kho tối đa"}, "optional": "<PERSON><PERSON><PERSON>", "@optional": {"description": "Chỉ báo trường tùy chọn"}, "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "@active": {"description": "<PERSON><PERSON>ã<PERSON> trạng thái hoạt động"}, "productActiveDescription": "<PERSON><PERSON><PERSON> phẩm có sẵn để bán", "@productActiveDescription": {"description": "<PERSON><PERSON> tả trạng thái sản phẩm hoạt động"}, "update": "<PERSON><PERSON><PERSON>", "@update": {"description": "<PERSON><PERSON><PERSON> bản nút cập nh<PERSON>t"}, "deleteProduct": "<PERSON><PERSON><PERSON> p<PERSON>m", "@deleteProduct": {"description": "<PERSON><PERSON><PERSON> thao tác x<PERSON>a sản phẩm"}, "confirmDeleteProduct": "Bạn có chắc chắn muốn xóa sản phẩm", "@confirmDeleteProduct": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o xác nhận xóa sản phẩm"}, "productDeleted": "Đ<PERSON> x<PERSON>a sản phẩm", "@productDeleted": {"description": "<PERSON><PERSON><PERSON><PERSON> báo xác nhận đã xóa sản phẩm"}, "errorOccurred": "Đ<PERSON> xảy ra lỗi", "@errorOccurred": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi chung"}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "@status": {"description": "<PERSON><PERSON><PERSON><PERSON> cho bộ lọc trạng thái"}, "total": "<PERSON><PERSON><PERSON> cộng", "@total": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng số tiền"}, "notes": "<PERSON><PERSON><PERSON>", "@notes": {"description": "Nhãn trư<PERSON><PERSON> ghi chú"}, "createAccount": "<PERSON><PERSON><PERSON> t<PERSON>", "@createAccount": {"description": "<PERSON><PERSON><PERSON> bản nút tạo tài k<PERSON>n"}, "enterAccountDetails": "<PERSON><PERSON><PERSON><PERSON> thông tin tài k<PERSON>n", "@enterAccountDetails": {"description": "<PERSON><PERSON><PERSON> bản hướng dẫn nhập thông tin tài khoản"}, "enterYourAccountInfo": "<PERSON><PERSON><PERSON><PERSON> thông tin tài khoản của bạn", "@enterYourAccountInfo": {"description": "Hướng dẫn nhập thông tin tài khoản"}, "fullName": "Họ và tên", "@fullName": {"description": "<PERSON><PERSON>ã<PERSON> trường họ và tên"}, "enterFullName": "<PERSON><PERSON><PERSON><PERSON> họ và tên", "@enterFullName": {"description": "Placeholder tr<PERSON><PERSON><PERSON> họ và tên"}, "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "@phoneNumber": {"description": "<PERSON><PERSON><PERSON><PERSON> trường số điện thoại"}, "enterPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "@enterPhoneNumber": {"description": "Placeholder tr<PERSON><PERSON><PERSON> số điện thoại"}, "editProduct": "Chỉnh s<PERSON>a sản phẩm", "@editProduct": {"description": "<PERSON><PERSON><PERSON><PERSON> đề hộp thoại chỉnh sửa sản phẩm"}, "sku": "Mã SKU", "@sku": {"description": "Nhãn trường mã SKU"}, "price": "<PERSON><PERSON><PERSON> b<PERSON>", "@price": {"description": "Nhãn trường giá bán"}, "addNewPartner": "<PERSON><PERSON><PERSON><PERSON> đối tác mới", "@addNewPartner": {"description": "Tiêu đề thêm đối tác mới"}, "savePartner": "<PERSON><PERSON><PERSON> t<PERSON>c", "@savePartner": {"description": "<PERSON><PERSON><PERSON> l<PERSON> đ<PERSON> tác"}, "partnerAddedSuccessfully": "<PERSON><PERSON><PERSON> tác đã đư<PERSON><PERSON> thêm thành công", "@partnerAddedSuccessfully": {"description": "<PERSON><PERSON><PERSON><PERSON> báo thêm đối tác thành công"}, "errorAddingPartner": "Lỗi khi thêm đối tác", "@errorAddingPartner": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi khi thêm đối tác"}, "customerType": "<PERSON><PERSON><PERSON><PERSON>", "@customerType": {"description": "<PERSON><PERSON><PERSON> đối tác kh<PERSON>ch hàng"}, "supplierType": "<PERSON><PERSON><PERSON> cung cấp", "@supplierType": {"description": "<PERSON><PERSON><PERSON> đối tác nhà cung cấp"}, "addFinanceTransaction": "<PERSON><PERSON><PERSON><PERSON> phiếu thu chi", "@addFinanceTransaction": {"description": "Ti<PERSON><PERSON> đề thêm phiếu thu chi"}, "saveTransaction": "<PERSON><PERSON><PERSON>", "@saveTransaction": {"description": "<PERSON><PERSON><PERSON> l<PERSON>u p<PERSON>u thu chi"}, "transactionDescription": "<PERSON><PERSON>", "@transactionDescription": {"description": "<PERSON><PERSON><PERSON><PERSON> mô tả giao dịch"}, "transactionDescriptionHint": "VD: <PERSON><PERSON>, <PERSON><PERSON>", "@transactionDescriptionHint": {"description": "<PERSON><PERSON><PERSON> ý mô tả giao dịch"}, "pleaseEnterDescription": "<PERSON><PERSON> lòng nhập mô tả", "@pleaseEnterDescription": {"description": "<PERSON><PERSON><PERSON><PERSON> báo yêu cầu nhập mô tả"}, "incomeType": "<PERSON>hu", "@incomeType": {"description": "<PERSON><PERSON><PERSON> giao dịch thu"}, "expenseType": "<PERSON>", "@expenseType": {"description": "<PERSON><PERSON><PERSON> giao dịch chi"}, "allProducts": "<PERSON><PERSON><PERSON> c<PERSON>", "@allProducts": {"description": "<PERSON><PERSON> tất cả sản phẩm"}, "drinks": "<PERSON><PERSON>", "@drinks": {"description": "<PERSON><PERSON> mục đồ uống"}, "food": "<PERSON><PERSON><PERSON><PERSON> ăn", "@food": {"description": "<PERSON><PERSON> mục thức ăn"}, "snacks": "Bánh kẹo", "@snacks": {"description": "<PERSON><PERSON> mục b<PERSON>h kẹo"}, "other": "K<PERSON><PERSON><PERSON>", "@other": {"description": "<PERSON><PERSON> k<PERSON>c"}, "searchProductHint": "T<PERSON>m sản phẩm theo tên hoặc mã vạch...", "@searchProductHint": {"description": "<PERSON><PERSON><PERSON> ý tìm kiếm sản phẩm"}, "barcodeFeatureInDevelopment": "<PERSON><PERSON><PERSON> năng quét mã vạch đang đư<PERSON><PERSON> phát triển", "@barcodeFeatureInDevelopment": {"description": "<PERSON>h<PERSON><PERSON> báo t<PERSON>h năng quét mã vạch đang phát triển"}, "emptyCart": "Giỏ hàng trống", "@emptyCart": {"description": "<PERSON><PERSON><PERSON><PERSON> báo giỏ hàng trống"}, "addProductsToStart": "<PERSON>h<PERSON><PERSON> sản phẩm để bắt đầu", "@addProductsToStart": {"description": "Hướng dẫn thêm sản phẩm vào giỏ"}, "invoiceStatistics": "<PERSON><PERSON><PERSON><PERSON> kê hóa đơn", "@invoiceStatistics": {"description": "Ti<PERSON><PERSON> đề thống kê hóa đơn"}, "noInvoicesYet": "<PERSON><PERSON><PERSON> có hóa đơn nào", "@noInvoicesYet": {"description": "<PERSON><PERSON><PERSON><PERSON> báo chưa có hóa đơn"}, "createFirstInvoice": "<PERSON><PERSON><PERSON> hóa đơn đầu tiên để bắt đầu", "@createFirstInvoice": {"description": "Hướng dẫn tạo hóa đơn đầu tiên"}, "noCategoriesYet": "<PERSON><PERSON><PERSON> có danh mục nào", "@noCategoriesYet": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o ch<PERSON>a có danh mục"}, "noCategoriesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh mục", "@noCategoriesFound": {"description": "<PERSON>hông báo không tìm thấy danh mục"}, "addFirstCategory": "<PERSON>hê<PERSON> danh mục đầu tiên để bắt đầu", "@addFirstCategory": {"description": "<PERSON><PERSON>ớng dẫn thêm danh mục đầu tiên"}, "tryDifferentKeyword": "Th<PERSON> tìm kiếm với từ khóa khác", "@tryDifferentKeyword": {"description": "<PERSON>ợ<PERSON> ý thử từ khóa khác"}, "addCategory": "<PERSON><PERSON><PERSON><PERSON> danh mục", "@addCategory": {"description": "<PERSON><PERSON><PERSON> thêm danh mục"}, "editCategory": "Chỉnh sửa danh mục", "@editCategory": {"description": "Ti<PERSON><PERSON> đề chỉnh sửa danh mục"}, "categoryName": "<PERSON><PERSON><PERSON> da<PERSON> mục", "@categoryName": {"description": "<PERSON><PERSON><PERSON><PERSON> tên danh mục"}, "categoryDescription": "<PERSON><PERSON>", "@categoryDescription": {"description": "<PERSON><PERSON><PERSON><PERSON> mô tả danh mục"}, "addStockTransaction": "<PERSON><PERSON><PERSON><PERSON> phi<PERSON>u xuất nh<PERSON>p kho", "@addStockTransaction": {"description": "Ti<PERSON><PERSON> đề thêm phiếu xuất nhập kho"}, "salesCategories": "<PERSON><PERSON>", "@salesCategories": {"description": "<PERSON><PERSON><PERSON> b<PERSON> hàng"}, "serviceCategories": "<PERSON><PERSON><PERSON> v<PERSON>", "@serviceCategories": {"description": "<PERSON><PERSON> m<PERSON> d<PERSON> vụ"}, "interestCategories": "<PERSON><PERSON><PERSON>", "@interestCategories": {"description": "<PERSON><PERSON> mục lãi su<PERSON>t"}, "purchaseCategories": "<PERSON><PERSON><PERSON><PERSON>", "@purchaseCategories": {"description": "<PERSON><PERSON> m<PERSON><PERSON> nh<PERSON><PERSON> hàng"}, "salaryCategories": "<PERSON><PERSON><PERSON><PERSON>", "@salaryCategories": {"description": "<PERSON><PERSON> m<PERSON><PERSON> tiề<PERSON> l<PERSON>"}, "rentCategories": "<PERSON><PERSON><PERSON><PERSON> thuê", "@rentCategories": {"description": "<PERSON><PERSON> mục tiền thuê"}, "utilitiesCategories": "Điệ<PERSON> n<PERSON>", "@utilitiesCategories": {"description": "<PERSON><PERSON> mục điện nư<PERSON>c"}, "marketingCategories": "Marketing", "@marketingCategories": {"description": "<PERSON><PERSON> m<PERSON> marketing"}, "noNotifications": "<PERSON><PERSON><PERSON>ng có thông báo", "@noNotifications": {"description": "Thông báo không có thông báo"}, "allNotificationsWillAppearHere": "<PERSON>ất cả thông báo sẽ hiển thị ở đây", "@allNotificationsWillAppearHere": {"description": "Hướng dẫn thông báo sẽ hiển thị"}, "stockTransactionsTitle": "<PERSON><PERSON><PERSON><PERSON> kho", "@stockTransactionsTitle": {"description": "Ti<PERSON><PERSON> đề màn hình xuất nhập kho"}, "cart": "Giỏ hàng", "@cart": {"description": "Nhãn giỏ hàng"}, "clearAll": "<PERSON><PERSON><PERSON> tất cả", "@clearAll": {"description": "<PERSON><PERSON><PERSON> x<PERSON>a tất cả"}, "invoiceList": "<PERSON><PERSON> s<PERSON>ch hóa đ<PERSON>n", "@invoiceList": {"description": "<PERSON><PERSON><PERSON><PERSON> đề danh sách hóa đơn"}, "invoicesCount": "h<PERSON><PERSON> đ<PERSON>n", "@invoicesCount": {"description": "Đơn vị đếm hóa đơn"}, "cashbook": "Sổ quỹ", "@cashbook": {"description": "Ti<PERSON>u đề sổ quỹ"}, "addReceiptExpense": "<PERSON>hê<PERSON> phiếu thu/chi", "@addReceiptExpense": {"description": "<PERSON><PERSON><PERSON> thêm phiếu thu chi"}, "weeklyRevenueReport": "<PERSON><PERSON><PERSON> c<PERSON>o doanh thu tuần", "@weeklyRevenueReport": {"description": "Ti<PERSON><PERSON> đề báo cáo doanh thu tuần"}, "weeklyRevenueReportReady": "<PERSON><PERSON><PERSON> c<PERSON>o doanh thu tuần này đã sẵn sàng để xem", "@weeklyRevenueReportReady": {"description": "<PERSON><PERSON><PERSON> dung báo c<PERSON>o doanh thu tuần"}, "viewReport": "<PERSON><PERSON> b<PERSON>o c<PERSON>o", "@viewReport": {"description": "Nhãn nút xem báo cáo"}, "blackCoffee": "<PERSON>à phê đen", "@blackCoffee": {"description": "<PERSON><PERSON><PERSON> sản phẩm cà phê đen"}, "freshMilk": "<PERSON><PERSON><PERSON>", "@freshMilk": {"description": "<PERSON><PERSON><PERSON> sản phẩm s<PERSON>a t<PERSON>i"}, "customerName": "<PERSON><PERSON><PERSON><PERSON>", "@customerName": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng mẫu"}, "rememberLogin": "<PERSON><PERSON> nhớ đăng nhập", "@rememberLogin": {"description": "Nhãn checkbox ghi nhớ đăng nhập"}, "alreadyHaveAccountLogin": "Đã có tài k<PERSON>n? ", "@alreadyHaveAccountLogin": {"description": "Text hỏi đã có tài k<PERSON>n"}, "loginNowLink": "<PERSON><PERSON><PERSON> nh<PERSON> ngay", "@loginNowLink": {"description": "<PERSON> nh<PERSON>p ngay"}, "posTitle": "POS - <PERSON><PERSON>", "@posTitle": {"description": "Ti<PERSON><PERSON> đề màn hình <PERSON>"}, "cartTitle": "Giỏ hàng", "@cartTitle": {"description": "Ti<PERSON>u đề giỏ hàng với số lượng"}, "clearAllTooltip": "<PERSON><PERSON><PERSON> tất cả", "@clearAllTooltip": {"description": "<PERSON>lt<PERSON> nút xóa tất cả"}, "invoiceListTitle": "<PERSON><PERSON> s<PERSON>ch hóa đ<PERSON>n", "@invoiceListTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề danh sách hóa đơn"}, "invoicesCountLabel": "h<PERSON><PERSON> đ<PERSON>n", "@invoicesCountLabel": {"description": "<PERSON><PERSON>ãn số lượng hóa đơn"}, "financeTitle": "Sổ quỹ", "@financeTitle": {"description": "Ti<PERSON><PERSON> đề màn hình tài ch<PERSON>h"}, "subtotal": "<PERSON><PERSON><PERSON>", "@subtotal": {"description": "<PERSON><PERSON><PERSON><PERSON> tạm t<PERSON>h"}, "tax": "<PERSON><PERSON><PERSON>", "@tax": {"description": "<PERSON><PERSON><PERSON><PERSON> thuế"}, "discount": "G<PERSON>ảm giá", "@discount": {"description": "<PERSON>hãn gi<PERSON>m giá"}, "markAllAsRead": "<PERSON><PERSON><PERSON> dấu tất cả đã đọc", "@markAllAsRead": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h dấu tất cả thông báo đã đọc"}, "deleteAllRead": "<PERSON><PERSON><PERSON> tất cả đã đọc", "@deleteAllRead": {"description": "<PERSON><PERSON><PERSON> xóa tất cả thông báo đã đọc"}, "deleteAllReadConfirm": "<PERSON><PERSON><PERSON> tất cả thông báo đã đọc", "@deleteAllReadConfirm": {"description": "Ti<PERSON><PERSON> đề xác nhận xóa thông báo"}, "deleteAllReadMessage": "Bạn có chắc chắn muốn xóa tất cả thông báo đã đọc?", "@deleteAllReadMessage": {"description": "<PERSON><PERSON><PERSON> dung xác nhận xóa thông báo"}, "notificationSettingsTitle": "Cài đặt thông báo", "@notificationSettingsTitle": {"description": "Tiêu đề cài đặt thông báo"}, "notificationSettingsMessage": "<PERSON><PERSON><PERSON> năng cài đặt thông báo sẽ có sẵn sớm.", "@notificationSettingsMessage": {"description": "<PERSON><PERSON>i dung cài đặt thông báo"}, "productsCount": "s<PERSON><PERSON> p<PERSON>m", "@productsCount": {"description": "<PERSON>ừ đếm sản phẩm (số nhiều)"}, "viewCart": "<PERSON>em giỏ hàng", "@viewCart": {"description": "<PERSON><PERSON><PERSON> xem giỏ hàng"}, "clearCart": "Xóa giỏ hàng", "@clearCart": {"description": "Tooltip xóa giỏ hàng"}, "date": "<PERSON><PERSON><PERSON>", "@date": {"description": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>"}, "selectDate": "<PERSON><PERSON><PERSON>", "@selectDate": {"description": "<PERSON><PERSON><PERSON> ch<PERSON> ng<PERSON>y"}, "transactionDate": "<PERSON><PERSON><PERSON> giao d<PERSON>", "@transactionDate": {"description": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>y giao d<PERSON>ch"}, "itemTotal": "<PERSON><PERSON><PERSON><PERSON> tiền", "@itemTotal": {"description": "<PERSON><PERSON><PERSON><PERSON> thành tiền của item"}, "amount": "<PERSON><PERSON> tiền", "@amount": {"description": "<PERSON><PERSON><PERSON><PERSON> số tiền"}, "noInvoicesFound": "<PERSON><PERSON><PERSON> có hóa đơn nào", "@noInvoicesFound": {"description": "<PERSON><PERSON><PERSON><PERSON> báo không có hóa đơn"}, "createFirstInvoiceToStart": "<PERSON><PERSON><PERSON> hóa đơn đầu tiên để bắt đầu", "@createFirstInvoiceToStart": {"description": "Hướng dẫn tạo hóa đơn đầu tiên"}, "posScreenTitle": "<PERSON><PERSON> (POS)", "@posScreenTitle": {"description": "Ti<PERSON><PERSON> đề màn hình <PERSON>"}, "posScreenEnhancedTitle": "<PERSON><PERSON> (POS) - Enhanced", "@posScreenEnhancedTitle": {"description": "Tiêu đề màn hình POS Enhanced"}, "clearCartTooltip": "Xóa giỏ hàng", "@clearCartTooltip": {"description": "Tooltip xóa giỏ hàng"}, "scanBarcode": "Quét mã vạch", "@scanBarcode": {"description": "<PERSON>lt<PERSON> quét mã vạch"}, "blackCoffeeDemo": "<PERSON>à phê đen", "@blackCoffeeDemo": {"description": "<PERSON><PERSON><PERSON> sản phẩm demo cà phê đen"}, "freshMilkDemo": "<PERSON><PERSON><PERSON>", "@freshMilkDemo": {"description": "<PERSON><PERSON><PERSON> sản phẩm demo s<PERSON>a t<PERSON>i"}, "customerNameDemo": "<PERSON><PERSON><PERSON><PERSON>", "@customerNameDemo": {"description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "weeklyRevenueReportTitle": "<PERSON><PERSON><PERSON> c<PERSON>o doanh thu tuần", "@weeklyRevenueReportTitle": {"description": "Ti<PERSON><PERSON> đề báo cáo doanh thu tuần"}, "weeklyRevenueReportMessage": "<PERSON><PERSON><PERSON> c<PERSON>o doanh thu tuần này đã sẵn sàng để xem", "@weeklyRevenueReportMessage": {"description": "<PERSON><PERSON><PERSON> dung báo c<PERSON>o doanh thu tuần"}, "issueDate": "<PERSON><PERSON><PERSON>", "@issueDate": {"description": "<PERSON><PERSON><PERSON><PERSON> ngày phát hành"}, "dueDate": "Hạn", "@dueDate": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn thanh toán"}, "paidAmountShort": "<PERSON><PERSON> thu", "@paidAmountShort": {"description": "<PERSON><PERSON><PERSON><PERSON> số tiền đã thu (ngắn gọn)"}, "remainingAmount": "<PERSON>òn lại", "@remainingAmount": {"description": "Nhãn số tiền còn lại"}, "statusDraft": "Nháp", "@statusDraft": {"description": "<PERSON>r<PERSON><PERSON> thái n<PERSON>p"}, "statusSent": "Đ<PERSON> gửi", "@statusSent": {"description": "Tr<PERSON>ng thái đã gửi"}, "statusPaid": "<PERSON><PERSON> thanh toán", "@statusPaid": {"description": "Tr<PERSON>ng thái đã thanh toán"}, "statusOverdue": "<PERSON><PERSON><PERSON> h<PERSON>n", "@statusOverdue": {"description": "<PERSON>r<PERSON><PERSON> thái quá hạn"}, "statusCancelled": "<PERSON><PERSON> hủy", "@statusCancelled": {"description": "<PERSON>r<PERSON><PERSON> thái đã hủy"}, "cashFlowDetailTitle": "<PERSON> tiết phiếu thu chi", "@cashFlowDetailTitle": {"description": "Ti<PERSON>u đề màn hình chi tiết phiếu thu chi"}, "editEntry": "Chỉnh sửa", "@editEntry": {"description": "<PERSON><PERSON><PERSON> chỉnh sửa phiếu"}, "deleteEntry": "Xóa", "@deleteEntry": {"description": "<PERSON><PERSON><PERSON> x<PERSON> p<PERSON>"}, "saveChanges": "<PERSON><PERSON><PERSON> thay đổi", "@saveChanges": {"description": "<PERSON><PERSON><PERSON> l<PERSON>u thay đổi"}, "cancelEdit": "<PERSON><PERSON><PERSON>", "@cancelEdit": {"description": "<PERSON><PERSON><PERSON> hủ<PERSON> chỉnh sửa"}, "entryUpdatedSuccessfully": "<PERSON><PERSON> cập nhật phiếu thành công", "@entryUpdatedSuccessfully": {"description": "<PERSON><PERSON><PERSON><PERSON> báo cập nhật phi<PERSON>u thành công"}, "errorUpdatingEntry": "Lỗi khi cập nhật phi<PERSON>u", "@errorUpdatingEntry": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi cập nhật phi<PERSON>u"}, "confirmDeleteEntry": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON>a p<PERSON>u", "@confirmDeleteEntry": {"description": "Ti<PERSON>u đề xác nhận xóa phiếu"}, "deleteEntryMessage": "Bạn có chắc chắn muốn xóa phiếu này?", "@deleteEntryMessage": {"description": "<PERSON><PERSON><PERSON> dung xác nhận xóa phiếu"}, "entryDeletedSuccessfully": "<PERSON><PERSON> xóa phiếu thành công", "@entryDeletedSuccessfully": {"description": "<PERSON><PERSON><PERSON><PERSON> báo xóa phiếu thành công"}, "errorDeletingEntry": "Lỗi khi xóa phiếu", "@errorDeletingEntry": {"description": "<PERSON>h<PERSON>ng báo lỗi xóa phiếu"}, "stockTransactionsScreen": "<PERSON><PERSON><PERSON><PERSON> kho", "@stockTransactionsScreen": {"description": "Ti<PERSON><PERSON> đề màn hình xuất nhập kho"}, "addStockTransactionButton": "<PERSON><PERSON><PERSON><PERSON> phi<PERSON>u xuất nh<PERSON>p kho", "@addStockTransactionButton": {"description": "<PERSON><PERSON><PERSON> thêm phiếu xuất nhập kho"}, "stockInType": "<PERSON><PERSON><PERSON><PERSON> kho", "@stockInType": {"description": "<PERSON><PERSON><PERSON> n<PERSON> kho"}, "stockOutType": "<PERSON><PERSON><PERSON> kho", "@stockOutType": {"description": "<PERSON><PERSON><PERSON> xuất kho"}, "stockAdjustmentType": "<PERSON><PERSON><PERSON>u chỉnh kho", "@stockAdjustmentType": {"description": "<PERSON><PERSON>i điều chỉnh kho"}, "transactionType": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "@transactionType": {"description": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON>i giao d<PERSON>ch"}, "quantity": "Số lượng", "@quantity": {"description": "<PERSON><PERSON>ã<PERSON> s<PERSON> l<PERSON>"}, "reason": "Lý do", "@reason": {"description": "Nhãn lý do"}, "reference": "<PERSON><PERSON>", "@reference": {"description": "<PERSON><PERSON><PERSON><PERSON> tham chi<PERSON>u"}, "noTransactionsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy giao dịch nào", "@noTransactionsFound": {"description": "<PERSON>hông báo không có giao dịch"}, "createFirstTransaction": "<PERSON><PERSON><PERSON> giao dịch đầu tiên để bắt đầu", "@createFirstTransaction": {"description": "<PERSON>ướng dẫn tạo giao dịch đầu tiên"}, "totalTransactions": "<PERSON><PERSON><PERSON> p<PERSON>u", "@totalTransactions": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng số phi<PERSON>u"}, "stockFilters": "<PERSON><PERSON> lọc kho", "@stockFilters": {"description": "<PERSON><PERSON><PERSON><PERSON> đề bộ lọc kho"}, "transactionTypeFilter": "<PERSON><PERSON><PERSON>", "@transactionTypeFilter": {"description": "<PERSON><PERSON><PERSON><PERSON> bộ lọc lo<PERSON> p<PERSON>u"}, "allTransactionTypes": "<PERSON><PERSON><PERSON> c<PERSON>", "@allTransactionTypes": {"description": "L<PERSON><PERSON> chọn tất cả loại phi<PERSON>u"}, "stockDateRange": "<PERSON><PERSON><PERSON><PERSON> thời gian", "@stockDateRange": {"description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ng thời gian kho"}, "stockStartDate": "<PERSON><PERSON> ngày", "@stockStartDate": {"description": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>y b<PERSON><PERSON> đ<PERSON>u kho"}, "stockEndDate": "<PERSON><PERSON><PERSON>", "@stockEndDate": {"description": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>y kết thúc kho"}, "stockQuickFilters": "<PERSON><PERSON><PERSON>", "@stockQuickFilters": {"description": "<PERSON><PERSON><PERSON><PERSON> bộ lọc n<PERSON>h kho"}, "stockToday": "<PERSON><PERSON><PERSON> nay", "@stockToday": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n hôm nay kho"}, "stockThisWeek": "<PERSON><PERSON><PERSON>", "@stockThisWeek": {"description": "<PERSON><PERSON><PERSON> chọn tuần này kho"}, "stockThisMonth": "<PERSON><PERSON><PERSON><PERSON>", "@stockThisMonth": {"description": "<PERSON><PERSON><PERSON> chọn tháng nà<PERSON> kho"}, "stockClearFilters": "Xóa bộ lọc", "@stockClearFilters": {"description": "<PERSON>út x<PERSON>a bộ lọc kho"}, "createStockTransaction": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>u xu<PERSON>t nh<PERSON><PERSON> kho", "@createStockTransaction": {"description": "Tiêu đề tạo phiếu xuất nhập kho"}, "selectTransactionType": "<PERSON><PERSON><PERSON> lo<PERSON>", "@selectTransactionType": {"description": "<PERSON><PERSON><PERSON><PERSON> chọn lo<PERSON>i p<PERSON>u"}, "partnerName": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON>c", "@partnerName": {"description": "<PERSON><PERSON><PERSON><PERSON> tên đ<PERSON>i tác"}, "enterPartnerName": "<PERSON><PERSON><PERSON><PERSON> tên đ<PERSON><PERSON> t<PERSON> (t<PERSON><PERSON> ch<PERSON>n)", "@enterPartnerName": {"description": "Placeholder tê<PERSON> đ<PERSON><PERSON> t<PERSON>c"}, "note": "<PERSON><PERSON><PERSON>", "@note": {"description": "<PERSON><PERSON><PERSON><PERSON> ghi chú"}, "enterNote": "<PERSON><PERSON><PERSON><PERSON> (t<PERSON><PERSON> ch<PERSON>n)", "@enterNote": {"description": "Placeholder g<PERSON> chú"}, "stockProducts": "<PERSON><PERSON><PERSON> p<PERSON>m", "@stockProducts": {"description": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m kho"}, "addStockProduct": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "@addStockProduct": {"description": "<PERSON><PERSON><PERSON> thêm sản phẩm kho"}, "stockProductName": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "@stockProductName": {"description": "<PERSON><PERSON><PERSON><PERSON> tên sản phẩm kho"}, "enterStockProductName": "<PERSON><PERSON><PERSON><PERSON> tên sản phẩm", "@enterStockProductName": {"description": "Placeholder t<PERSON><PERSON> sản ph<PERSON>m kho"}, "unitPrice": "Đơn giá", "@unitPrice": {"description": "<PERSON><PERSON>ãn đơn giá"}, "enterUnitPrice": "<PERSON><PERSON><PERSON><PERSON> đơn giá", "@enterUnitPrice": {"description": "Placeholder <PERSON><PERSON><PERSON> giá"}, "enterQuantity": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "@enterQuantity": {"description": "Placeholder <PERSON><PERSON>"}, "pleaseAddAtLeastOneStockProduct": "<PERSON><PERSON> lòng thêm ít nhất một sản phẩm", "@pleaseAddAtLeastOneStockProduct": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi cần thêm sản phẩm kho"}, "pleaseEnterStockProductName": "<PERSON><PERSON> lòng nhập tên sản phẩm", "@pleaseEnterStockProductName": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi cần nhập tên sản phẩm kho"}, "pleaseEnterValidStockQuantity": "<PERSON><PERSON> lòng nhập số lư<PERSON>ng hợp lệ", "@pleaseEnterValidStockQuantity": {"description": "<PERSON>h<PERSON><PERSON> báo lỗi số lượng không hợp lệ kho"}, "pleaseEnterValidStockPrice": "<PERSON><PERSON> lòng nh<PERSON>p gi<PERSON> hợp lệ", "@pleaseEnterValidStockPrice": {"description": "<PERSON>hông báo lỗi giá không hợp lệ kho"}, "noStockTransactionsYet": "<PERSON><PERSON><PERSON> có phiếu xuất nhập kho", "@noStockTransactionsYet": {"description": "<PERSON><PERSON><PERSON><PERSON> báo chưa có phiếu xuất nhập kho"}, "createFirstStockTransaction": "<PERSON><PERSON><PERSON> phiếu đầu tiên để bắt đầu", "@createFirstStockTransaction": {"description": "Hướng dẫn tạo phiếu đầu tiên"}, "moreItems": "sản ph<PERSON><PERSON>c", "@moreItems": {"description": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m kh<PERSON>c"}, "transactionInfo": "Thông tin phiếu", "@transactionInfo": {"description": "Ti<PERSON>u đề thông tin phiếu"}, "transactionNumber": "<PERSON><PERSON> p<PERSON>", "@transactionNumber": {"description": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> p<PERSON>u"}, "transactionTypeLabel": "<PERSON><PERSON><PERSON>", "@transactionTypeLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON> p<PERSON>u"}, "createdDate": "<PERSON><PERSON><PERSON>", "@createdDate": {"description": "<PERSON><PERSON><PERSON><PERSON> ngày t<PERSON>o"}, "cartWithCount": "Giỏ hàng", "@cartWithCount": {"description": "Ti<PERSON><PERSON> đề giỏ hàng có số lượng"}, "emptyCartMessage": "Giỏ hàng trống", "@emptyCartMessage": {"description": "<PERSON><PERSON><PERSON><PERSON> báo giỏ hàng trống"}, "selectProductsToAddToCart": "<PERSON><PERSON><PERSON> sản phẩm để thêm vào giỏ hàng", "@selectProductsToAddToCart": {"description": "Hướng dẫn chọn sản phẩm"}, "addStockTransactionTitle": "<PERSON><PERSON><PERSON><PERSON> phi<PERSON>u xuất nh<PERSON>p kho", "@addStockTransactionTitle": {"description": "Ti<PERSON><PERSON> đề thêm phiếu xuất nhập kho"}, "saveStockTransaction": "<PERSON><PERSON><PERSON>", "@saveStockTransaction": {"description": "<PERSON><PERSON><PERSON> kho"}, "transactionCreatedSuccessfully": "<PERSON><PERSON><PERSON> xuất nhập kho đã đư<PERSON><PERSON> tạo thành công", "@transactionCreatedSuccessfully": {"description": "<PERSON><PERSON><PERSON><PERSON> báo tạo phiếu thành công"}, "errorCreatingTransaction": "Lỗi khi tạo phiếu", "@errorCreatingTransaction": {"description": "<PERSON>h<PERSON>ng báo lỗi tạo phiếu"}, "stockInRadio": "<PERSON><PERSON><PERSON><PERSON> kho", "@stockInRadio": {"description": "Radio button nhập kho"}, "stockOutRadio": "<PERSON><PERSON><PERSON> kho", "@stockOutRadio": {"description": "Radio button xuất kho"}, "posSearchProducts": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "@posSearchProducts": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm kiếm sản phẩm POS"}, "posSearchProductsHint": "<PERSON><PERSON><PERSON><PERSON>, mã sản phẩm...", "@posSearchProductsHint": {"description": "Placeholder t<PERSON><PERSON> kiếm sản phẩm POS"}, "posProductCode": "Mã", "@posProductCode": {"description": "<PERSON>hãn mã sản phẩm POS"}, "posStockQuantityShort": "SL", "@posStockQuantityShort": {"description": "<PERSON>hãn số lượng tồn kho POS (viết tắt)"}, "posNoProductsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm nào", "@posNoProductsFound": {"description": "<PERSON>hông báo không tìm thấy sản phẩm POS"}, "posNoProductsYet": "<PERSON><PERSON><PERSON> c<PERSON> sản phẩm nào", "@posNoProductsYet": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o chưa có sản phẩm POS"}, "posSubtotal": "<PERSON><PERSON><PERSON>", "@posSubtotal": {"description": "<PERSON><PERSON><PERSON><PERSON> tạm t<PERSON>h POS"}, "posTax": "<PERSON><PERSON><PERSON> (10%)", "@posTax": {"description": "<PERSON><PERSON><PERSON><PERSON> thu<PERSON> POS"}, "posDiscount": "G<PERSON>ảm giá", "@posDiscount": {"description": "Nhãn gi<PERSON>m giá POS"}, "posGrandTotal": "<PERSON><PERSON><PERSON> cộng", "@posGrandTotal": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng cộng POS"}, "posSaveDraft": "<PERSON><PERSON><PERSON>", "@posSaveDraft": {"description": "<PERSON><PERSON><PERSON> l<PERSON> nháp POS"}, "posCheckout": "<PERSON><PERSON> toán", "@posCheckout": {"description": "<PERSON><PERSON><PERSON>h toán POS"}, "posDraftSaved": "<PERSON><PERSON> lưu đơn hàng n<PERSON>p", "@posDraftSaved": {"description": "<PERSON>h<PERSON><PERSON> báo lưu nháp thành công POS"}, "posScanBarcodeTitle": "Quét mã vạch", "@posScanBarcodeTitle": {"description": "Ti<PERSON>u đề quét mã vạch POS"}, "posEnterBarcode": "<PERSON>hậ<PERSON> mã vạch", "@posEnterBarcode": {"description": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>p mã vạch POS"}, "posScanOrEnterBarcode": "<PERSON><PERSON>t hoặc nhập mã sản phẩm...", "@posScanOrEnterBarcode": {"description": "Placeholder quét mã vạch POS"}, "posUseCameraToScan": "Hoặc sử dụng camera để quét mã vạch", "@posUseCameraToScan": {"description": "Hướng dẫn sử dụng camera POS"}, "addStockTransactionScreenTitle": "<PERSON><PERSON><PERSON><PERSON> phi<PERSON>u xuất nh<PERSON>p kho", "@addStockTransactionScreenTitle": {"description": "Tiêu đề màn hình thêm phiếu xuất nhập kho"}, "saveButton": "<PERSON><PERSON><PERSON>", "@saveButton": {"description": "<PERSON><PERSON><PERSON>"}, "cancelButton": "<PERSON><PERSON><PERSON>", "@cancelButton": {"description": "<PERSON><PERSON><PERSON>"}, "saveTransactionButton": "<PERSON><PERSON><PERSON>", "@saveTransactionButton": {"description": "<PERSON><PERSON><PERSON>"}, "stockInLabel": "<PERSON><PERSON><PERSON><PERSON> kho", "@stockInLabel": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> kho"}, "stockOutLabel": "<PERSON><PERSON><PERSON> kho", "@stockOutLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON>t kho"}, "stockTransactionCreatedSuccess": "<PERSON><PERSON><PERSON> xuất nhập kho đã đư<PERSON><PERSON> tạo thành công", "@stockTransactionCreatedSuccess": {"description": "<PERSON><PERSON><PERSON><PERSON> báo tạo phiếu thành công"}, "errorCreatingStockTransaction": "Lỗi khi tạo phiếu", "@errorCreatingStockTransaction": {"description": "<PERSON>h<PERSON>ng báo lỗi tạo phiếu"}, "createStockTransactionDialogTitle": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>u xu<PERSON>t nh<PERSON><PERSON> kho", "@createStockTransactionDialogTitle": {"description": "Tiêu đề dialog tạo phiếu"}, "createTransactionButton": "<PERSON><PERSON><PERSON>", "@createTransactionButton": {"description": "<PERSON><PERSON><PERSON> t<PERSON>o <PERSON>"}, "paymentTitle": "<PERSON><PERSON> toán", "@paymentTitle": {"description": "<PERSON>i<PERSON><PERSON> đề thanh toán"}, "enterAmountReceived": "<PERSON><PERSON><PERSON><PERSON> số tiền nhận", "@enterAmountReceived": {"description": "Placeholder <PERSON><PERSON><PERSON><PERSON> số tiền nhận"}, "paymentFeatureInDevelopment": "<PERSON><PERSON><PERSON> năng thanh toán đang đư<PERSON> phát triển", "@paymentFeatureInDevelopment": {"description": "<PERSON><PERSON><PERSON><PERSON> báo t<PERSON>h năng thanh toán đang phát triển"}, "closeButton": "Đ<PERSON><PERSON>", "@closeButton": {"description": "<PERSON><PERSON><PERSON>"}, "completeButton": "<PERSON><PERSON><PERSON> t<PERSON>t", "@completeButton": {"description": "<PERSON><PERSON><PERSON> ho<PERSON>n tất"}, "orderCreatedSuccess": "<PERSON><PERSON> tạo đơn hàng thành công", "@orderCreatedSuccess": {"description": "<PERSON><PERSON><PERSON><PERSON> báo tạo đơn hàng thành công"}, "demoProduct1": "<PERSON><PERSON><PERSON> ph<PERSON>m demo 1", "@demoProduct1": {"description": "<PERSON><PERSON><PERSON> p<PERSON> demo 1"}, "demoProduct2": "<PERSON><PERSON><PERSON> ph<PERSON>m demo 2", "@demoProduct2": {"description": "<PERSON><PERSON><PERSON> ph<PERSON> demo 2"}, "demoProduct3": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> demo 3", "@demoProduct3": {"description": "<PERSON><PERSON><PERSON> p<PERSON> demo 3"}, "productSectionTitle": "<PERSON><PERSON><PERSON> p<PERSON>m", "@productSectionTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề phần sản phẩm"}, "selectProductLabel": "<PERSON><PERSON><PERSON> sản phẩm *", "@selectProductLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> chọn sản phẩm"}, "pleaseSelectProduct": "<PERSON><PERSON> lòng chọn sản phẩm", "@pleaseSelectProduct": {"description": "<PERSON><PERSON><PERSON><PERSON> báo vui lòng chọn sản phẩm"}, "quantityAndPriceTitle": "Số lượng & Giá", "@quantityAndPriceTitle": {"description": "Tiêu đề số lượng và giá"}, "quantityLabel": "Số lư<PERSON>ng *", "@quantityLabel": {"description": "<PERSON><PERSON>ã<PERSON> s<PERSON> l<PERSON>"}, "unitLabel": "cái", "@unitLabel": {"description": "Đơn vị cái"}, "pleaseEnterQuantity": "<PERSON><PERSON> lòng nhập số lượng", "@pleaseEnterQuantity": {"description": "<PERSON><PERSON><PERSON><PERSON> báo vui lòng nhập số lượng"}, "invalidQuantity": "Số lư<PERSON> không hợp lệ", "@invalidQuantity": {"description": "<PERSON><PERSON><PERSON><PERSON> báo số lượng không hợp lệ"}, "unitPriceLabel": "Đơn giá", "@unitPriceLabel": {"description": "<PERSON><PERSON>ãn đơn giá"}, "invalidUnitPrice": "Đơn gi<PERSON> không hợp lệ", "@invalidUnitPrice": {"description": "<PERSON><PERSON><PERSON><PERSON> báo đơn giá không hợp lệ"}, "additionalInfoTitle": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung", "@additionalInfoTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề thông tin bổ sung"}, "referenceNumberLabel": "<PERSON><PERSON> tham chiếu", "@referenceNumberLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> số tham chiếu"}, "referenceNumberHint": "VD: PO001, SO001", "@referenceNumberHint": {"description": "Gợi ý số tham chiếu"}, "noteLabel": "<PERSON><PERSON><PERSON>", "@noteLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ghi chú"}, "partnerNameLabel": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON>c", "@partnerNameLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tên đ<PERSON>i tác"}, "partnerNameHint": "<PERSON><PERSON><PERSON><PERSON> tên nhà cung cấp hoặc khách hàng", "@partnerNameHint": {"description": "G<PERSON><PERSON> ý tên đối tác"}, "pleaseEnterPartnerName": "<PERSON><PERSON> lòng nhập tên đối tác", "@pleaseEnterPartnerName": {"description": "Th<PERSON>ng báo vui lòng nhập tên đối tác"}, "productListTitle": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "@productListTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề danh sách sản phẩm"}, "addProductButton": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "@addProductButton": {"description": "<PERSON><PERSON><PERSON> thêm sản ph<PERSON>m"}, "noteHint": "<PERSON><PERSON><PERSON><PERSON> ghi chú cho phi<PERSON>u (t<PERSON><PERSON> ch<PERSON>n)", "@noteHint": {"description": "Gợi ý ghi chú"}, "stockInRadioLabel": "<PERSON><PERSON><PERSON><PERSON> kho", "@stockInRadioLabel": {"description": "Nhãn radio nhập kho"}, "stockOutRadioLabel": "<PERSON><PERSON><PERSON> kho", "@stockOutRadioLabel": {"description": "Nhãn radio xuất kho"}, "noProductsYetMessage": "<PERSON><PERSON><PERSON> c<PERSON> sản phẩm nào", "@noProductsYetMessage": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o ch<PERSON>a có sản phẩm"}, "clickAddProductToStart": "<PERSON><PERSON><PERSON><PERSON> \"Thêm sản phẩm\" để bắt đầu", "@clickAddProductToStart": {"description": "Hướng dẫn nhấn thêm sản phẩm"}, "dialogProductNameLabel": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "@dialogProductNameLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tên sản phẩm trong dialog"}, "dialogPleaseEnterProductName": "<PERSON><PERSON> lòng nhập tên sản phẩm", "@dialogPleaseEnterProductName": {"description": "Thông báo vui lòng nhập tên sản phẩm trong dialog"}, "quantityMustBeGreaterThanZero": "Số lượng phải lớn hơn 0", "@quantityMustBeGreaterThanZero": {"description": "<PERSON><PERSON><PERSON><PERSON> báo số lượng phải lớn hơn 0"}, "pleaseEnterUnitPrice": "<PERSON><PERSON> lòng nh<PERSON>p đơn giá", "@pleaseEnterUnitPrice": {"description": "<PERSON><PERSON><PERSON><PERSON> báo vui lòng nhập đơn giá"}, "unitPriceMustBeGreaterThanZero": "Đơn giá phải lớn hơn 0", "@unitPriceMustBeGreaterThanZero": {"description": "<PERSON><PERSON><PERSON><PERSON> báo đơn giá phải lớn hơn 0"}, "pleaseAddAtLeastOneProduct": "<PERSON><PERSON> lòng thêm ít nhất một sản phẩm", "@pleaseAddAtLeastOneProduct": {"description": "Thông báo vui lòng thêm ít nhất một sản phẩm"}, "inventoryNoProductsYet": "<PERSON><PERSON><PERSON> c<PERSON> sản phẩm nào", "@inventoryNoProductsYet": {"description": "<PERSON>h<PERSON><PERSON> b<PERSON>o chưa có sản phẩm trong inventory"}, "inventoryAddFirstProduct": "<PERSON><PERSON><PERSON> thêm sản phẩm đầu tiên của bạn", "@inventoryAddFirstProduct": {"description": "Hướng dẫn thêm sản phẩm đầu tiên trong inventory"}, "partnerManagement": "<PERSON><PERSON><PERSON><PERSON> lý đối tác", "@partnerManagement": {"description": "Tiêu đề quản lý đối tác"}, "addPartnerTooltip": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>i tác", "@addPartnerTooltip": {"description": "<PERSON><PERSON><PERSON> thêm đối tác"}, "refreshTooltip": "<PERSON><PERSON><PERSON>", "@refreshTooltip": {"description": "<PERSON><PERSON><PERSON> làm mới"}, "totalPartners": "<PERSON><PERSON><PERSON> đối tác", "@totalPartners": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng đối tác"}, "activePartners": "<PERSON><PERSON><PERSON> đ<PERSON>", "@activePartners": {"description": "<PERSON><PERSON><PERSON><PERSON> đối tác hoạt động"}, "customersLabel": "<PERSON><PERSON><PERSON><PERSON>", "@customersLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng"}, "suppliersLabel": "<PERSON><PERSON><PERSON> cung cấp", "@suppliersLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> nhà cung cấp"}, "searchPartners": "<PERSON><PERSON><PERSON> kiếm đối tác", "@searchPartners": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm kiếm đối tác"}, "searchPartnerHint": "<PERSON><PERSON><PERSON><PERSON> tê<PERSON>, mã, số điện thoại...", "@searchPartnerHint": {"description": "G<PERSON><PERSON> ý tìm kiếm đối tác"}, "noPartnersFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đối tác nào", "@noPartnersFound": {"description": "<PERSON>hông báo không tìm thấy đối tác"}, "noPartnersYet": "Chưa có đối tác nào", "@noPartnersYet": {"description": "<PERSON><PERSON><PERSON><PERSON> báo chưa có đối tác"}, "tryDifferentKeywords": "Th<PERSON> tìm kiếm với từ khóa khác", "@tryDifferentKeywords": {"description": "<PERSON>ợ<PERSON> ý thử từ khóa khác"}, "addFirstPartner": "Thê<PERSON> đối tác đầu tiên để bắt đầu", "@addFirstPartner": {"description": "Hướng dẫn thêm đối tác đầu tiên"}, "addPartnerButton": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>i tác", "@addPartnerButton": {"description": "<PERSON><PERSON><PERSON> thêm đối tác"}, "posMenuTitle": "<PERSON><PERSON> (POS)", "@posMenuTitle": {"description": "Tiêu đề menu bán hàng P<PERSON>"}, "notificationsMenuTitle": "<PERSON><PERSON><PERSON><PERSON> báo", "@notificationsMenuTitle": {"description": "Tiêu đề menu thông báo"}, "stockTransactionsMenuTitle": "<PERSON><PERSON><PERSON><PERSON> kho", "@stockTransactionsMenuTitle": {"description": "Tiêu đề menu xuất nhập kho"}, "transactionNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phi<PERSON>u", "@transactionNotFound": {"description": "<PERSON>hông báo không tìm thấy phiếu"}, "transactionDetails": "<PERSON> tiết p<PERSON>u", "@transactionDetails": {"description": "Ti<PERSON>u đề chi tiết phiếu"}, "transactionEditTooltip": "Chỉnh sửa", "@transactionEditTooltip": {"description": "Toolt<PERSON> chỉnh sửa phi<PERSON>u"}, "transactionSaveChanges": "<PERSON><PERSON><PERSON>", "@transactionSaveChanges": {"description": "<PERSON><PERSON><PERSON> lưu thay đổi phi<PERSON>u"}, "deleteTransactionButton": "<PERSON><PERSON><PERSON>", "@deleteTransactionButton": {"description": "<PERSON><PERSON><PERSON> x<PERSON> p<PERSON>"}, "confirmDeleteTransaction": "<PERSON><PERSON><PERSON>n x<PERSON>a", "@confirmDeleteTransaction": {"description": "Ti<PERSON>u đề xác nhận xóa phiếu"}, "deleteTransactionMessage": "Bạn có chắc chắn muốn xóa phiếu", "@deleteTransactionMessage": {"description": "<PERSON><PERSON><PERSON> dung xác nhận xóa phiếu"}, "deleteButton": "Xóa", "@deleteButton": {"description": "Nút x<PERSON>"}, "systemErrorOccurred": "<PERSON><PERSON> lỗi xảy ra", "@systemErrorOccurred": {"description": "<PERSON><PERSON><PERSON><PERSON> báo có lỗi xảy ra hệ thống"}, "tryAgainButton": "<PERSON><PERSON><PERSON> lại", "@tryAgainButton": {"description": "<PERSON><PERSON><PERSON> thử lại"}, "partnerLabel": "<PERSON><PERSON><PERSON>", "@partnerLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> tác"}, "createdBy": "<PERSON><PERSON><PERSON><PERSON> tạo", "@createdBy": {"description": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> tạo"}, "noteFieldLabel": "<PERSON><PERSON><PERSON>", "@noteFieldLabel": {"description": "Nhãn trư<PERSON><PERSON> ghi chú"}, "enterNoteHint": "<PERSON><PERSON><PERSON><PERSON> ghi chú...", "@enterNoteHint": {"description": "<PERSON><PERSON><PERSON> ý nhập ghi chú"}, "noNoteAvailable": "<PERSON><PERSON><PERSON><PERSON> có ghi chú", "@noNoteAvailable": {"description": "Thông báo không có ghi chú"}, "productListLabel": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "@productListLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm"}, "productCountLabel": "S<PERSON> l<PERSON> sản phẩm", "@productCountLabel": {"description": "<PERSON><PERSON>ã<PERSON> số lượng sản phẩm"}, "totalQuantityLabel": "Tổng số lượng", "@totalQuantityLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng số lượng"}, "totalValueLabel": "Tổng giá trị", "@totalValueLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng giá trị"}, "summaryLabel": "Tổng kết", "@summaryLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng kết"}, "cancelEditButton": "<PERSON><PERSON><PERSON>", "@cancelEditButton": {"description": "<PERSON><PERSON><PERSON> hủ<PERSON> chỉnh sửa"}, "saveChangesButton": "<PERSON><PERSON><PERSON> thay đổi", "@saveChangesButton": {"description": "<PERSON><PERSON><PERSON> l<PERSON>u thay đổi"}, "transactionUpdatedSuccess": "<PERSON><PERSON> cập nhật phiếu thành công", "@transactionUpdatedSuccess": {"description": "<PERSON><PERSON><PERSON><PERSON> báo cập nhật phi<PERSON>u thành công"}, "errorUpdatingTransaction": "Lỗi khi cập nhật phi<PERSON>u", "@errorUpdatingTransaction": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi cập nhật phi<PERSON>u"}, "transactionDeletedSuccess": "<PERSON><PERSON> xóa phiếu thành công", "@transactionDeletedSuccess": {"description": "<PERSON><PERSON><PERSON><PERSON> báo xóa phiếu thành công"}, "errorDeletingTransaction": "Lỗi khi xóa phiếu", "@errorDeletingTransaction": {"description": "<PERSON>h<PERSON>ng báo lỗi xóa phiếu"}, "addTransactionTitle": "<PERSON><PERSON><PERSON><PERSON> giao d<PERSON>ch", "@addTransactionTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề thêm giao dịch"}, "noTransactionsYet": "Chưa có giao dịch nào", "@noTransactionsYet": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o chưa có giao dịch"}, "addFirstTransaction": "<PERSON><PERSON><PERSON><PERSON> giao dịch đầu tiên để bắt đầu", "@addFirstTransaction": {"description": "<PERSON><PERSON><PERSON>ng dẫn thêm giao dịch đầu tiên"}, "noChartData": "Chưa có dữ liệu biểu đồ", "@noChartData": {"description": "<PERSON><PERSON><PERSON><PERSON> báo chưa có dữ liệu biểu đồ"}, "addTransactionForChart": "<PERSON><PERSON><PERSON><PERSON> giao dịch để xem biểu đồ dòng tiền", "@addTransactionForChart": {"description": "Hướng dẫn thêm giao dịch để xem biểu đồ"}, "financialOverview": "<PERSON><PERSON><PERSON> quan tài ch<PERSON>h", "@financialOverview": {"description": "Ti<PERSON><PERSON> đề tổng quan tài ch<PERSON>h"}, "searchCategoriesHint": "<PERSON><PERSON><PERSON> kiếm danh mục...", "@searchCategoriesHint": {"description": "G<PERSON><PERSON> ý tìm kiếm danh mục"}, "transactionDetailsTitle": "<PERSON> tiết giao dịch", "@transactionDetailsTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề chi tiết giao dịch"}, "descriptionLabel": "<PERSON><PERSON>", "@descriptionLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> mô tả"}, "typeLabel": "<PERSON><PERSON><PERSON>", "@typeLabel": {"description": "<PERSON><PERSON><PERSON><PERSON>"}, "categoryLabel": "<PERSON><PERSON>", "@categoryLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> danh mục"}, "amountLabel": "<PERSON><PERSON> tiền", "@amountLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> số tiền"}, "incomeTypeLabel": "<PERSON>hu", "@incomeTypeLabel": {"description": "<PERSON><PERSON><PERSON> thu nhập"}, "expenseTypeLabel": "<PERSON>", "@expenseTypeLabel": {"description": "<PERSON><PERSON>i chi tiêu"}, "orderDetailsLabel": "<PERSON> ti<PERSON>", "@orderDetailsLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> chi tiết đơn hàng"}, "subtotalLabel": "<PERSON><PERSON><PERSON>", "@subtotalLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tạm t<PERSON>h"}, "createNewButton": "<PERSON><PERSON><PERSON> mới", "@createNewButton": {"description": "<PERSON><PERSON><PERSON> tạo mới"}, "editButton": "Chỉnh sửa", "@editButton": {"description": "<PERSON><PERSON><PERSON> chỉnh sửa"}, "viewDetails": "<PERSON>em chi tiết", "@viewDetails": {"description": "<PERSON><PERSON><PERSON> xem chi tiết"}, "copy": "Sao chép", "@copy": {"description": "Nút sao chép"}, "share": "<PERSON><PERSON> sẻ", "@share": {"description": "<PERSON><PERSON><PERSON> chia sẻ"}, "print": "In", "@print": {"description": "<PERSON><PERSON><PERSON> in"}, "export": "<PERSON><PERSON><PERSON>", "@export": {"description": "<PERSON><PERSON><PERSON>"}, "import": "<PERSON><PERSON><PERSON><PERSON>", "@import": {"description": "<PERSON><PERSON><PERSON>"}, "upload": "<PERSON><PERSON><PERSON>", "@upload": {"description": "<PERSON><PERSON><PERSON> lên"}, "download": "<PERSON><PERSON><PERSON>", "@download": {"description": "<PERSON><PERSON><PERSON> t<PERSON>"}, "sync": "<PERSON><PERSON><PERSON> bộ", "@sync": {"description": "<PERSON><PERSON><PERSON> đồng bộ"}, "refreshButton": "<PERSON><PERSON><PERSON>", "@refreshButton": {"description": "<PERSON><PERSON><PERSON> làm mới"}, "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "@restore": {"description": "<PERSON><PERSON><PERSON> ph<PERSON>c"}, "backup": "<PERSON><PERSON> lư<PERSON>", "@backup": {"description": "Nút sao lưu"}, "options": "<PERSON><PERSON><PERSON>", "@options": {"description": "<PERSON><PERSON><PERSON> t<PERSON>n"}, "filtersButton": "<PERSON><PERSON> lọc", "@filtersButton": {"description": "<PERSON><PERSON>t bộ lọc"}, "sortButton": "<PERSON><PERSON><PERSON>p", "@sortButton": {"description": "<PERSON><PERSON><PERSON> xếp"}, "searchButton": "<PERSON><PERSON><PERSON>", "@searchButton": {"description": "<PERSON><PERSON><PERSON> t<PERSON> k<PERSON>"}, "results": "<PERSON><PERSON><PERSON> qu<PERSON>", "@results": {"description": "<PERSON><PERSON><PERSON><PERSON> kết quả"}, "page": "<PERSON><PERSON>", "@page": {"description": "<PERSON><PERSON><PERSON><PERSON> trang"}, "next": "<PERSON><PERSON><PERSON><PERSON> theo", "@next": {"description": "<PERSON><PERSON><PERSON> ti<PERSON> theo"}, "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON> đó", "@previous": {"description": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> đó"}, "first": "<PERSON><PERSON><PERSON> tiên", "@first": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>u tiên"}, "last": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "@last": {"description": "<PERSON><PERSON><PERSON> cu<PERSON>i c<PERSON>ng"}, "all": "<PERSON><PERSON><PERSON> c<PERSON>", "@all": {"description": "<PERSON><PERSON><PERSON> chọn tất cả trong bộ lọc"}, "none": "<PERSON><PERSON><PERSON><PERSON> có", "@none": {"description": "<PERSON><PERSON><PERSON> ch<PERSON>n không có"}, "yes": "<PERSON><PERSON>", "@yes": {"description": "<PERSON><PERSON><PERSON> có"}, "no": "K<PERSON>ô<PERSON>", "@no": {"description": "<PERSON><PERSON><PERSON>h<PERSON>ng"}, "trueValue": "<PERSON><PERSON><PERSON>", "@trueValue": {"description": "<PERSON><PERSON><PERSON> trị đúng"}, "falseValue": "<PERSON>", "@falseValue": {"description": "G<PERSON>á trị sai"}, "on": "<PERSON><PERSON><PERSON>", "@on": {"description": "<PERSON><PERSON><PERSON><PERSON> thái bật"}, "off": "Tắt", "@off": {"description": "<PERSON>r<PERSON><PERSON> thái tắt"}, "invoiceTitle": "<PERSON><PERSON><PERSON>", "@invoiceTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề hóa đơn"}, "salesInvoice": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "@salesInvoice": {"description": "<PERSON><PERSON><PERSON> b<PERSON> hàng"}, "invoiceNumber": "Số", "@invoiceNumber": {"description": "S<PERSON> hóa đơn"}, "dateLabel": "<PERSON><PERSON><PERSON>", "@dateLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>"}, "taxLabel": "<PERSON><PERSON><PERSON>", "@taxLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> thuế"}, "discountLabel": "G<PERSON>ảm giá", "@discountLabel": {"description": "<PERSON>hãn gi<PERSON>m giá"}, "totalAmountLabel": "<PERSON><PERSON><PERSON> cộng", "@totalAmountLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng cộng"}, "paymentMethodLabel": "<PERSON><PERSON> toán", "@paymentMethodLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức thanh toán"}, "thankYouMessage": "<PERSON><PERSON>m ơn quý khách!", "@thankYouMessage": {"description": "<PERSON><PERSON><PERSON> cảm ơn kh<PERSON>ch hàng"}, "closeDialogButton": "Đ<PERSON><PERSON>", "@closeDialogButton": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>g dialog"}, "printInvoiceTitle": "In hóa đơn", "@printInvoiceTitle": {"description": "<PERSON>i<PERSON><PERSON> đề in hóa đơn"}, "printInvoiceQuestion": "Bạn có muốn in hóa đơn cho đơn hàng này không?", "@printInvoiceQuestion": {"description": "Câu hỏi in hóa đơn"}, "noButton": "K<PERSON>ô<PERSON>", "@noButton": {"description": "<PERSON><PERSON><PERSON>"}, "printInvoiceButton": "In hóa đơn", "@printInvoiceButton": {"description": "<PERSON><PERSON>t in hóa đơn"}, "confirmationTitle": "<PERSON><PERSON><PERSON>", "@confirmationTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề xác n<PERSON>n"}, "clearCartConfirmation": "Bạn có chắc chắn muốn xóa tất cả sản phẩm trong giỏ hàng?", "@clearCartConfirmation": {"description": "<PERSON><PERSON><PERSON> nhận xóa giỏ hàng"}, "paymentProcessingError": "Lỗi xử lý thanh toán", "@paymentProcessingError": {"description": "Lỗi xử lý thanh toán"}, "invoiceDetailTitle": "<PERSON> tiết hóa đơn", "@invoiceDetailTitle": {"description": "Ti<PERSON><PERSON> đề chi tiết hóa đơn"}, "invoiceInformation": "<PERSON>h<PERSON><PERSON> tin hóa đơn", "@invoiceInformation": {"description": "<PERSON>h<PERSON><PERSON> tin hóa đơn"}, "invoiceNumberLabel": "S<PERSON> hóa đơn", "@invoiceNumberLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> số hóa đơn"}, "createdDateLabel": "<PERSON><PERSON><PERSON>", "@createdDateLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ngày t<PERSON>o"}, "statusLabel": "<PERSON><PERSON><PERSON><PERSON> thái", "@statusLabel": {"description": "<PERSON><PERSON>ãn trạng thái"}, "createdByLabel": "<PERSON><PERSON><PERSON><PERSON> tạo", "@createdByLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> tạo"}, "notesLabel": "<PERSON><PERSON><PERSON>", "@notesLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ghi chú"}, "cashPayment": "Tiền mặt", "@cashPayment": {"description": "<PERSON><PERSON> toán tiền mặt"}, "creditCardPayment": "Thẻ tín dụng", "@creditCardPayment": {"description": "<PERSON>h toán thẻ tín dụng"}, "bankTransferPayment": "<PERSON><PERSON><PERSON><PERSON>", "@bankTransferPayment": {"description": "<PERSON><PERSON> <PERSON><PERSON> chuyể<PERSON> k<PERSON>n"}, "eWalletPayment": "<PERSON><PERSON> điện tử", "@eWalletPayment": {"description": "<PERSON><PERSON> toán ví điện tử"}, "otherPayment": "K<PERSON><PERSON><PERSON>", "@otherPayment": {"description": "<PERSON><PERSON>"}, "paidStatus": "<PERSON><PERSON> thanh toán", "@paidStatus": {"description": "Tr<PERSON>ng thái đã thanh toán"}, "unpaidStatus": "<PERSON><PERSON><PERSON> to<PERSON>", "@unpaidStatus": {"description": "<PERSON>r<PERSON><PERSON> thái ch<PERSON>a thanh toán"}, "processingStatus": "<PERSON><PERSON> lý", "@processingStatus": {"description": "<PERSON>r<PERSON><PERSON> thái đang xử lý"}, "cancelledStatus": "<PERSON><PERSON> hủy", "@cancelledStatus": {"description": "<PERSON>r<PERSON><PERSON> thái đã hủy"}, "completedStatus": "<PERSON><PERSON><PERSON> th<PERSON>", "@completedStatus": {"description": "<PERSON>r<PERSON><PERSON> thái hoàn thành"}, "pendingConfirmationStatus": "<PERSON>ờ x<PERSON>c n<PERSON>n", "@pendingConfirmationStatus": {"description": "<PERSON>r<PERSON><PERSON> thái chờ xác nh<PERSON>n"}, "confirmedStatus": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "@confirmedStatus": {"description": "<PERSON>r<PERSON>ng thái đã xác n<PERSON>n"}, "shippingStatus": "<PERSON><PERSON> giao hàng", "@shippingStatus": {"description": "<PERSON>r<PERSON><PERSON> thái đang giao hàng"}, "deliveredStatus": "Đ<PERSON> giao hàng", "@deliveredStatus": {"description": "<PERSON>r<PERSON>ng thái đã giao hàng"}, "returnedStatus": "<PERSON><PERSON><PERSON> h<PERSON>", "@returnedStatus": {"description": "<PERSON>r<PERSON><PERSON> thái trả hàng"}, "refundedStatus": "<PERSON><PERSON><PERSON> ti<PERSON>n", "@refundedStatus": {"description": "<PERSON>r<PERSON><PERSON> thái hoàn tiền"}, "errorStatus": "Lỗi", "@errorStatus": {"description": "Trạng thái lỗi"}, "successStatus": "<PERSON><PERSON><PERSON><PERSON> công", "@successStatus": {"description": "<PERSON>r<PERSON><PERSON> thái thành công"}, "warningStatus": "<PERSON><PERSON><PERSON> b<PERSON>o", "@warningStatus": {"description": "<PERSON><PERSON><PERSON><PERSON> thái cảnh báo"}, "infoStatus": "Thông tin", "@infoStatus": {"description": "Tr<PERSON>ng thái thông tin"}, "confirmStatus": "<PERSON><PERSON><PERSON>", "@confirmStatus": {"description": "<PERSON>r<PERSON><PERSON> thái x<PERSON>c <PERSON>n"}, "rejectStatus": "<PERSON><PERSON> chối", "@rejectStatus": {"description": "<PERSON>r<PERSON><PERSON> thái từ chối"}, "showLabel": "<PERSON><PERSON><PERSON> thị", "@showLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> hiển thị"}, "hideLabel": "Ẩn", "@hideLabel": {"description": "<PERSON><PERSON><PERSON><PERSON>"}, "openLabel": "Mở", "@openLabel": {"description": "Nhãn mở"}, "startLabel": "<PERSON><PERSON><PERSON> đ<PERSON>u", "@startLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "endLabel": "<PERSON><PERSON><PERSON>", "@endLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> kết th<PERSON>c"}, "pauseLabel": "<PERSON><PERSON><PERSON>", "@pauseLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tạm dừng"}, "continueLabel": "<PERSON><PERSON><PERSON><PERSON>", "@continueLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON> t<PERSON>c"}, "stopLabel": "Dừng", "@stopLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>ng"}, "restartLabel": "Khởi động lại", "@restartLabel": {"description": "Nhãn khởi động lại"}, "updateLabel": "<PERSON><PERSON><PERSON>", "@updateLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> cập nh<PERSON>t"}, "reloadLabel": "<PERSON><PERSON><PERSON> l<PERSON>i", "@reloadLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tải lại"}, "pasteLabel": "Dán", "@pasteLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> dán"}, "cutLabel": "Cắt", "@cutLabel": {"description": "<PERSON><PERSON><PERSON><PERSON>"}, "undoLabel": "<PERSON><PERSON><PERSON>", "@undoLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>n t<PERSON>c"}, "redoLabel": "<PERSON><PERSON><PERSON>", "@redoLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> làm lại"}, "groupLabel": "Nhóm", "@groupLabel": {"description": "<PERSON>hã<PERSON>"}, "classifyLabel": "<PERSON><PERSON> lo<PERSON>", "@classifyLabel": {"description": "Nhãn phân loại"}, "tagLabel": "Thẻ", "@tagLabel": {"description": "Nhãn thẻ"}, "labelLabel": "<PERSON><PERSON>ã<PERSON>", "@labelLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> nhãn"}, "colorLabel": "<PERSON><PERSON><PERSON>", "@colorLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>c"}, "sizeLabel": "<PERSON><PERSON><PERSON>", "@sizeLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "weightLabel": "<PERSON><PERSON><PERSON><PERSON>", "@weightLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> trọng l<PERSON>"}, "heightLabel": "<PERSON><PERSON><PERSON> cao", "@heightLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> chi<PERSON>u cao"}, "widthLabel": "<PERSON><PERSON><PERSON> r<PERSON>", "@widthLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> chi<PERSON>u rộng"}, "lengthLabel": "<PERSON><PERSON> dài", "@lengthLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> độ dài"}, "areaLabel": "<PERSON><PERSON><PERSON> t<PERSON>ch", "@areaLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tích"}, "volumeLabel": "<PERSON><PERSON><PERSON> tích", "@volumeLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> thể tích"}, "quantityFieldLabel": "Số lượng", "@quantityFieldLabel": {"description": "<PERSON>hãn trường số lượng"}, "unitPriceFieldLabel": "Đơn giá", "@unitPriceFieldLabel": {"description": "Nhãn trường đơn giá"}, "totalPriceLabel": "<PERSON><PERSON><PERSON><PERSON> tiền", "@totalPriceLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> thành tiền"}, "grandTotalLabel": "<PERSON><PERSON><PERSON> tiền", "@grandTotalLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng tiền"}, "changeLabel": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i", "@changeLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tiền thối"}, "receivedLabel": "<PERSON><PERSON><PERSON><PERSON>n", "@receivedLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tiền nhận"}, "feeLabel": "Phí", "@feeLabel": {"description": "Nhãn phí"}, "promotionLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "@promotionLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n mãi"}, "voucherLabel": "Voucher", "@voucherLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> voucher"}, "discountCodeLabel": "Mã giảm giá", "@discountCodeLabel": {"description": "Nhãn mã giảm giá"}, "loyaltyPointsLabel": "<PERSON><PERSON><PERSON><PERSON> thưởng", "@loyaltyPointsLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> điểm thưởng"}, "accumulateLabel": "<PERSON><PERSON><PERSON>", "@accumulateLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ch l<PERSON>"}, "exchangeLabel": "<PERSON><PERSON>", "@exchangeLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> quy đổi"}, "convertLabel": "<PERSON><PERSON><PERSON><PERSON> đổi", "@convertLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> chuyển đổi"}, "exchangeRateLabel": "Tỷ giá", "@exchangeRateLabel": {"description": "Nhãn tỷ giá"}, "unitFieldLabel": "Đơn vị", "@unitFieldLabel": {"description": "<PERSON>hãn trường đơn vị"}, "packageLabel": "<PERSON><PERSON><PERSON>", "@packageLabel": {"description": "<PERSON><PERSON><PERSON><PERSON>"}, "boxLabel": "<PERSON><PERSON><PERSON>", "@boxLabel": {"description": "<PERSON><PERSON><PERSON><PERSON>"}, "cartonLabel": "<PERSON><PERSON><PERSON><PERSON>", "@cartonLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>ng"}, "bagLabel": "Bao", "@bagLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> bao"}, "bottleLabel": "<PERSON><PERSON>", "@bottleLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> chai"}, "jarLabel": "Lọ", "@jarLabel": {"description": "<PERSON><PERSON>ã<PERSON> l<PERSON>"}, "sackLabel": "<PERSON><PERSON><PERSON>", "@sackLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> túi"}, "pieceLabel": "C<PERSON>i", "@pieceLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>i"}, "itemLabel": "<PERSON><PERSON><PERSON>", "@itemLabel": {"description": "<PERSON><PERSON><PERSON><PERSON>"}, "setLabel": "Bộ", "@setLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> bộ"}, "pairLabel": "<PERSON><PERSON><PERSON>", "@pairLabel": {"description": "<PERSON><PERSON><PERSON><PERSON>"}, "coupleLabel": "Cặp", "@coupleLabel": {"description": "Nhãn cặp"}, "kilogramLabel": "Kg", "@kilogramLabel": {"description": "Nhãn kilogram"}, "gramLabel": "Gram", "@gramLabel": {"description": "Nhãn gram"}, "literLabel": "<PERSON><PERSON><PERSON>", "@literLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "milliliterLabel": "Ml", "@milliliterLabel": {"description": "Nhãn milliliter"}, "meterLabel": "<PERSON><PERSON><PERSON>", "@meterLabel": {"description": "<PERSON><PERSON><PERSON><PERSON>"}, "centimeterLabel": "Cm", "@centimeterLabel": {"description": "Nhãn centimeter"}, "millimeterLabel": "Mm", "@millimeterLabel": {"description": "Nhãn millimeter"}, "inchLabel": "Inch", "@inchLabel": {"description": "Nhãn inch"}, "feetLabel": "Feet", "@feetLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> feet"}, "yardLabel": "Yard", "@yardLabel": {"description": "Nhãn yard"}, "customerNameNote": "<PERSON><PERSON><PERSON><PERSON>", "@customerNameNote": {"description": "<PERSON><PERSON> chú tên khách hàng"}, "customerPhoneNote": "SĐT", "@customerPhoneNote": {"description": "<PERSON><PERSON> chú số điện thoại khách hàng"}, "salesScreenTitle": "<PERSON><PERSON>", "@salesScreenTitle": {"description": "<PERSON>i<PERSON><PERSON> đề màn hình bán hàng"}, "newSaleButton": "<PERSON><PERSON> hàng mới", "@newSaleButton": {"description": "<PERSON><PERSON><PERSON> b<PERSON> hàng mới"}, "salesAnalytics": "<PERSON><PERSON> tích b<PERSON> hàng", "@salesAnalytics": {"description": "<PERSON><PERSON> tích b<PERSON> hàng"}, "ordersListTitle": "<PERSON><PERSON> s<PERSON>ch đơn hàng", "@ordersListTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề danh sách đơn hàng"}, "orderDetailsTitle": "<PERSON> tiết đơn hàng", "@orderDetailsTitle": {"description": "Ti<PERSON><PERSON> đề chi tiết đơn hàng"}, "orderNumber": "<PERSON><PERSON> đơn hàng", "@orderNumber": {"description": "<PERSON><PERSON> đơn hàng"}, "orderStatusField": "<PERSON><PERSON><PERSON><PERSON> thái đơn hàng", "@orderStatusField": {"description": "Tr<PERSON><PERSON><PERSON> trạng thái đơn hàng"}, "orderDate": "<PERSON><PERSON><PERSON> đặt hàng", "@orderDate": {"description": "<PERSON><PERSON><PERSON> đặt hàng"}, "orderTime": "<PERSON><PERSON><PERSON><PERSON> gian đặt hàng", "@orderTime": {"description": "<PERSON><PERSON><PERSON><PERSON> gian đặt hàng"}, "customerInfo": "Thông tin khách hàng", "@customerInfo": {"description": "Thông tin khách hàng"}, "customerNameField": "<PERSON><PERSON><PERSON> h<PERSON>ng", "@customerNameField": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> tên kh<PERSON>ch hàng"}, "customerPhoneField": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "@customerPhoneField": {"description": "<PERSON>r<PERSON><PERSON><PERSON> số điện thoại"}, "customerAddressField": "Địa chỉ", "@customerAddressField": {"description": "Trường địa chỉ"}, "orderNotesField": "<PERSON><PERSON> chú đơn hàng", "@orderNotesField": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> ghi chú đơn hàng"}, "paymentMethodField": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "@paymentMethodField": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức thanh toán"}, "amountPaidField": "Số tiền đã trả", "@amountPaidField": {"description": "Trường số tiền đã trả"}, "changeAmountField": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i", "@changeAmountField": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiền thối"}, "vatAmountField": "Thuế VAT", "@vatAmountField": {"description": "Trư<PERSON>ng thuế VAT"}, "discountAmountField": "Số tiền giảm giá", "@discountAmountField": {"description": "Tr<PERSON><PERSON>ng số tiền giảm giá"}, "promotionCodeField": "<PERSON><PERSON> k<PERSON>ến mãi", "@promotionCodeField": {"description": "Trường mã khuyến mãi"}, "loyaltyPointsField": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ch <PERSON>", "@loyaltyPointsField": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> điểm tích l<PERSON>y"}, "membershipField": "<PERSON><PERSON><PERSON><PERSON> viên", "@membershipField": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> thành viên"}, "guestCustomer": "<PERSON><PERSON><PERSON><PERSON> vãng lai", "@guestCustomer": {"description": "<PERSON><PERSON><PERSON><PERSON> vãng lai"}, "orderStatusPaid": "<PERSON><PERSON> thanh toán", "@orderStatusPaid": {"description": "Tr<PERSON>ng thái đã thanh toán"}, "orderStatusUnpaid": "<PERSON><PERSON><PERSON> to<PERSON>", "@orderStatusUnpaid": {"description": "<PERSON>r<PERSON><PERSON> thái ch<PERSON>a thanh toán"}, "orderStatusProcessing": "<PERSON><PERSON> lý", "@orderStatusProcessing": {"description": "<PERSON>r<PERSON><PERSON> thái đang xử lý"}, "orderStatusCompleted": "<PERSON><PERSON><PERSON> th<PERSON>", "@orderStatusCompleted": {"description": "<PERSON>r<PERSON><PERSON> thái hoàn thành"}, "orderStatusCancelled": "<PERSON><PERSON> hủy", "@orderStatusCancelled": {"description": "<PERSON>r<PERSON><PERSON> thái đã hủy"}, "orderStatusReturned": "<PERSON><PERSON><PERSON> h<PERSON>", "@orderStatusReturned": {"description": "<PERSON>r<PERSON><PERSON> thái trả hàng"}, "orderStatusRefunded": "<PERSON><PERSON><PERSON> ti<PERSON>n", "@orderStatusRefunded": {"description": "<PERSON>r<PERSON><PERSON> thái hoàn tiền"}, "printInvoiceAction": "In hóa đơn", "@printInvoiceAction": {"description": "<PERSON><PERSON><PERSON> động in hóa đơn"}, "sendInvoiceAction": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>n", "@sendInvoiceAction": {"description": "<PERSON><PERSON><PERSON> động gửi hóa đơn"}, "saveOrderAction": "<PERSON><PERSON><PERSON> hàng", "@saveOrderAction": {"description": "<PERSON><PERSON><PERSON> động lưu đơn hàng"}, "cancelOrderAction": "<PERSON><PERSON><PERSON> đơn hàng", "@cancelOrderAction": {"description": "<PERSON><PERSON><PERSON> động hủy đơn hàng"}, "confirmPaymentAction": "<PERSON><PERSON><PERSON>n <PERSON>h to<PERSON>", "@confirmPaymentAction": {"description": "<PERSON><PERSON><PERSON> động xác nhận thanh toán"}, "undoAction": "<PERSON><PERSON><PERSON>", "@undoAction": {"description": "<PERSON><PERSON><PERSON> động hoàn tác"}, "refreshAction": "<PERSON><PERSON><PERSON>", "@refreshAction": {"description": "<PERSON><PERSON><PERSON> động làm mới"}, "reloadAction": "<PERSON><PERSON><PERSON> l<PERSON>i", "@reloadAction": {"description": "<PERSON><PERSON><PERSON> động tải lại"}, "syncAction": "<PERSON><PERSON><PERSON> bộ", "@syncAction": {"description": "<PERSON><PERSON><PERSON> động đồng bộ"}, "backupAction": "<PERSON><PERSON> lư<PERSON>", "@backupAction": {"description": "<PERSON><PERSON><PERSON> động sao lưu"}, "restoreAction": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "@restoreAction": {"description": "<PERSON><PERSON><PERSON> động khôi phục"}, "searchProductsHint": "<PERSON><PERSON><PERSON> k<PERSON>m sản phẩm", "@searchProductsHint": {"description": "<PERSON><PERSON><PERSON> ý tìm kiếm sản phẩm"}, "scanBarcodeAction": "Quét mã vạch", "@scanBarcodeAction": {"description": "<PERSON><PERSON><PERSON> động quét mã vạch"}, "addToCartAction": "Thêm vào giỏ", "@addToCartAction": {"description": "<PERSON><PERSON><PERSON> động thêm vào giỏ"}, "removeFromCartAction": "Xóa khỏi giỏ", "@removeFromCartAction": {"description": "<PERSON><PERSON><PERSON> động xóa khỏi giỏ"}, "updateQuantityAction": "<PERSON><PERSON><PERSON> nh<PERSON>t số lư<PERSON>", "@updateQuantityAction": {"description": "<PERSON><PERSON><PERSON> đ<PERSON> cập nhật số lượng"}, "clearAllCartAction": "<PERSON><PERSON><PERSON> tất cả", "@clearAllCartAction": {"description": "<PERSON><PERSON><PERSON> động xóa tất cả giỏ hàng"}, "continueShoppingAction": "<PERSON><PERSON><PERSON><PERSON> tụ<PERSON> mua hàng", "@continueShoppingAction": {"description": "<PERSON><PERSON><PERSON> động tiếp tục mua hàng"}, "checkoutNowAction": "<PERSON><PERSON> to<PERSON> ngay", "@checkoutNowAction": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h toán ngay"}, "selectCustomerAction": "<PERSON><PERSON><PERSON> kh<PERSON> h<PERSON>ng", "@selectCustomerAction": {"description": "<PERSON><PERSON><PERSON> động chọn khách hàng"}, "addNewCustomerAction": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng mới", "@addNewCustomerAction": {"description": "<PERSON><PERSON><PERSON> động thêm khách hàng mới"}, "applyDiscountAction": "<PERSON><PERSON> dụng gi<PERSON>m giá", "@applyDiscountAction": {"description": "<PERSON><PERSON><PERSON> động áp dụng giảm giá"}, "calculateTaxAction": "<PERSON><PERSON><PERSON> thuế", "@calculateTaxAction": {"description": "<PERSON><PERSON><PERSON> động tính thuế"}, "previewInvoiceAction": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> h<PERSON> đơn", "@previewInvoiceAction": {"description": "<PERSON><PERSON><PERSON> động xem trước hóa đơn"}, "salesReportsTitle": "<PERSON><PERSON><PERSON> c<PERSON>o b<PERSON> h<PERSON>ng", "@salesReportsTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> báo cáo bán hàng"}, "salesStatisticsTitle": "<PERSON><PERSON><PERSON><PERSON> kê b<PERSON> hàng", "@salesStatisticsTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề thống kê bán hàng"}, "salesHistoryTitle": "<PERSON><PERSON><PERSON> s<PERSON> b<PERSON> hàng", "@salesHistoryTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> lịch sử bán hàng"}, "transactionHistoryTitle": "<PERSON><PERSON><PERSON> sử giao dịch", "@transactionHistoryTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> lịch sử giao dịch"}, "revenueLabel": "<PERSON><PERSON>h thu", "@revenueLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> doanh thu"}, "profitLabel": "<PERSON><PERSON><PERSON>", "@profitLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> lợi n<PERSON>n"}, "quantitySoldLabel": "S<PERSON> l<PERSON> bán", "@quantitySoldLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> số l<PERSON> bán"}, "bestSellingProductsLabel": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> b<PERSON>y", "@bestSellingProductsLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m b<PERSON>y"}, "loyalCustomersLabel": "<PERSON><PERSON><PERSON><PERSON> hàng thân thiết", "@loyalCustomersLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng thân thiết"}, "productCatalogTitle": "<PERSON><PERSON> m<PERSON><PERSON> sản ph<PERSON>m", "@productCatalogTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề danh mục sản phẩm"}, "productSelectionTitle": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "@productSelectionTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đề chọn sản phẩm"}, "cartManagementTitle": "<PERSON><PERSON><PERSON><PERSON> lý giỏ hàng", "@cartManagementTitle": {"description": "Ti<PERSON><PERSON> đề quản lý giỏ hàng"}, "checkoutScreenTitle": "<PERSON><PERSON> toán", "@checkoutScreenTitle": {"description": "<PERSON>i<PERSON><PERSON> đề màn hình thanh toán"}, "receiptPrintingTitle": "In hóa đơn", "@receiptPrintingTitle": {"description": "<PERSON>i<PERSON><PERSON> đề in hóa đơn"}, "scanBarcodeTitle": "Quét mã vạch", "@scanBarcodeTitle": {"description": "Tiêu đề dialog quét mã vạch"}, "enterBarcodeLabel": "<PERSON>hậ<PERSON> mã vạch", "@enterBarcodeLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>p mã vạch"}, "scanOrEnterBarcodeHint": "<PERSON><PERSON>t hoặc nhập mã sản phẩm...", "@scanOrEnterBarcodeHint": {"description": "Gợ<PERSON> ý quét hoặc nhập mã vạch"}, "orUseCameraToScan": "Hoặc sử dụng camera để quét mã vạch", "@orUseCameraToScan": {"description": "Hướng dẫn sử dụng camera quét mã vạch"}, "addedToCartMessage": "Đã thêm {productName} vào giỏ hàng", "@addedToCartMessage": {"description": "<PERSON><PERSON><PERSON><PERSON> báo đã thêm sản phẩm vào giỏ hàng", "placeholders": {"productName": {"type": "String"}}}, "productNotFoundMessage": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm với mã: {barcode}", "@productNotFoundMessage": {"description": "<PERSON>hô<PERSON> báo không tìm thấy sản phẩm", "placeholders": {"barcode": {"type": "String"}}}, "cashMethodLabel": "Tiền mặt", "@cashMethodLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức thanh toán tiền mặt"}, "creditCardMethodLabel": "Thẻ tín dụng", "@creditCardMethodLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức thanh toán thẻ tín dụng"}, "bankTransferMethodLabel": "<PERSON><PERSON><PERSON><PERSON>", "@bankTransferMethodLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức thanh to<PERSON> chuyển k<PERSON>n"}, "eWalletMethodLabel": "<PERSON><PERSON> điện tử", "@eWalletMethodLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức thanh toán ví điện tử"}, "otherMethodLabel": "K<PERSON><PERSON><PERSON>", "@otherMethodLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức thanh to<PERSON> kh<PERSON>c"}, "amountReceivedLabel": "<PERSON><PERSON> tiền nhận", "@amountReceivedLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> số tiền nhận"}, "changeAmountLabel": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i", "@changeAmountLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tiền thối"}, "enterAmountReceivedHint": "<PERSON><PERSON><PERSON><PERSON> số tiền khách đưa", "@enterAmountReceivedHint": {"description": "<PERSON><PERSON><PERSON> ý nhập số tiền khách đưa"}, "exactAmountButton": "Vừa đủ", "@exactAmountButton": {"description": "<PERSON><PERSON>t số tiền vừa đủ"}, "processPaymentButton": "<PERSON><PERSON> lý thanh toán", "@processPaymentButton": {"description": "<PERSON><PERSON><PERSON> lý thanh toán"}, "processingPaymentLabel": "<PERSON><PERSON> xử lý...", "@processingPaymentLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> đang xử lý thanh toán"}, "printReceiptDialogTitle": "In hóa đơn", "@printReceiptDialogTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> hộp tho<PERSON> in hóa đơn"}, "printReceiptDialogContent": "Bạn có muốn in hóa đơn cho đơn hàng này không?", "@printReceiptDialogContent": {"description": "<PERSON><PERSON><PERSON> dung hộp tho<PERSON>i in hóa đơn"}, "noButtonLabel": "K<PERSON>ô<PERSON>", "@noButtonLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>t không"}, "printReceiptButtonLabel": "In hóa đơn", "@printReceiptButtonLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> nú<PERSON> in hóa đơn"}, "taxReceiptLabel": "<PERSON><PERSON><PERSON>", "@taxReceiptLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> thuế trong hóa đơn"}, "discountReceiptLabel": "G<PERSON>ảm giá", "@discountReceiptLabel": {"description": "<PERSON><PERSON>ã<PERSON> giảm giá trong hóa đơn"}, "lineItemTotalLabel": "Tổng dòng", "@lineItemTotalLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng cho mỗi dòng sản phẩm"}, "checkoutButtonLabel": "<PERSON><PERSON> toán", "@checkoutButtonLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>t <PERSON>h toán"}, "saveDraftButtonLabel": "<PERSON><PERSON><PERSON>", "@saveDraftButtonLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> lưu nh<PERSON>"}, "customerInfoTooltip": "Thông tin khách hàng", "@customerInfoTooltip": {"description": "<PERSON><PERSON> thích thông tin khách hàng"}, "discountTooltip": "G<PERSON>ảm giá", "@discountTooltip": {"description": "<PERSON><PERSON> thích giảm giá"}, "customerInfoDialogTitle": "Thông tin khách hàng", "@customerInfoDialogTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> hộp thoại thông tin khách hàng"}, "customerNameFieldLabel": "<PERSON><PERSON><PERSON> h<PERSON>ng", "@customerNameFieldLabel": {"description": "<PERSON><PERSON><PERSON>n trường tên khách hàng"}, "customerPhoneFieldLabel": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "@customerPhoneFieldLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> trường số điện thoại"}, "saveButtonLabel": "<PERSON><PERSON><PERSON>", "@saveButtonLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>u"}, "discountDialogTitle": "G<PERSON>ảm giá", "@discountDialogTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> hộp thoại giảm giá"}, "discountAmountFieldLabel": "Số tiền giảm giá", "@discountAmountFieldLabel": {"description": "<PERSON>hãn trường số tiền giảm giá"}, "subtotalDisplayLabel": "<PERSON><PERSON><PERSON>", "@subtotalDisplayLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> hiển thị tạm t<PERSON>h"}, "applyButtonLabel": "<PERSON><PERSON>", "@applyButtonLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dụng"}, "checkoutDialogTitle": "<PERSON><PERSON> toán", "@checkoutDialogTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> hộp thoại thanh toán"}, "subtotalCheckoutLabel": "<PERSON><PERSON><PERSON>", "@subtotalCheckoutLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tạm tính trong thanh toán"}, "taxCheckoutLabel": "<PERSON><PERSON><PERSON> (10%)", "@taxCheckoutLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> thuế trong thanh toán"}, "discountCheckoutLabel": "G<PERSON>ảm giá", "@discountCheckoutLabel": {"description": "<PERSON>hãn gi<PERSON>m giá trong checkout"}, "returningToDashboard": "Đang quay lại màn hình ch<PERSON>h", "@returningToDashboard": {"description": "<PERSON><PERSON><PERSON><PERSON> báo khi quay lại màn hình ch<PERSON>h"}, "appResumed": "<PERSON><PERSON> khôi phục ứng dụng", "@appResumed": {"description": "<PERSON>h<PERSON><PERSON> báo khi ứng dụng đư<PERSON><PERSON> khôi phục từ chế độ nền"}, "dataUpdated": "📊 <PERSON><PERSON> liệu đã được cập nhật", "@dataUpdated": {"description": "<PERSON><PERSON><PERSON><PERSON> báo khi dữ liệu đư<PERSON><PERSON> làm mới"}, "manualRefresh": "<PERSON><PERSON><PERSON> mới thủ công", "@manualRefresh": {"description": "<PERSON><PERSON><PERSON> động làm mới thủ công"}, "refreshData": "<PERSON><PERSON><PERSON>", "@refreshData": {"description": "<PERSON><PERSON><PERSON> làm mới dữ liệu"}, "errorLoadingData": "Lỗi khi tải dữ liệu: {error}", "@errorLoadingData": {"description": "Thông báo lỗi khi không thể tải dữ liệu", "placeholders": {"error": {"type": "String"}}}, "retryAfterError": "Thử lại sau khi lỗi", "@retryAfterError": {"description": "<PERSON><PERSON><PERSON> động thử lại sau khi xảy ra lỗi"}, "barcodeDialogTitle": "Quét mã vạch", "@barcodeDialogTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> hộp thoại quét mã vạch"}, "barcodeDialogMessage": "<PERSON><PERSON>t mã vạch để thêm sản phẩm vào giỏ hàng", "@barcodeDialogMessage": {"description": "<PERSON><PERSON><PERSON><PERSON> báo trong hộp thoại quét mã vạch"}, "barcodeDialogScanButton": "<PERSON><PERSON><PERSON>", "@barcodeDialogScanButton": {"description": "<PERSON><PERSON><PERSON><PERSON> nút quét trong hộp thoại mã vạch"}, "barcodeDialogCancelLabel": "<PERSON><PERSON><PERSON>", "@barcodeDialogCancelLabel": {"description": "Nhãn nút hủy trong dialog barcode"}, "totalCheckoutLabel": "<PERSON><PERSON><PERSON> cộng", "@totalCheckoutLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng cộng trong checkout"}, "paymentMethodCheckoutLabel": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "@paymentMethodCheckoutLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán trong checkout"}, "customerCashLabel": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON><PERSON> đ<PERSON>", "@customerCashLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tiền khách đưa"}, "changeCheckoutLabel": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i", "@changeCheckoutLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> tiền thối trong checkout"}, "completeButtonLabel": "<PERSON><PERSON><PERSON> t<PERSON>t", "@completeButtonLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> nút hoàn tất"}, "insufficientCashMessage": "<PERSON><PERSON> tiền nhận không đủ để thanh toán", "@insufficientCashMessage": {"description": "<PERSON>h<PERSON><PERSON> báo số tiền không đủ"}, "orderCreatedSuccessMessage": "<PERSON><PERSON> tạo đơn hàng thành công", "@orderCreatedSuccessMessage": {"description": "<PERSON><PERSON><PERSON><PERSON> báo tạo đơn hàng thành công"}, "itemCountLabel": "s<PERSON><PERSON> p<PERSON>m", "@itemCountLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> đếm sản phẩm"}, "revenue": "<PERSON><PERSON>h thu", "@revenue": {"description": "<PERSON><PERSON><PERSON><PERSON> doanh thu trong báo cáo"}, "profit": "<PERSON><PERSON><PERSON>", "@profit": {"description": "<PERSON><PERSON><PERSON><PERSON> lợi nhuận trong báo cáo"}, "orderCount": "<PERSON><PERSON> đơn hàng", "@orderCount": {"description": "<PERSON><PERSON> đơn hàng"}, "inventoryValue": "<PERSON><PERSON><PERSON> kho", "@inventoryValue": {"description": "<PERSON>hãn giá trị tồn kho"}, "dateRangeLabel": "<PERSON><PERSON><PERSON><PERSON> thời gian:", "@dateRangeLabel": {"description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> thời gian"}, "overview": "<PERSON><PERSON><PERSON> quan", "@overview": {"description": "Ti<PERSON><PERSON> đề tổng quan"}, "salesReportTitle": "<PERSON><PERSON><PERSON> c<PERSON>o b<PERSON> h<PERSON>ng", "@salesReportTitle": {"description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> báo cáo bán hàng"}, "totalCost": "Tổng chi phí", "@totalCost": {"description": "Tổng chi phí"}, "grossProfit": "<PERSON><PERSON><PERSON>", "@grossProfit": {"description": "<PERSON><PERSON><PERSON>"}, "profitMargin": "Tỷ suất lợi n<PERSON>n", "@profitMargin": {"description": "Tỷ suất lợi n<PERSON>n"}, "inventoryReportTitle": "<PERSON><PERSON>o c<PERSON>o tồn kho", "@inventoryReportTitle": {"description": "Ti<PERSON><PERSON> đề báo cáo tồn kho"}, "totalProducts": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "@totalProducts": {"description": "Tổng số sản phẩm"}, "inventoryWorth": "<PERSON><PERSON><PERSON> trị tồn kho", "@inventoryWorth": {"description": "<PERSON><PERSON><PERSON> trị tồn kho"}, "lowStockItems": "<PERSON><PERSON><PERSON> hế<PERSON> hàng", "@lowStockItems": {"description": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> s<PERSON><PERSON> hết hàng"}, "outOfStockItems": "<PERSON><PERSON><PERSON>", "@outOfStockItems": {"description": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> hết hàng"}, "financeReportTitle": "Báo c<PERSON>o tài ch<PERSON>h", "@financeReportTitle": {"description": "Ti<PERSON><PERSON> đề báo cáo tài ch<PERSON>h"}, "totalIncome": "<PERSON><PERSON><PERSON> thu", "@totalIncome": {"description": "<PERSON><PERSON><PERSON> thu nhập"}, "totalExpense": "Tổng chi", "@totalExpense": {"description": "Tổng chi phí"}, "netCashFlow": "<PERSON>òng tiền ròng", "@netCashFlow": {"description": "<PERSON>òng tiền ròng"}, "endingBalance": "Số dư cuối kỳ", "@endingBalance": {"description": "Số dư cuối kỳ"}, "topSellingProducts": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> b<PERSON>y", "@topSellingProducts": {"description": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> b<PERSON> ch<PERSON> nh<PERSON>t"}, "sold": "<PERSON><PERSON> bán", "@sold": {"description": "<PERSON><PERSON> bán"}, "inventoryAlerts": "<PERSON><PERSON><PERSON> b<PERSON>o tồn kho", "@inventoryAlerts": {"description": "<PERSON><PERSON><PERSON> b<PERSON>o tồn kho"}, "allProductsInStock": "Tất cả sản phẩm đều có đủ tồn kho", "@allProductsInStock": {"description": "<PERSON><PERSON><PERSON><PERSON> báo tất cả sản phẩm đủ hàng"}, "stockInfo": "Tồn: {current} / Tối thiểu: {min}", "@stockInfo": {"description": "Thông tin tồn kho", "placeholders": {"current": {}, "min": {}}}, "outOfStock": "<PERSON><PERSON><PERSON>", "@outOfStock": {"description": "<PERSON><PERSON><PERSON><PERSON> thái hết hàng"}, "lowStock": "<PERSON><PERSON><PERSON>", "@lowStock": {"description": "<PERSON><PERSON><PERSON><PERSON> thái sắp hết hàng"}, "receiptRecordedFromOrder": "<PERSON><PERSON> ghi nhận phiếu thu từ đơn hàng {orderNumber}, số tiền {amount}", "@receiptRecordedFromOrder": {"description": "<PERSON><PERSON><PERSON><PERSON> báo đã ghi nhận phiếu thu từ đơn hàng", "placeholders": {"orderNumber": {"type": "String", "example": "ORD001"}, "amount": {"type": "num", "example": "100000"}}}, "receiptFromOrder": "<PERSON>hu từ đơn hàng {orderNumber}", "@receiptFromOrder": {"description": "<PERSON><PERSON> tả phiếu thu từ đơn hàng", "placeholders": {"orderNumber": {"type": "String", "example": "ORD001"}}}, "errorCreatingReceipt": "Lỗi tạo phiếu thu/chi", "@errorCreatingReceipt": {"description": "<PERSON>h<PERSON>ng báo lỗi khi tạo phiếu thu"}, "errorSendingPaymentNotification": "Lỗi g<PERSON>i thông báo thanh toán", "@errorSendingPaymentNotification": {"description": "Thông báo lỗi khi gửi thông báo thanh toán"}, "cash": "Tiền mặt", "@cash": {"description": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán tiền mặt"}, "paymentMethods": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "@paymentMethods": {"description": "<PERSON><PERSON><PERSON><PERSON> đề phần ph<PERSON><PERSON><PERSON> thức thanh toán"}, "card": "Thẻ", "@card": {"description": "<PERSON><PERSON><PERSON><PERSON> thức thanh to<PERSON> bằng thẻ"}, "transfer": "<PERSON><PERSON><PERSON><PERSON>", "@transfer": {"description": "<PERSON><PERSON><PERSON><PERSON> thức thanh to<PERSON> chuyển k<PERSON>n"}, "invoiceCreatedFromOrder": "<PERSON><PERSON> tạo hóa đơn từ đơn hàng {orderNumber}", "@invoiceCreatedFromOrder": {"description": "<PERSON><PERSON><PERSON><PERSON> báo đã tạo hóa đơn từ đơn hàng", "placeholders": {"orderNumber": {"type": "String", "example": "ORD001"}}}, "errorCreatingInvoice": "Lỗi tạo hóa đơn", "@errorCreatingInvoice": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi khi tạo hóa đơn"}, "retailCustomer": "Khách lẻ", "@retailCustomer": {"description": "Tên mặc định cho khách hàng lẻ"}, "searchInvoice": "<PERSON><PERSON><PERSON> kiếm hóa đơn...", "@searchInvoice": {"description": "Placeholder cho ô tìm kiếm hóa đơn"}, "invoiceType": "<PERSON><PERSON><PERSON> hóa đơn", "@invoiceType": {"description": "<PERSON><PERSON><PERSON><PERSON> cho bộ lọc lo<PERSON>i hóa đơn"}, "sale": "<PERSON><PERSON>", "@sale": {"description": "<PERSON><PERSON><PERSON> h<PERSON>a đơn b<PERSON> hàng"}, "purchase": "<PERSON><PERSON>", "@purchase": {"description": "<PERSON><PERSON><PERSON> hóa đơn mua hàng"}, "return_": "<PERSON><PERSON><PERSON> h<PERSON>", "@return_": {"description": "<PERSON><PERSON><PERSON> hóa đơn trả hàng"}, "draft": "Nháp", "@draft": {"description": "<PERSON>r<PERSON><PERSON> thái hóa đơn nháp"}, "sent": "Đ<PERSON> gửi", "@sent": {"description": "<PERSON>r<PERSON><PERSON> thái hóa đơn đã gửi"}, "overdue": "<PERSON><PERSON><PERSON> h<PERSON>n", "@overdue": {"description": "<PERSON>r<PERSON><PERSON> thái hóa đơn quá hạn"}, "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "partnerTypeRequired": "<PERSON><PERSON> lòng chọn loại đối tác", "partnerNameRequired": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON> *", "partnerCode": "<PERSON><PERSON> đối tác", "partnerCodeHint": "<PERSON><PERSON><PERSON><PERSON> mã đối tác (nếu c<PERSON>)", "partnerGroup": "<PERSON><PERSON><PERSON><PERSON> đối tác", "contactInfo": "<PERSON>h<PERSON>ng tin liên hệ", "invalidPhoneNumber": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ", "address": "Địa chỉ", "contactPerson": "<PERSON><PERSON><PERSON><PERSON> li<PERSON>n hệ", "businessInfo": "<PERSON>h<PERSON>ng tin doanh nghiệp", "taxCode": "<PERSON><PERSON> số thuế", "invalidTaxCode": "<PERSON><PERSON> số thuế không hợp lệ", "activePartnerDescription": "<PERSON><PERSON><PERSON> tác đang hoạt động", "generalGroup": "<PERSON><PERSON><PERSON><PERSON> chung", "vipGroup": "Nhóm VIP", "wholesaleGroup": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> b<PERSON>n", "retailGroup": "<PERSON><PERSON><PERSON><PERSON> bán lẻ", "distributorGroup": "Nhóm phân phối", "manufacturerGroup": "<PERSON><PERSON><PERSON><PERSON> sản xu<PERSON>", "partnerDetails": "<PERSON> tiết đối tác", "editTooltip": "Chỉnh sửa thông tin", "deletePartner": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> tác", "tryAgain": "<PERSON><PERSON><PERSON> lại", "partnerNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đối tác", "partnerInfo": "Th<PERSON>ng tin đối tác", "partnerType": "<PERSON><PERSON><PERSON> đối tác", "supplier": "<PERSON><PERSON><PERSON> cung cấp", "enterNotes": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "noNotes": "<PERSON><PERSON><PERSON><PERSON> có ghi chú", "notAvailable": "<PERSON><PERSON><PERSON><PERSON> có", "statistics": "<PERSON><PERSON><PERSON><PERSON> kê", "creditLimit": "<PERSON><PERSON><PERSON> mức tín dụng", "currentBalance": "<PERSON><PERSON><PERSON> nợ hiện tại", "inactive": "Ngưng hoạt động", "deletePartnerConfirmation": "Bạn có chắc muốn xóa đối tác này?", "receipt": "<PERSON><PERSON><PERSON> thu", "expense": "Chi phí", "salary": "Lư<PERSON><PERSON>", "rent": "<PERSON><PERSON><PERSON>", "utilities": "<PERSON><PERSON><PERSON><PERSON>", "marketing": "Marketing", "loan": "<PERSON><PERSON>", "investment": "<PERSON><PERSON><PERSON>", "codeLabel": "Mã", "createdAt": "Tạo lúc", "deleteInvoice": "<PERSON><PERSON><PERSON> hóa đơn", "invoiceNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy hóa đơn", "deleteReceipt": "<PERSON><PERSON><PERSON> phiếu thu/chi", "receiptDetails": "<PERSON> tiết phiếu thu", "paymentDetails": "<PERSON> tiết phiếu chi", "itemRemoved": "Đã xóa", "fromCart": "khỏi giỏ hàng", "scanBarcodeToAddProduct": "<PERSON><PERSON><PERSON> mã vạch để thêm sản phẩm", "createOrder": "<PERSON><PERSON><PERSON> đơn hàng", "orderDetails": "<PERSON> tiết đơn hàng", "generatingPdf": "Đang tạo PDF...", "back": "Quay lại", "invoiceSharedSuccess": "Chia sẻ hóa đơn thành công!", "invoiceShareError": "Lỗi chia sẻ hóa đơn", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "showInvoiceAfterPayment": "<PERSON><PERSON><PERSON> thị hóa đơn sau thanh toán", "showInvoiceAfterPaymentDescription": "Tự động mở màn hình hóa đơn sau khi thanh toán thành công", "invoiceSettings": "<PERSON>ài đặt hóa đơn", "invoiceAfterPaymentEnabled": "<PERSON><PERSON> bật hiển thị hóa đơn sau thanh toán", "invoiceAfterPaymentDisabled": "<PERSON><PERSON> tắt hiển thị hóa đơn sau thanh toán", "errorLoadingCashbookEntry": "Lỗi khi tải phiếu thu/chi: {error}", "@errorLoadingCashbookEntry": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi khi tải phiếu thu/chi", "placeholders": {"error": {}}}, "transactionTypeRequired": "<PERSON><PERSON> lòng chọn lo<PERSON>i giao d<PERSON>ch", "@transactionTypeRequired": {"description": "<PERSON><PERSON><PERSON><PERSON> báo yêu cầu chọn loại giao dịch"}, "amountRequired": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "@amountRequired": {"description": "<PERSON><PERSON><PERSON><PERSON> báo yêu cầu nhập số tiền"}, "invalidAmount": "<PERSON><PERSON> tiền không hợp lệ", "@invalidAmount": {"description": "<PERSON><PERSON><PERSON><PERSON> báo số tiền không hợp lệ"}, "categoryRequired": "<PERSON><PERSON> lòng chọn danh mục", "@categoryRequired": {"description": "<PERSON><PERSON><PERSON><PERSON> báo yêu cầu chọn danh mục"}, "paymentMethodRequired": "<PERSON><PERSON> lòng chọn ph<PERSON><PERSON><PERSON> thức thanh toán", "@paymentMethodRequired": {"description": "<PERSON><PERSON><PERSON><PERSON> báo yêu cầu chọn ph<PERSON><PERSON><PERSON> thức thanh toán"}, "additionalInfo": "Thông tin thêm", "@additionalInfo": {"description": "<PERSON><PERSON><PERSON><PERSON> trường thông tin thêm"}, "income": "<PERSON>hu", "@income": {"description": "<PERSON><PERSON><PERSON> giao dịch thu"}, "general": "<PERSON>", "@general": {"description": "<PERSON><PERSON> chung"}, "service": "<PERSON><PERSON><PERSON> v<PERSON>", "@service": {"description": "<PERSON><PERSON> m<PERSON> d<PERSON> vụ"}, "transport": "<PERSON><PERSON><PERSON> ch<PERSON>", "@transport": {"description": "<PERSON><PERSON> m<PERSON>c vận chuy<PERSON>n"}, "maintenance": "<PERSON><PERSON><PERSON> trì", "@maintenance": {"description": "<PERSON><PERSON> m<PERSON>c b<PERSON>o trì"}, "bankTransfer": "<PERSON><PERSON><PERSON><PERSON>", "@bankTransfer": {"description": "<PERSON><PERSON><PERSON><PERSON> thức thanh to<PERSON> chuyển k<PERSON>n"}, "creditCard": "Thẻ tín dụng", "@creditCard": {"description": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán thẻ tín dụng"}, "debitCard": "Thẻ ghi nợ", "@debitCard": {"description": "<PERSON><PERSON><PERSON><PERSON> thức thanh to<PERSON> thẻ ghi nợ"}, "eWallet": "<PERSON><PERSON> điện tử", "@eWallet": {"description": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán ví điện tử"}, "cashBook": "Sổ quỹ", "@cashBook": {"description": "Tiêu đề trang sổ quỹ"}, "addReceipt": "<PERSON><PERSON><PERSON><PERSON> phi<PERSON>u thu", "@addReceipt": {"description": "<PERSON><PERSON><PERSON> thêm phi<PERSON>u thu"}, "receiptNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phiếu thu", "@receiptNotFound": {"description": "Thông báo không tìm thấy phiếu thu"}, "receiptInfo": "Thông tin phiếu thu", "@receiptInfo": {"description": "Ti<PERSON>u đề phần thông tin phiếu thu"}, "receiptType": "<PERSON><PERSON><PERSON> phiếu thu", "@receiptType": {"description": "<PERSON><PERSON><PERSON><PERSON> trư<PERSON><PERSON> loại phiếu thu"}, "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "@unknown": {"description": "<PERSON><PERSON><PERSON><PERSON> cho giá trị không xác định"}, "noPartnerInfo": "<PERSON><PERSON><PERSON><PERSON> có thông tin đối tác", "@noPartnerInfo": {"description": "Thông báo khi không có thông tin đối tác"}, "deleteReceiptConfirmation": "Bạn có chắc chắn muốn xóa phiếu thu này?", "@deleteReceiptConfirmation": {"description": "<PERSON><PERSON><PERSON><PERSON> báo xác nhận xóa phiếu thu"}, "receiptUpdated": "<PERSON><PERSON><PERSON> nhật phiếu thu thành công", "@receiptUpdated": {"description": "<PERSON><PERSON><PERSON><PERSON> báo khi cập nhật phiếu thu thành công"}, "errorUpdatingReceipt": "Lỗi khi cập nhật phiếu thu", "@errorUpdatingReceipt": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi khi cập nhật phiếu thu"}, "receiptDeleted": "<PERSON><PERSON><PERSON> phiếu thu thành công", "@receiptDeleted": {"description": "<PERSON>h<PERSON>ng báo khi xóa phiếu thu thành công"}, "errorDeletingReceipt": "Lỗi khi xóa phiếu thu", "@errorDeletingReceipt": {"description": "<PERSON>h<PERSON>ng báo lỗi khi xóa phiếu thu"}, "cashBalance": "<PERSON><PERSON> dư tiền mặt", "@cashBalance": {"description": "Ti<PERSON><PERSON> đề phần số dư tiền mặt"}, "balance": "Số dư", "@balance": {"description": "<PERSON><PERSON><PERSON><PERSON> cho số dư"}, "noReceipts": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phiếu thu", "@noReceipts": {"description": "Thông báo khi không tìm thấy phiếu thu"}, "totalInvoices": "Tổng số hóa đơn", "@totalInvoices": {"description": "<PERSON><PERSON><PERSON><PERSON> cho tổng số hóa đơn"}, "paidInvoices": "<PERSON><PERSON><PERSON> đơn đã thanh toán", "@paidInvoices": {"description": "<PERSON><PERSON><PERSON><PERSON> cho hóa đơn đã thanh toán"}, "pendingInvoices": "<PERSON><PERSON><PERSON> đơn chờ thanh toán", "@pendingInvoices": {"description": "<PERSON><PERSON><PERSON><PERSON> cho hóa đơn chờ thanh toán"}, "overdueInvoices": "<PERSON><PERSON><PERSON> đơn quá hạn", "@overdueInvoices": {"description": "<PERSON><PERSON><PERSON><PERSON> cho hóa đơn quá hạn"}, "totalValue": "Tổng giá trị", "@totalValue": {"description": "<PERSON><PERSON><PERSON><PERSON> cho tổng giá trị hóa đơn"}, "collectedAmount": "<PERSON><PERSON> tiền đã thu", "@collectedAmount": {"description": "<PERSON><PERSON><PERSON><PERSON> cho số tiền đã thu"}, "overdueAmount": "<PERSON><PERSON> tiền quá hạn", "@overdueAmount": {"description": "<PERSON><PERSON><PERSON><PERSON> cho số tiền quá hạn"}, "collectionRate": "Tỷ lệ thu", "@collectionRate": {"description": "<PERSON><PERSON><PERSON><PERSON> cho tỷ lệ thu"}, "createNewInvoice": "<PERSON><PERSON><PERSON> hóa đơn mới", "@createNewInvoice": {"description": "<PERSON><PERSON><PERSON><PERSON> cho tạo hóa đơn mới"}, "errorLoadingCashbookEntries": "Lỗi khi tải phiếu thu: {error}", "@errorLoadingCashbookEntries": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi khi tải phiếu thu", "placeholders": {"error": {"type": "String"}}}, "errorCreatingCashbookEntry": "Lỗi khi tạo phiếu thu: {error}", "@errorCreatingCashbookEntry": {"description": "<PERSON>h<PERSON>ng báo lỗi khi tạo phiếu thu", "placeholders": {"error": {"type": "String"}}}, "errorUpdatingCashbookEntry": "Lỗi khi cập nhật phiếu thu: {error}", "@errorUpdatingCashbookEntry": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi khi cập nhật phiếu thu", "placeholders": {"error": {"type": "String"}}}, "errorDeletingCashbookEntry": "Lỗi khi xóa phiếu thu: {error}", "@errorDeletingCashbookEntry": {"description": "<PERSON>h<PERSON>ng báo lỗi khi xóa phiếu thu", "placeholders": {"error": {"type": "String"}}}, "errorLoadingCashBalance": "Lỗi khi tải số dư tiền mặt: {error}", "@errorLoadingCashBalance": {"description": "<PERSON><PERSON><PERSON><PERSON> báo lỗi khi tải số dư tiền mặt", "placeholders": {"error": {"type": "String"}}}, "errorLoadingFinanceStats": "Lỗi khi tải thống kê tài chính: {error}", "@errorLoadingFinanceStats": {"description": "<PERSON>h<PERSON><PERSON> báo lỗi khi tải thống kê tài chính", "placeholders": {"error": {"type": "String"}}}, "totalAmount": "<PERSON><PERSON><PERSON> tiền", "@totalAmount": {"description": "<PERSON><PERSON><PERSON><PERSON> tổng tiền"}, "customerReport": "<PERSON><PERSON>o c<PERSON>o kh<PERSON>ch hàng", "@customerReport": {"description": "<PERSON><PERSON>o c<PERSON>o kh<PERSON>ch hàng"}, "productReport": "<PERSON><PERSON><PERSON> c<PERSON>o sản ph<PERSON>m", "@productReport": {"description": "<PERSON><PERSON><PERSON> c<PERSON>o sản ph<PERSON>m"}}