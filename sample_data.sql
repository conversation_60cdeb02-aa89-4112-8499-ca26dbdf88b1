-- Sample data for City POS
-- Run this after setting up the schema

-- Insert sample product categories
INSERT INTO product_categories (name, description) VALUES
('<PERSON><PERSON> uống', '<PERSON><PERSON><PERSON> loại nước uống, nước ngọt, trà, cà phê'),
('Thực phẩm', 'Bánh kẹo, snack, thực phẩm khô'),
('Văn phòng phẩm', '<PERSON><PERSON><PERSON>, gi<PERSON>y, dụng cụ văn phòng'),
('<PERSON>ia dụng', '<PERSON><PERSON> dùng gia đình, dụng cụ nhà bếp'),
('Điện tử', 'Thiế<PERSON> bị điện tử, phụ kiện');

-- Insert sample partners (customers and suppliers)
INSERT INTO partners (name, phone, email, address, type) VALUES
('Công ty TNHH ABC', '0901234567', '<EMAIL>', '123 Đường ABC, Quận 1, TP.HCM', 'supplier'),
('<PERSON><PERSON><PERSON> phân phối XYZ', '0912345678', '<EMAIL>', '456 Đường XYZ, Quận 2, TP.HCM', 'supplier'),
('Nguyễn Văn A', '0923456789', '<EMAIL>', '789 Đường DEF, Quận 3, TP.HCM', 'customer'),
('Trần Thị B', '0934567890', '<EMAIL>', '321 Đường GHI, Quận 4, TP.HCM', 'customer'),
('Lê Văn C', '0945678901', '<EMAIL>', '654 Đường JKL, Quận 5, TP.HCM', 'customer'),
('Phạm Thị D', '0956789012', '<EMAIL>', '987 Đường MNO, Quận 6, TP.HCM', 'customer');

-- Insert sample products
INSERT INTO products (name, sku, unit, category_id, price_import, price_sale, stock_qty, description, barcode) VALUES
('Coca Cola 330ml', 'COCA-330', 'lon', (SELECT id FROM product_categories WHERE name = 'Đồ uống'), 8000, 12000, 100, 'Nước ngọt Coca Cola lon 330ml', '1234567890123'),
('Pepsi 330ml', 'PEPSI-330', 'lon', (SELECT id FROM product_categories WHERE name = 'Đồ uống'), 7500, 11000, 80, 'Nước ngọt Pepsi lon 330ml', '1234567890124'),
('Nước suối Aquafina 500ml', 'AQUA-500', 'chai', (SELECT id FROM product_categories WHERE name = 'Đồ uống'), 3000, 5000, 200, 'Nước suối Aquafina chai 500ml', '1234567890125'),
('Bánh Oreo', 'OREO-001', 'gói', (SELECT id FROM product_categories WHERE name = 'Thực phẩm'), 15000, 22000, 50, 'Bánh quy Oreo gói 137g', '1234567890126'),
('Kẹo Mentos', 'MENTOS-001', 'gói', (SELECT id FROM product_categories WHERE name = 'Thực phẩm'), 8000, 12000, 75, 'Kẹo Mentos vị bạc hà', '1234567890127'),
('Bút bi Thiên Long', 'TL-001', 'cây', (SELECT id FROM product_categories WHERE name = 'Văn phòng phẩm'), 3000, 5000, 120, 'Bút bi Thiên Long màu xanh', '1234567890128'),
('Tập vở 200 trang', 'TAP-200', 'quyển', (SELECT id FROM product_categories WHERE name = 'Văn phòng phẩm'), 8000, 12000, 60, 'Tập vở học sinh 200 trang', '1234567890129'),
('Cốc thủy tinh', 'CUP-001', 'cái', (SELECT id FROM product_categories WHERE name = 'Gia dụng'), 25000, 35000, 30, 'Cốc thủy tinh 300ml', '1234567890130'),
('Tai nghe Bluetooth', 'BT-001', 'cái', (SELECT id FROM product_categories WHERE name = 'Điện tử'), 150000, 250000, 20, 'Tai nghe Bluetooth không dây', '1234567890131'),
('Cáp sạc USB-C', 'USB-C-001', 'sợi', (SELECT id FROM product_categories WHERE name = 'Điện tử'), 50000, 80000, 40, 'Cáp sạc USB-C dài 1m', '1234567890132');

-- Insert sample stock transactions (imports)
INSERT INTO stock_transactions (type, partner_id, total_amount, notes) VALUES
('import', (SELECT id FROM partners WHERE name = 'Công ty TNHH ABC'), 5000000, 'Nhập hàng đợt 1 tháng 12'),
('import', (SELECT id FROM partners WHERE name = 'Nhà phân phối XYZ'), 3000000, 'Nhập hàng điện tử');

-- Insert sample stock transaction items
-- For first import transaction
INSERT INTO stock_transaction_items (transaction_id, product_id, qty, unit_price, total_price) VALUES
((SELECT id FROM stock_transactions WHERE notes = 'Nhập hàng đợt 1 tháng 12'), 
 (SELECT id FROM products WHERE sku = 'COCA-330'), 50, 8000, 400000),
((SELECT id FROM stock_transactions WHERE notes = 'Nhập hàng đợt 1 tháng 12'), 
 (SELECT id FROM products WHERE sku = 'PEPSI-330'), 40, 7500, 300000),
((SELECT id FROM stock_transactions WHERE notes = 'Nhập hàng đợt 1 tháng 12'), 
 (SELECT id FROM products WHERE sku = 'AQUA-500'), 100, 3000, 300000),
((SELECT id FROM stock_transactions WHERE notes = 'Nhập hàng đợt 1 tháng 12'), 
 (SELECT id FROM products WHERE sku = 'OREO-001'), 30, 15000, 450000);

-- For second import transaction
INSERT INTO stock_transaction_items (transaction_id, product_id, qty, unit_price, total_price) VALUES
((SELECT id FROM stock_transactions WHERE notes = 'Nhập hàng điện tử'), 
 (SELECT id FROM products WHERE sku = 'BT-001'), 10, 150000, 1500000),
((SELECT id FROM stock_transactions WHERE notes = 'Nhập hàng điện tử'), 
 (SELECT id FROM products WHERE sku = 'USB-C-001'), 20, 50000, 1000000);

-- Insert sample orders (sales)
INSERT INTO orders (customer_id, total_amount, discount, payment_method, notes) VALUES
((SELECT id FROM partners WHERE name = 'Nguyễn Văn A'), 47000, 3000, 'cash', 'Khách hàng thân thiết'),
((SELECT id FROM partners WHERE name = 'Trần Thị B'), 85000, 0, 'card', 'Thanh toán thẻ'),
(NULL, 24000, 1000, 'cash', 'Khách lẻ');

-- Insert sample order items
-- For first order
INSERT INTO order_items (order_id, product_id, qty, unit_price, total_price) VALUES
((SELECT id FROM orders WHERE notes = 'Khách hàng thân thiết'), 
 (SELECT id FROM products WHERE sku = 'COCA-330'), 2, 12000, 24000),
((SELECT id FROM orders WHERE notes = 'Khách hàng thân thiết'), 
 (SELECT id FROM products WHERE sku = 'OREO-001'), 1, 22000, 22000),
((SELECT id FROM orders WHERE notes = 'Khách hàng thân thiết'), 
 (SELECT id FROM products WHERE sku = 'TL-001'), 1, 5000, 5000);

-- For second order
INSERT INTO order_items (order_id, product_id, qty, unit_price, total_price) VALUES
((SELECT id FROM orders WHERE notes = 'Thanh toán thẻ'), 
 (SELECT id FROM products WHERE sku = 'BT-001'), 1, 250000, 250000),
((SELECT id FROM orders WHERE notes = 'Thanh toán thẻ'), 
 (SELECT id FROM products WHERE sku = 'CUP-001'), 1, 35000, 35000);

-- For third order (walk-in customer)
INSERT INTO order_items (order_id, product_id, qty, unit_price, total_price) VALUES
((SELECT id FROM orders WHERE notes = 'Khách lẻ'), 
 (SELECT id FROM products WHERE sku = 'PEPSI-330'), 2, 11000, 22000),
((SELECT id FROM orders WHERE notes = 'Khách lẻ'), 
 (SELECT id FROM products WHERE sku = 'MENTOS-001'), 1, 12000, 12000);

-- Insert sample cashbook entries
INSERT INTO cashbooks (type, amount, partner_id, order_id, description) VALUES
('receipt', 44000, (SELECT id FROM partners WHERE name = 'Nguyễn Văn A'), 
 (SELECT id FROM orders WHERE notes = 'Khách hàng thân thiết'), 'Thu tiền bán hàng'),
('receipt', 85000, (SELECT id FROM partners WHERE name = 'Trần Thị B'), 
 (SELECT id FROM orders WHERE notes = 'Thanh toán thẻ'), 'Thu tiền bán hàng'),
('receipt', 23000, NULL, 
 (SELECT id FROM orders WHERE notes = 'Khách lẻ'), 'Thu tiền bán hàng khách lẻ'),
('payment', 5000000, (SELECT id FROM partners WHERE name = 'Công ty TNHH ABC'), 
 NULL, 'Thanh toán tiền nhập hàng'),
('payment', 3000000, (SELECT id FROM partners WHERE name = 'Nhà phân phối XYZ'), 
 NULL, 'Thanh toán tiền nhập hàng điện tử');
