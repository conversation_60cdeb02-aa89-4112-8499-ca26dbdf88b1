class AppConstants {
  // App Info
  static const String appName = 'City POS';
  static const String appVersion = '1.0.0';
  
  // Database Tables
  static const String usersTable = 'users';
  static const String productsTable = 'products';
  static const String productCategoriesTable = 'product_categories';
  static const String stockTransactionsTable = 'stock_transactions';
  static const String stockTransactionItemsTable = 'stock_transaction_items';
  static const String ordersTable = 'orders';
  static const String orderItemsTable = 'order_items';
  static const String cashbooksTable = 'cashbooks';
  static const String partnersTable = 'partners';
  
  // Storage Buckets
  static const String productImagesBucket = 'product-images';
  static const String receiptsBucket = 'receipts';
  
  // User Roles
  static const String adminRole = 'admin';
  static const String salesRole = 'sales';
  static const String accountantRole = 'accountant';
  static const String warehouseRole = 'warehouse';
  
  // Transaction Types
  static const String importType = 'import';
  static const String exportType = 'export';
  
  // Partner Types
  static const String customerType = 'customer';
  static const String supplierType = 'supplier';
  
  // Payment Methods
  static const String cashPayment = 'cash';
  static const String cardPayment = 'card';
  static const String transferPayment = 'transfer';
  
  // Cashbook Types
  static const String receiptType = 'receipt';
  static const String paymentType = 'payment';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxNameLength = 100;
  static const int maxDescriptionLength = 500;
  
  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String timeFormat = 'HH:mm';
  
  // Currency
  static const String currencySymbol = '₫';
  static const String currencyCode = 'VND';
  
  // File Extensions
  static const List<String> imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx'];
}
