import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../data/models/order.dart';
import '../../data/models/partner.dart';
import '../../features/notifications/data/services/notification_service.dart';
import '../config/supabase_config.dart';
import 'invoice_service.dart';
import 'payment_service.dart';
import 'push_notification_service.dart';
import '../../features/sales/domain/entities/order.dart' as sales;

class SalesService {
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Create order with full integration
  static Future<sales.Order> createOrder(sales.Order order) async {
    try {
      print('Creating real order in Supabase...');

      // Generate real order number and UUID
      final orderNumber = _generateOrderNumber();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final orderId =
          '550e8400-e29b-41d4-a716-${timestamp.toString().substring(0, 12)}';

      // Create order record
      final orderData = {
        'id': orderId,
        'order_number': orderNumber,
        'type': order.type,
        'status': order.status.value,
        'subtotal': order.subtotal,
        'tax_amount': order.tax,
        'discount_amount': order.discount,
        'total_amount': order.total,
        'payment_status': order.paymentStatus.name,
        'payment_method': order.paymentMethod.name,
        'notes': order.note,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase.from('orders').insert(orderData);

      // Create order items
      for (final item in order.items) {
        final itemData = {
          'order_id': orderId,
          'product_id': item.productId,
          'quantity': item.quantity,
          'unit_price': item.unitPrice,
          'total_amount': item.totalAmount,
          'created_at': DateTime.now().toIso8601String(),
        };

        print('🔥 ORDER ITEM DEBUG: $itemData');
        await _supabase.from('order_items').insert(itemData);
      }

      print('Order created successfully: $orderNumber');

      final createdOrder = order.copyWith(
        id: orderId,
        orderNumber: orderNumber,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Create integrations
      await _createInvoiceFromOrder(createdOrder);
      await _recordCashReceipt(createdOrder);
      await createStockTransactionsForOrder(createdOrder);

      // Send push notification for new order
      await _sendOrderNotification(createdOrder);

      return createdOrder;
    } catch (e) {
      print('Error creating order: $e');
      throw Exception('Lỗi tạo đơn hàng: $e');
    }
  }

  // Get all orders
  static Future<List<sales.Order>> getOrders({
    String? search,
    String? status,
    String? paymentStatus,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      print('🔥 Getting orders with filters:');
      print('🔥 startDate: $startDate');
      print('🔥 endDate: $endDate');
      print('🔥 status: $status');

      dynamic query = _supabase
          .from('orders')
          .select()
          .order('created_at', ascending: false);

      // Apply date filters
      if (startDate != null) {
        query = query.filter('created_at', 'gte', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.filter('created_at', 'lte', endDate.toIso8601String());
      }

      // Apply other filters
      if (status != null) {
        query = query.eq('status', status);
      }

      if (paymentStatus != null) {
        query = query.eq('payment_status', paymentStatus);
      }

      if (search != null && search.isNotEmpty) {
        query = query.or('order_number.ilike.%$search%,notes.ilike.%$search%');
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 50) - 1);
      }

      final response = await query;
      final orders = response.map((json) => sales.Order.fromJson(json)).toList();

      print('🔥 Found ${orders.length} orders');

      return orders;
    } catch (e) {
      print('Error getting orders: $e');
      return [];
    }
  }

  // Get order by ID
  static Future<sales.Order?> getOrderById(String id) async {
    try {
      final response = await _supabase
          .from('orders')
          .select()
          .eq('id', id)
          .single();
      return sales.Order.fromJson(response);
    } catch (e) {
      print('Error getting order by ID: $e');
      return null;
    }
  }

  // Get sales analytics
  static Future<Map<String, dynamic>> getSalesAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      print('🔥 Getting sales analytics...');
      print('🔥 Date range: $startDate to $endDate');

      // Get orders data with date filters
      var ordersQuery = _supabase.from('orders').select('*');

      if (startDate != null) {
        ordersQuery = ordersQuery.filter(
          'created_at',
          'gte',
          startDate.toIso8601String(),
        );
      }

      if (endDate != null) {
        ordersQuery = ordersQuery.filter(
          'created_at',
          'lte',
          endDate.toIso8601String(),
        );
      }

      final ordersResponse = await ordersQuery;
      print('🔥 Raw orders response: ${ordersResponse.length} orders');

      final orders = ordersResponse
          .map((json) => sales.Order.fromJson(json))
          .toList();

      print('🔥 Parsed orders: ${orders.length} orders');

      // Calculate analytics
      final totalOrders = orders.length;
      final completedOrders = orders
          .where((o) => o.status == sales.OrderStatus.completed)
          .length;
      final pendingOrders = orders.where((o) => o.status == sales.OrderStatus.pending).length;
      final totalRevenue = orders.fold(
        0.0,
        (sum, order) => sum + order.total,
      );
      final paidAmount = orders
          .where((o) => o.paymentStatus == sales.PaymentStatus.paid)
          .fold(0.0, (sum, order) => sum + order.total);
      final pendingAmount = orders
          .where((o) => o.paymentStatus == sales.PaymentStatus.pending)
          .fold(0.0, (sum, order) => sum + order.total);
      final averageOrderValue = totalOrders > 0
          ? totalRevenue / totalOrders
          : 0.0;
      final conversionRate = totalOrders > 0
          ? (completedOrders / totalOrders) * 100
          : 0.0;

      print('🔥 Analytics calculated:');
      print('🔥 Total orders: $totalOrders');
      print('🔥 Completed orders: $completedOrders');
      print('🔥 Total revenue: $totalRevenue');

      final analyticsResult = {
        'totalRevenue': totalRevenue,
        'totalOrders': totalOrders,
        'completedOrders': completedOrders,
        'pendingOrders': pendingOrders,
        'paidAmount': paidAmount,
        'pendingAmount': pendingAmount,
        'averageOrderValue': averageOrderValue,
        'conversionRate': conversionRate,
      };

      print('🔥 Analytics result: $analyticsResult');
      return analyticsResult;
    } catch (e) {
      print('Error getting sales analytics: $e');
      return {
        'totalRevenue': 0.0,
        'totalOrders': 0,
        'completedOrders': 0,
        'pendingOrders': 0,
        'paidAmount': 0.0,
        'pendingAmount': 0.0,
        'averageOrderValue': 0.0,
        'conversionRate': 0.0,
      };
    }
  }

  // Get top selling products
  static Future<List<Map<String, dynamic>>> getTopSellingProducts({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async {
    try {
      // Build query to get top selling products
      String query = '''
        order_items!inner(
          product_id,
          quantity,
          products!inner(name, price, image_url),
          orders!inner(created_at, status)
        )
      ''';

      var queryBuilder = _supabase.from('order_items').select(query);

      // Add date filters
      if (startDate != null) {
        queryBuilder = queryBuilder.filter(
          'orders.created_at',
          'gte',
          startDate.toIso8601String(),
        );
      }
      if (endDate != null) {
        queryBuilder = queryBuilder.filter(
          'orders.created_at',
          'lte',
          endDate.toIso8601String(),
        );
      }

      // Only completed orders
      queryBuilder = queryBuilder.filter('orders.status', 'eq', 'completed');

      final response = await queryBuilder;

      // Group by product and calculate totals
      final Map<String, Map<String, dynamic>> productSales = {};

      for (final item in response) {
        final productId = item['product_id'] as String;
        final quantity = item['quantity'] as int;
        final product = item['products'];

        if (productSales.containsKey(productId)) {
          productSales[productId]!['totalQuantity'] += quantity;
          productSales[productId]!['totalRevenue'] +=
              quantity * (product['price'] as num).toDouble();
        } else {
          productSales[productId] = {
            'productId': productId,
            'productName': product['name'],
            'productPrice': (product['price'] as num).toDouble(),
            'productImage': product['image_url'],
            'totalQuantity': quantity,
            'totalRevenue': quantity * (product['price'] as num).toDouble(),
          };
        }
      }

      // Sort by total quantity and take top products
      final sortedProducts = productSales.values.toList()
        ..sort(
          (a, b) =>
              (b['totalQuantity'] as int).compareTo(a['totalQuantity'] as int),
        );

      return sortedProducts.take(limit).toList();
    } catch (e) {
      print('Error getting top selling products: $e');
      return [];
    }
  }

  // Update order
  static Future<sales.Order> updateOrder(String id, sales.Order order) async {
    try {
      final updateData = {
        'status': order.status.value,
        'payment_status': order.paymentStatus.name,
        'payment_method': order.paymentMethod.name,
        'notes': order.note,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase.from('orders').update(updateData).eq('id', id);

      final updatedOrder = await getOrderById(id);
      return updatedOrder ?? order;
    } catch (e) {
      throw Exception('Lỗi cập nhật đơn hàng: $e');
    }
  }

  // Update order status
  static Future<sales.Order> updateOrderStatus(String id, String status) async {
    try {
      await _supabase
          .from('orders')
          .update({
            'status': status,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', id);

      final updatedOrder = await getOrderById(id);
      if (updatedOrder == null) {
        throw Exception('Order not found after update');
      }
      return updatedOrder;
    } catch (e) {
      throw Exception('Lỗi cập nhật trạng thái đơn hàng: $e');
    }
  }

  // Update payment status
  static Future<sales.Order> updatePaymentStatus(
    String id,
    String paymentStatus,
  ) async {
    try {
      await _supabase
          .from('orders')
          .update({
            'payment_status': paymentStatus,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', id);

      final updatedOrder = await getOrderById(id);
      if (updatedOrder == null) {
        throw Exception('Order not found after update');
      }
      return updatedOrder;
    } catch (e) {
      throw Exception('Lỗi cập nhật trạng thái thanh toán: $e');
    }
  }

  // Delete order
  static Future<void> deleteOrder(String id) async {
    try {
      // Delete order items first (cascade should handle this, but being explicit)
      await _supabase.from('order_items').delete().eq('order_id', id);

      // Delete the order
      await _supabase.from('orders').delete().eq('id', id);
    } catch (e) {
      throw Exception('Lỗi xóa đơn hàng: $e');
    }
  }

  // =====================================================
  // PRODUCT & INVENTORY MANAGEMENT
  // =====================================================

  // Get all products with category information
  static Future<List<Map<String, dynamic>>> getProducts({
    String? search,
    String? categoryId,
    bool? isActive,
    int? limit,
    int? offset,
  }) async {
    try {
      dynamic queryBuilder = _supabase.from('products').select('''
        id, name, description, sku, barcode, price, cost,
        stock_quantity, min_stock_level, max_stock_level,
        unit, image_url, is_active, category_id,
        created_at, updated_at,
        categories(id, name, description, color, icon)
      ''');

      // Apply filters
      if (search != null && search.isNotEmpty) {
        queryBuilder = queryBuilder.or(
          'name.ilike.%$search%,sku.ilike.%$search%,barcode.ilike.%$search%',
        );
      }

      if (categoryId != null) {
        queryBuilder = queryBuilder.eq('category_id', categoryId);
      }

      if (isActive != null) {
        queryBuilder = queryBuilder.eq('is_active', isActive);
      }

      // Apply ordering first, then limit and range
      queryBuilder = queryBuilder.order('created_at', ascending: false);

      if (limit != null) {
        queryBuilder = queryBuilder.limit(limit);
      }

      if (offset != null) {
        queryBuilder = queryBuilder.range(offset, offset + (limit ?? 50) - 1);
      }

      final response = await queryBuilder;
      return response;
    } catch (e) {
      print('Error getting products: $e');
      return [];
    }
  }

  // Get product by ID with full details
  static Future<Map<String, dynamic>?> getProductById(String id) async {
    try {
      final response = await _supabase
          .from('products')
          .select('''
            id, name, description, sku, barcode, price, cost,
            stock_quantity, min_stock_level, max_stock_level,
            unit, image_url, is_active, category_id,
            created_at, updated_at,
            categories(id, name, description, color, icon)
          ''')
          .eq('id', id)
          .single();
      return response;
    } catch (e) {
      print('Error getting product by ID: $e');
      return null;
    }
  }

  // Get product by barcode/SKU
  static Future<Map<String, dynamic>?> getProductByBarcode(
    String barcode,
  ) async {
    try {
      final response = await _supabase
          .from('products')
          .select('''
            id, name, description, sku, barcode, price, cost,
            stock_quantity, min_stock_level, max_stock_level,
            unit, image_url, is_active, category_id,
            created_at, updated_at,
            categories(id, name, description, color, icon)
          ''')
          .or('barcode.eq.$barcode,sku.eq.$barcode,name.ilike.%$barcode%')
          .eq('is_active', true)
          .limit(1)
          .maybeSingle();

      return response;
    } catch (e) {
      print('Error getting product by barcode: $e');
      return null;
    }
  }

  // Update product stock quantity
  static Future<void> updateProductStock(
    String productId,
    int newQuantity,
  ) async {
    try {
      await _supabase
          .from('products')
          .update({
            'stock_quantity': newQuantity,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', productId);
    } catch (e) {
      throw Exception('Failed to update product stock: $e');
    }
  }

  // Bulk update product stock (for order processing)
  static Future<void> bulkUpdateProductStock(
    List<Map<String, dynamic>> updates,
  ) async {
    try {
      for (final update in updates) {
        await updateProductStock(
          update['productId'] as String,
          update['newQuantity'] as int,
        );
      }
    } catch (e) {
      throw Exception('Failed to bulk update product stock: $e');
    }
  }

  // Get low stock products
  static Future<List<Map<String, dynamic>>> getLowStockProducts() async {
    try {
      print('🔥 Getting low stock products...');

      // First, get all products to see what we have
      final allProducts = await _supabase
          .from('products')
          .select('id, name, sku, stock_quantity, min_stock_level, is_active')
          .eq('is_active', true);

      print('🔥 Total active products: ${allProducts.length}');

      // Filter low stock products manually since Supabase filter might not work as expected
      final lowStockProducts = <Map<String, dynamic>>[];

      for (final product in allProducts) {
        final stockQty = (product['stock_quantity'] as num?)?.toDouble() ?? 0.0;
        final minLevel =
            (product['min_stock_level'] as num?)?.toDouble() ?? 0.0;

        if (stockQty <= minLevel) {
          lowStockProducts.add(product);
          print(
            '🔥 Low stock: ${product['name']} - Stock: $stockQty, Min: $minLevel',
          );
        }
      }

      print('🔥 Found ${lowStockProducts.length} low stock products');
      return lowStockProducts;
    } catch (e) {
      print('🔥 Error getting low stock products: $e');
      return [];
    }
  }

  // Get partners (customers/suppliers)
  static Future<List<Partner>> getPartners({
    String? search,
    String? type,
    bool? isActive,
  }) async {
    try {
      print('🔥 Getting partners with filters:');
      print('🔥 type: $type, isActive: $isActive, search: $search');

      var query = _supabase.from('partners').select();

      if (search != null && search.isNotEmpty) {
        query = query.or(
          'name.ilike.%$search%,phone.ilike.%$search%,email.ilike.%$search%',
        );
      }

      if (type != null) {
        query = query.eq('type', type);
      }

      if (isActive != null) {
        query = query.eq('is_active', isActive);
      }

      final response = await query.order('name');
      final partners = response.map((json) => Partner.fromJson(json)).toList();

      print('🔥 Found ${partners.length} partners');
      for (final partner in partners.take(3)) {
        print('🔥 Partner: ${partner.name} (${partner.type})');
      }

      return partners;
    } catch (e) {
      print('🔥 Error getting partners: $e');
      return [];
    }
  }

  // =====================================================
  // INVOICE MANAGEMENT
  // =====================================================

  // Get all invoices
  static Future<List<Map<String, dynamic>>> getInvoices({
    String? search,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      var query = _supabase.from('invoices').select('''
        id, invoice_number, order_id, subtotal, tax_amount,
        discount_amount, total_amount, status, notes,
        created_at, updated_at,
        orders(order_number, partner_id, partners(name))
      ''');

      if (search != null && search.isNotEmpty) {
        query = query.or(
          'invoice_number.ilike.%$search%,notes.ilike.%$search%',
        );
      }

      if (status != null) {
        query = query.eq('status', status);
      }

      if (startDate != null) {
        query = query.filter('created_at', 'gte', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.filter('created_at', 'lte', endDate.toIso8601String());
      }

      final response = await query.order('created_at', ascending: false);
      return response;
    } catch (e) {
      print('Error getting invoices: $e');
      return [];
    }
  }

  // Get invoice by ID
  static Future<Map<String, dynamic>?> getInvoiceById(String id) async {
    try {
      final response = await _supabase
          .from('invoices')
          .select('''
            id, invoice_number, order_id, subtotal, tax_amount,
            discount_amount, total_amount, status, notes,
            created_at, updated_at,
            orders(order_number, partner_id, partners(name))
          ''')
          .eq('id', id)
          .single();
      return response;
    } catch (e) {
      print('Error getting invoice by ID: $e');
      return null;
    }
  }

  // Update invoice status
  static Future<void> updateInvoiceStatus(String id, String status) async {
    try {
      await _supabase
          .from('invoices')
          .update({
            'status': status,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', id);
    } catch (e) {
      throw Exception('Failed to update invoice status: $e');
    }
  }

  // =====================================================
  // PAYMENT MANAGEMENT
  // =====================================================

  // Get all payments
  static Future<List<Map<String, dynamic>>> getPayments({
    String? search,
    String? type,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      var query = _supabase.from('payments').select('''
        id, payment_number, type, category, amount,
        description, payment_method, payment_date,
        reference_type, reference_id, created_at, updated_at
      ''');

      if (search != null && search.isNotEmpty) {
        query = query.or(
          'payment_number.ilike.%$search%,description.ilike.%$search%',
        );
      }

      if (type != null) {
        query = query.eq('type', type);
      }

      if (category != null) {
        query = query.eq('category', category);
      }

      if (startDate != null) {
        query = query.filter(
          'payment_date',
          'gte',
          startDate.toIso8601String().split('T')[0],
        );
      }

      if (endDate != null) {
        query = query.filter(
          'payment_date',
          'lte',
          endDate.toIso8601String().split('T')[0],
        );
      }

      final response = await query.order('payment_date', ascending: false);
      return response;
    } catch (e) {
      print('Error getting payments: $e');
      return [];
    }
  }

  // Get payment summary
  static Future<Map<String, dynamic>> getPaymentSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      var query = _supabase.from('payments').select('type, amount');

      if (startDate != null) {
        query = query.filter(
          'payment_date',
          'gte',
          startDate.toIso8601String().split('T')[0],
        );
      }

      if (endDate != null) {
        query = query.filter(
          'payment_date',
          'lte',
          endDate.toIso8601String().split('T')[0],
        );
      }

      final response = await query;

      double totalIncome = 0.0;
      double totalExpense = 0.0;

      for (final payment in response) {
        final amount = (payment['amount'] as num).toDouble();
        if (payment['type'] == 'income') {
          totalIncome += amount;
        } else if (payment['type'] == 'expense') {
          totalExpense += amount;
        }
      }

      return {
        'totalIncome': totalIncome,
        'totalExpense': totalExpense,
        'netIncome': totalIncome - totalExpense,
        'transactionCount': response.length,
      };
    } catch (e) {
      print('Error getting payment summary: $e');
      return {
        'totalIncome': 0.0,
        'totalExpense': 0.0,
        'netIncome': 0.0,
        'transactionCount': 0,
      };
    }
  }

  // =====================================================
  // STOCK TRANSACTION MANAGEMENT
  // =====================================================

  // Create stock transaction for order items
  static Future<void> createStockTransactionsForOrder(sales.Order order) async {
    try {
      for (final item in order.items) {
        final currentStock = await _getProductStock(item.productId);
        final newStock = currentStock - item.quantity;
        await updateProductStock(item.productId, newStock);
      }
    } catch (e) {
      print('Error creating stock transactions: $e');
      rethrow;
    }
  }

  // Get product stock
  static Future<int> _getProductStock(String productId) async {
    try {
      final response = await _supabase
          .from('products')
          .select('stock')
          .eq('id', productId)
          .single();
      return response['stock'] as int;
    } catch (e) {
      print('Error getting product stock: $e');
      return 0;
    }
  }

  // Get stock transactions
  static Future<List<Map<String, dynamic>>> getStockTransactions({
    String? productId,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      var query = _supabase.from('stock_transactions').select('''
        id, product_id, type, quantity, unit_cost,
        reference_type, reference_id, notes, created_at,
        products(name, sku, unit)
      ''');

      if (productId != null) {
        query = query.eq('product_id', productId);
      }

      if (type != null) {
        query = query.eq('type', type);
      }

      if (startDate != null) {
        query = query.filter('created_at', 'gte', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.filter('created_at', 'lte', endDate.toIso8601String());
      }

      final response = await query.order('created_at', ascending: false);
      return response;
    } catch (e) {
      print('Error getting stock transactions: $e');
      return [];
    }
  }

  // Get stock movement summary
  static Future<Map<String, dynamic>> getStockMovementSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      var query = _supabase.from('stock_transactions').select('type, quantity');

      if (startDate != null) {
        query = query.filter('created_at', 'gte', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.filter('created_at', 'lte', endDate.toIso8601String());
      }

      final response = await query;

      int totalIn = 0;
      int totalOut = 0;

      for (final transaction in response) {
        final quantity = transaction['quantity'] as int;
        if (transaction['type'] == 'in') {
          totalIn += quantity;
        } else if (transaction['type'] == 'out') {
          totalOut += quantity;
        }
      }

      return {
        'totalIn': totalIn,
        'totalOut': totalOut,
        'netMovement': totalIn - totalOut,
        'transactionCount': response.length,
      };
    } catch (e) {
      print('Error getting stock movement summary: $e');
      return {
        'totalIn': 0,
        'totalOut': 0,
        'netMovement': 0,
        'transactionCount': 0,
      };
    }
  }

  // =====================================================
  // CATEGORY MANAGEMENT
  // =====================================================

  // Get all categories
  static Future<List<Map<String, dynamic>>> getCategories({
    String? search,
    bool? isActive,
  }) async {
    try {
      var query = _supabase.from('categories').select('''
        id, name, description, color, icon, is_active,
        created_at, updated_at
      ''');

      if (search != null && search.isNotEmpty) {
        query = query.or('name.ilike.%$search%,description.ilike.%$search%');
      }

      if (isActive != null) {
        query = query.eq('is_active', isActive);
      }

      final response = await query.order('name');
      return response;
    } catch (e) {
      print('Error getting categories: $e');
      return [];
    }
  }

  // Get products count by category
  static Future<Map<String, int>> getProductsCountByCategory() async {
    try {
      final response = await _supabase
          .from('products')
          .select('category_id')
          .eq('is_active', true);

      final Map<String, int> counts = {};
      for (final product in response) {
        final categoryId = product['category_id'] as String?;
        if (categoryId != null) {
          counts[categoryId] = (counts[categoryId] ?? 0) + 1;
        }
      }

      return counts;
    } catch (e) {
      print('Error getting products count by category: $e');
      return {};
    }
  }

  // Helper methods
  static String _generateOrderNumber() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'HD$timestamp';
  }

  // Create invoice from order
  static Future<void> _createInvoiceFromOrder(sales.Order order) async {
    try {
      await InvoiceService.createInvoiceFromOrder(navigatorKey.currentContext!, order);
    } catch (e) {
      print('Error creating invoice from order: $e');
      rethrow;
    }
  }

  // Record cash receipt
  static Future<void> _recordCashReceipt(sales.Order order) async {
    try {
      await PaymentService.createReceiptFromOrder(navigatorKey.currentContext!, order);
    } catch (e) {
      print('Error recording cash receipt: $e');
      rethrow;
    }
  }

  // Send push notification for new order
  static Future<void> _sendOrderNotification(sales.Order order) async {
    try {
      final notification = {
        'title': 'New Order',
        'body': 'Order ${order.orderNumber} has been created',
        'data': {
          'orderId': order.id,
          'orderNumber': order.orderNumber,
        },
      };

      await PushNotificationService.sendNotification(notification);
    } catch (e) {
      print('Error sending order notification: $e');
    }
  }

  // Handle order completion
  Future<void> _handleOrderCompletion(BuildContext context, sales.Order order) async {
    try {
      // Create invoice from order
      await InvoiceService.createInvoiceFromOrder(context, order);

      // Create receipt from order
      await PaymentService.createReceiptFromOrder(context, order);

      // Update order status
      if (order.id != null) {
        await updateOrderStatus(order.id!, sales.OrderStatus.completed.value);
      }
    } catch (e) {
      debugPrint('Error handling order completion: $e');
      rethrow;
    }
  }
}
