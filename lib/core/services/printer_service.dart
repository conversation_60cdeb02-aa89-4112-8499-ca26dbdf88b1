import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart' as printing;

import '../../data/models/printer.dart';
import '../../features/sales/domain/entities/order.dart';

class PrinterService {
  static const String _boxName = 'printers';
  static Box<Printer>? _box;

  // Initialize Hive box
  static Future<void> init() async {
    try {
      _box = await Hive.openBox<Printer>(_boxName);
      debugPrint('✅ PrinterService initialized');
    } catch (e) {
      debugPrint('❌ Error initializing PrinterService: $e');
    }
  }

  // Get all printers
  static List<Printer> getAllPrinters() {
    try {
      return _box?.values.toList() ?? [];
    } catch (e) {
      debugPrint('❌ Error getting printers: $e');
      return [];
    }
  }

  // Get default printer
  static Printer? getDefaultPrinter() {
    try {
      final printers = getAllPrinters();
      return printers.where((p) => p.isDefault && p.isActive).firstOrNull;
    } catch (e) {
      debugPrint('❌ Error getting default printer: $e');
      return null;
    }
  }

  // Add new printer
  static Future<bool> addPrinter(Printer printer) async {
    try {
      await _box?.put(printer.id, printer);
      debugPrint('✅ Added printer: ${printer.name}');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding printer: $e');
      return false;
    }
  }

  // Update printer
  static Future<bool> updatePrinter(Printer printer) async {
    try {
      await _box?.put(printer.id, printer);
      debugPrint('✅ Updated printer: ${printer.name}');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating printer: $e');
      return false;
    }
  }

  // Delete printer
  static Future<bool> deletePrinter(String printerId) async {
    try {
      await _box?.delete(printerId);
      debugPrint('✅ Deleted printer: $printerId');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting printer: $e');
      return false;
    }
  }

  // Set default printer
  static Future<bool> setDefaultPrinter(String printerId) async {
    try {
      final printers = getAllPrinters();

      // Remove default from all printers
      for (final printer in printers) {
        if (printer.isDefault) {
          await updatePrinter(printer.copyWith(isDefault: false));
        }
      }

      // Set new default
      final targetPrinter = printers
          .where((p) => p.id == printerId)
          .firstOrNull;
      if (targetPrinter != null) {
        await updatePrinter(targetPrinter.copyWith(isDefault: true));
        debugPrint('✅ Set default printer: ${targetPrinter.name}');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('❌ Error setting default printer: $e');
      return false;
    }
  }

  // Test printer connection
  static Future<bool> testPrinterConnection(Printer printer) async {
    try {
      debugPrint('🔍 Testing connection to ${printer.displayAddress}');

      final socket = await Socket.connect(
        printer.ipAddress,
        printer.port,
        timeout: const Duration(seconds: 5),
      );

      await socket.close();
      debugPrint('✅ Connection test successful: ${printer.name}');
      return true;
    } catch (e) {
      debugPrint('❌ Connection test failed: ${printer.name} - $e');
      return false;
    }
  }

  // Print invoice
  static Future<bool> printInvoice(Order order, {Printer? printer}) async {
    try {
      final targetPrinter = printer ?? getDefaultPrinter();
      if (targetPrinter == null) {
        debugPrint('❌ No printer available for printing');
        return false;
      }

      debugPrint('🖨️ Printing invoice to ${targetPrinter.name}');

      // Generate PDF invoice
      final pdfBytes = await _generateInvoicePdf(order);

      // Print PDF using printing package
      final success = await printing.Printing.directPrintPdf(
        printer: printing.Printer(
          url:
              'ipp://${targetPrinter.ipAddress}:${targetPrinter.port}/ipp/print',
        ),
        onLayout: (format) => pdfBytes,
      );

      if (success) {
        // Update last used time
        await updatePrinter(targetPrinter.copyWith(lastUsedAt: DateTime.now()));
        debugPrint('✅ Invoice printed successfully');
        return true;
      } else {
        debugPrint('❌ Failed to print invoice');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error printing invoice: $e');
      return false;
    }
  }

  // Generate PDF invoice
  static Future<Uint8List> _generateInvoicePdf(Order order) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header
              pw.Center(
                child: pw.Column(
                  children: [
                    pw.Text(
                      'CITY POS',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 8),
                    pw.Text(
                      'HÓA ĐƠN BÁN HÀNG',
                      style: pw.TextStyle(
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              pw.SizedBox(height: 20),

              // Order info
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text('Mã HĐ: ${order.orderNumber}'),
                      pw.Text('Ngày: ${_formatDateTime(order.createdAt)}'),
                      if (order.customerName != null)
                        pw.Text('Khách hàng: ${order.customerName}'),
                      if (order.customerPhone != null)
                        pw.Text('SĐT: ${order.customerPhone}'),
                    ],
                  ),
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Text('Thu ngân: ${order.createdBy ?? 'N/A'}'),
                      pw.Text('Trạng thái: ${order.status.displayName}'),
                    ],
                  ),
                ],
              ),
              pw.SizedBox(height: 20),

              // Items table
              pw.Table(
                border: pw.TableBorder.all(),
                children: [
                  // Header row
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(
                      color: PdfColors.grey300,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'Sản phẩm',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'SL',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'Đơn giá',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'Thành tiền',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  // Item rows
                  ...order.items.map((item) {
                    final itemTotal = item.quantity * item.unitPrice;
                    return pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(item.productName),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(item.quantity.toString()),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(_formatCurrency(item.unitPrice)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(_formatCurrency(itemTotal)),
                        ),
                      ],
                    );
                  }),
                ],
              ),
              pw.SizedBox(height: 20),

              // Totals
              pw.Align(
                alignment: pw.Alignment.centerRight,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Text('Tạm tính: ${_formatCurrency(order.subtotal)}'),
                    if (order.discount > 0)
                      pw.Text('Giảm giá: -${_formatCurrency(order.discount)}'),
                    if (order.tax > 0)
                      pw.Text('Thuế: ${_formatCurrency(order.tax)}'),
                    pw.Divider(),
                    pw.Text(
                      'Tổng cộng: ${_formatCurrency(order.total)}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text('Thanh toán: ${_formatCurrency(order.amountPaid)}'),
                    if (order.change > 0)
                      pw.Text('Tiền thừa: ${_formatCurrency(order.change)}'),
                  ],
                ),
              ),
              pw.SizedBox(height: 30),

              // Footer
              pw.Center(
                child: pw.Column(
                  children: [
                    pw.Text('Cảm ơn quý khách!'),
                    pw.Text('Hẹn gặp lại!'),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  // Helper methods
  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/'
        '${dateTime.month.toString().padLeft(2, '0')}/'
        '${dateTime.year} '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}đ';
  }
}
