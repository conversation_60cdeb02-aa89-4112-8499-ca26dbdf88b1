import 'package:supabase_flutter/supabase_flutter.dart';

import '../../data/models/category.dart';
import '../../data/models/product.dart';
import '../config/supabase_config.dart';

class ProductService {
  static final SupabaseClient? _supabase = SupabaseConfig.client;

  // Get all products
  static Future<List<Product>> getProducts({
    String? search,
    String? categoryId,
    bool? isActive,
    int? limit,
    int? offset,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      await Future.delayed(
        const Duration(milliseconds: 500),
      ); // Simulate network delay

      var products = _getDemoProducts();

      // Apply filters
      if (search != null && search.isNotEmpty) {
        final searchLower = search.toLowerCase();
        products = products
            .where(
              (p) =>
                  p.name.toLowerCase().contains(searchLower) ||
                  (p.sku?.toLowerCase().contains(searchLower) ?? false) ||
                  (p.barcode?.toLowerCase().contains(searchLower) ?? false),
            )
            .toList();
      }

      if (categoryId != null) {
        products = products.where((p) => p.categoryId == categoryId).toList();
      }

      if (isActive != null) {
        products = products.where((p) => p.isActive == isActive).toList();
      }

      // Apply pagination
      if (offset != null) {
        products = products.skip(offset).toList();
      }
      if (limit != null) {
        products = products.take(limit).toList();
      }

      return products;
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      print('🔍 Loading products from Supabase...');

      var queryBuilder = _supabase!.from('products').select('''
        id, name, description, sku, barcode, price, cost,
        stock_quantity, min_stock_level, max_stock_level,
        unit, image_url, is_active, category_id,
        created_at, updated_at,
        categories(id, name, description, color, icon)
      ''');

      // Apply filters
      if (search != null && search.isNotEmpty) {
        queryBuilder = queryBuilder.or(
          'name.ilike.%$search%,sku.ilike.%$search%,barcode.ilike.%$search%',
        );
      }

      if (categoryId != null) {
        queryBuilder = queryBuilder.eq('category_id', categoryId);
      }

      if (isActive != null) {
        queryBuilder = queryBuilder.eq('is_active', isActive);
      }

      // Order and execute query
      final response = await queryBuilder.order('created_at', ascending: false);

      print('📦 Loaded ${response.length} products from Supabase');

      return response.map<Product>((data) {
        return Product(
          id: data['id'],
          name: data['name'],
          description: data['description'],
          sku: data['sku'],
          barcode: data['barcode'],
          categoryId: data['category_id'],
          // categoryName: data['categories']?['name'], // Remove this line - not in Product model
          price: (data['price'] as num?)?.toDouble() ?? 0.0,
          cost: (data['cost'] as num?)?.toDouble() ?? 0.0,
          stockQuantity: data['stock_quantity'] ?? 0,
          minStockLevel: data['min_stock_level'] ?? 0,
          maxStockLevel: data['max_stock_level'],
          unit: data['unit'] ?? 'pcs',
          imageUrl: data['image_url'],
          isActive: data['is_active'] ?? true,
          createdAt: data['created_at'] != null
              ? DateTime.parse(data['created_at'])
              : DateTime.now(),
          updatedAt: data['updated_at'] != null
              ? DateTime.parse(data['updated_at'])
              : DateTime.now(),
        );
      }).toList();
    } catch (e) {
      print('❌ Error loading products: $e');
      throw Exception('Failed to load products: $e');
    }
  }

  // Get product by ID
  static Future<Product?> getProductById(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      try {
        return _getDemoProducts().firstWhere((p) => p.id == id);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Get product by SKU
  static Future<Product?> getProductBySku(String sku) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      try {
        return _getDemoProducts().firstWhere((p) => p.sku == sku);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Get product by barcode
  static Future<Product?> getProductByBarcode(String barcode) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      try {
        // Try multiple fields for flexible matching
        return _getDemoProducts().firstWhere(
          (p) =>
              p.barcode?.toLowerCase() == barcode.toLowerCase() ||
              p.sku?.toLowerCase() == barcode.toLowerCase() ||
              p.name.toLowerCase() == barcode.toLowerCase() ||
              p.name.toLowerCase().contains(barcode.toLowerCase()),
        );
      } catch (e) {
        return null;
      }
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase!
          .from('products')
          .select('''
            id, name, description, sku, barcode, price, cost,
            stock_quantity, min_stock_level, max_stock_level,
            unit, image_url, is_active, category_id,
            created_at, updated_at
          ''')
          .or('barcode.eq.$barcode,sku.eq.$barcode,name.ilike.%$barcode%')
          .eq('is_active', true)
          .limit(1)
          .maybeSingle();

      if (response == null) return null;

      return Product(
        id: response['id'],
        name: response['name'],
        description: response['description'],
        sku: response['sku'],
        barcode: response['barcode'],
        categoryId: response['category_id'],
        price: (response['price'] as num?)?.toDouble() ?? 0.0,
        cost: (response['cost'] as num?)?.toDouble() ?? 0.0,
        stockQuantity: response['stock_quantity'] ?? 0,
        minStockLevel: response['min_stock_level'] ?? 0,
        maxStockLevel: response['max_stock_level'],
        unit: response['unit'] ?? 'pcs',
        imageUrl: response['image_url'],
        isActive: response['is_active'] ?? true,
        createdAt: response['created_at'] != null
            ? DateTime.parse(response['created_at'])
            : DateTime.now(),
        updatedAt: response['updated_at'] != null
            ? DateTime.parse(response['updated_at'])
            : DateTime.now(),
      );
    } catch (e) {
      print('❌ Error getting product by barcode: $e');
      return null;
    }
  }

  // Create product
  static Future<Product> createProduct(Product product) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      await Future.delayed(const Duration(milliseconds: 500));

      // Simulate creating product with generated ID
      final newProduct = product.copyWith(
        id: 'product_${DateTime.now().millisecondsSinceEpoch}',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      return newProduct;
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase!
          .from('products')
          .insert({
            'name': product.name,
            'description': product.description,
            'sku': product.sku,
            'barcode': product.barcode,
            'category_id': product.categoryId,
            'price': product.price,
            'cost': product.cost,
            'stock_quantity': product.stockQuantity,
            'min_stock_level': product.minStockLevel,
            'max_stock_level': product.maxStockLevel,
            'unit': product.unit,
            'image_url': product.imageUrl,
            'is_active': product.isActive,
          })
          .select()
          .single();

      return Product(
        id: response['id'],
        name: response['name'],
        description: response['description'],
        sku: response['sku'],
        barcode: response['barcode'],
        categoryId: response['category_id'],
        price: (response['price'] as num?)?.toDouble() ?? 0.0,
        cost: (response['cost'] as num?)?.toDouble() ?? 0.0,
        stockQuantity: response['stock_quantity'] ?? 0,
        minStockLevel: response['min_stock_level'] ?? 0,
        maxStockLevel: response['max_stock_level'],
        unit: response['unit'] ?? 'pcs',
        imageUrl: response['image_url'],
        isActive: response['is_active'] ?? true,
        createdAt: response['created_at'] != null
            ? DateTime.parse(response['created_at'])
            : DateTime.now(),
        updatedAt: response['updated_at'] != null
            ? DateTime.parse(response['updated_at'])
            : DateTime.now(),
      );
    } catch (e) {
      throw Exception('Failed to create product: $e');
    }
  }

  // Update product
  static Future<Product> updateProduct(String id, Product product) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      await Future.delayed(const Duration(milliseconds: 500));

      // Simulate updating product
      final updatedProduct = product.copyWith(
        id: id,
        updatedAt: DateTime.now(),
      );

      return updatedProduct;
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase!
          .from('products')
          .update({
            'name': product.name,
            'description': product.description,
            'sku': product.sku,
            'barcode': product.barcode,
            'category_id': product.categoryId,
            'price': product.price,
            'cost': product.cost,
            'stock_quantity': product.stockQuantity,
            'min_stock_level': product.minStockLevel,
            'max_stock_level': product.maxStockLevel,
            'unit': product.unit,
            'image_url': product.imageUrl,
            'is_active': product.isActive,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', id)
          .select()
          .single();

      return Product(
        id: response['id'],
        name: response['name'],
        description: response['description'],
        sku: response['sku'],
        barcode: response['barcode'],
        categoryId: response['category_id'],
        price: (response['price'] as num?)?.toDouble() ?? 0.0,
        cost: (response['cost'] as num?)?.toDouble() ?? 0.0,
        stockQuantity: response['stock_quantity'] ?? 0,
        minStockLevel: response['min_stock_level'] ?? 0,
        maxStockLevel: response['max_stock_level'],
        unit: response['unit'] ?? 'pcs',
        imageUrl: response['image_url'],
        isActive: response['is_active'] ?? true,
        createdAt: response['created_at'] != null
            ? DateTime.parse(response['created_at'])
            : DateTime.now(),
        updatedAt: response['updated_at'] != null
            ? DateTime.parse(response['updated_at'])
            : DateTime.now(),
      );
    } catch (e) {
      throw Exception('Failed to update product: $e');
    }
  }

  // Delete product
  static Future<void> deleteProduct(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      await Future.delayed(const Duration(milliseconds: 500));
      // Simulate deletion
      return;
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      await _supabase!.from('products').delete().eq('id', id);
    } catch (e) {
      throw Exception('Failed to delete product: $e');
    }
  }

  // Get low stock products
  static Future<List<Product>> getLowStockProducts() async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      final products = _getDemoProducts();
      return products.where((p) => p.stockQuantity <= p.minStockLevel).toList();
    }
    return [];
  }

  // Update stock
  static Future<void> updateStock(String productId, int newQuantity) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      await Future.delayed(const Duration(milliseconds: 300));
      // Simulate stock update
      return;
    }
    throw Exception('Demo mode - cannot update stock');
  }

  // Bulk update stock
  static Future<void> bulkUpdateStock(
    List<Map<String, dynamic>> updates,
  ) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      await Future.delayed(const Duration(milliseconds: 500));
      // Simulate bulk stock update
      return;
    }
    throw Exception('Demo mode - cannot bulk update stock');
  }

  // Get categories
  static Future<List<Category>> getCategories() async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      await Future.delayed(const Duration(milliseconds: 300));
      return _getDemoCategories();
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase!
          .from('categories')
          .select(
            'id, name, description, color, icon, is_active, created_at, updated_at',
          )
          .eq('is_active', true)
          .order('name');

      return response.map<Category>((data) {
        return Category(
          id: data['id'],
          name: data['name'],
          description: data['description'],
          color: data['color'] ?? '#3B82F6',
          icon: data['icon'] ?? 'category',
          isActive: data['is_active'] ?? true,
          createdAt: data['created_at'] != null
              ? DateTime.parse(data['created_at'])
              : DateTime.now(),
          updatedAt: data['updated_at'] != null
              ? DateTime.parse(data['updated_at'])
              : DateTime.now(),
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to load categories: $e');
    }
  }

  // Create category
  static Future<Category> createCategory(Category category) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      await Future.delayed(const Duration(milliseconds: 500));

      final newCategory = category.copyWith(
        id: 'category_${DateTime.now().millisecondsSinceEpoch}',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      return newCategory;
    }
    throw Exception('Demo mode - cannot create categories');
  }

  // Update category
  static Future<Category> updateCategory(String id, Category category) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      await Future.delayed(const Duration(milliseconds: 500));

      final updatedCategory = category.copyWith(
        id: id,
        updatedAt: DateTime.now(),
      );

      return updatedCategory;
    }
    throw Exception('Demo mode - cannot update categories');
  }

  // Delete category
  static Future<void> deleteCategory(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      await Future.delayed(const Duration(milliseconds: 500));
      // Simulate deletion
      return;
    }
    throw Exception('Demo mode - cannot delete categories');
  }

  // Get products count by category
  static Future<Map<String, int>> getProductsCountByCategory() async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      final products = _getDemoProducts();
      final Map<String, int> counts = {};

      for (final product in products) {
        if (product.categoryId != null) {
          counts[product.categoryId!] = (counts[product.categoryId!] ?? 0) + 1;
        }
      }

      return counts;
    }
    return {};
  }

  // Demo data methods
  static List<Product> _getDemoProducts() {
    final now = DateTime.now();

    return [
      Product(
        id: '1',
        name: 'Cà phê đen',
        sku: 'CF001',
        barcode: '8934567890123',
        categoryId: 'cat_1',
        description: 'Cà phê đen nguyên chất, hương vị đậm đà',
        price: 25000,
        cost: 15000,
        stockQuantity: 50,
        minStockLevel: 10,
        unit: 'ly',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
      Product(
        id: '2',
        name: 'Cà phê sữa',
        sku: 'CF002',
        barcode: '8934567890124',
        categoryId: 'cat_1',
        description: 'Cà phê sữa đá truyền thống',
        price: 30000,
        cost: 18000,
        stockQuantity: 45,
        minStockLevel: 10,
        unit: 'ly',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 25)),
        updatedAt: now.subtract(const Duration(days: 2)),
      ),
      Product(
        id: '3',
        name: 'Bánh mì thịt',
        sku: 'BM001',
        barcode: '8934567890125',
        categoryId: 'cat_2',
        description: 'Bánh mì thịt nướng với rau củ tươi',
        price: 35000,
        cost: 20000,
        stockQuantity: 20,
        minStockLevel: 5,
        unit: 'cái',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 20)),
        updatedAt: now.subtract(const Duration(days: 3)),
      ),
      Product(
        id: '4',
        name: 'Nước cam',
        sku: 'NC001',
        barcode: '8934567890126',
        categoryId: 'cat_1',
        description: 'Nước cam tươi ép 100%',
        price: 20000,
        cost: 12000,
        stockQuantity: 30,
        minStockLevel: 15,
        unit: 'chai',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 15)),
        updatedAt: now.subtract(const Duration(days: 4)),
      ),
      Product(
        id: '5',
        name: 'Bánh ngọt',
        sku: 'BN001',
        barcode: '8934567890127',
        categoryId: 'cat_3',
        description: 'Bánh ngọt tươi làm hàng ngày',
        price: 15000,
        cost: 8000,
        stockQuantity: 25,
        minStockLevel: 10,
        unit: 'cái',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
      Product(
        id: '6',
        name: 'Trà đá',
        sku: 'TD001',
        barcode: '8934567890128',
        categoryId: 'cat_1',
        description: 'Trà đá mát lạnh',
        price: 10000,
        cost: 5000,
        stockQuantity: 100,
        minStockLevel: 20,
        unit: 'ly',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now,
      ),
      Product(
        id: '7',
        name: 'Sandwich',
        sku: 'SW001',
        barcode: '8934567890129',
        categoryId: 'cat_2',
        description: 'Sandwich thịt nguội và rau xanh',
        price: 45000,
        cost: 25000,
        stockQuantity: 15,
        minStockLevel: 5,
        unit: 'cái',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 8)),
        updatedAt: now.subtract(const Duration(hours: 12)),
      ),
      Product(
        id: '8',
        name: 'Nước suối',
        sku: 'NS001',
        barcode: '8934567890130',
        categoryId: 'cat_1',
        description: 'Nước suối tinh khiết',
        price: 8000,
        cost: 4000,
        stockQuantity: 200,
        minStockLevel: 50,
        unit: 'chai',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 12)),
        updatedAt: now.subtract(const Duration(hours: 6)),
      ),
      Product(
        id: '9',
        name: 'Bánh croissant',
        sku: 'BC001',
        barcode: '8934567890131',
        categoryId: 'cat_3',
        description: 'Bánh croissant bơ thơm ngon',
        price: 25000,
        cost: 12000,
        stockQuantity: 8,
        minStockLevel: 10,
        unit: 'cái',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 6)),
        updatedAt: now.subtract(const Duration(hours: 3)),
      ),
      Product(
        id: '10',
        name: 'Sữa tươi',
        sku: 'ST001',
        barcode: '8934567890132',
        categoryId: 'cat_1',
        description: 'Sữa tươi nguyên kem',
        price: 18000,
        cost: 12000,
        stockQuantity: 3,
        minStockLevel: 20,
        unit: 'hộp',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 4)),
        updatedAt: now.subtract(const Duration(hours: 1)),
      ),
    ];
  }

  static List<Category> _getDemoCategories() {
    final now = DateTime.now();

    return [
      Category(
        id: 'cat_1',
        name: 'Đồ uống',
        description: 'Các loại đồ uống nóng và lạnh',
        color: '#2196F3', // Blue
        icon: 'local_drink',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 60)),
        updatedAt: now.subtract(const Duration(days: 10)),
      ),
      Category(
        id: 'cat_2',
        name: 'Thức ăn',
        description: 'Các món ăn nhanh và nhẹ',
        color: '#4CAF50', // Green
        icon: 'restaurant',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 55)),
        updatedAt: now.subtract(const Duration(days: 8)),
      ),
      Category(
        id: 'cat_3',
        name: 'Bánh kẹo',
        description: 'Bánh ngọt và kẹo các loại',
        color: '#FF9800', // Orange
        icon: 'cake',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 50)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
      Category(
        id: 'cat_4',
        name: 'Gia vị',
        description: 'Gia vị và nguyên liệu nấu ăn',
        color: '#9C27B0', // Purple
        icon: 'kitchen',
        isActive: true,
        createdAt: now.subtract(const Duration(days: 45)),
        updatedAt: now.subtract(const Duration(days: 3)),
      ),
    ];
  }
}
