import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:city_pos/generated/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

import '../../data/models/order.dart';
import '../config/supabase_config.dart';

class InvoiceService {
  static final SupabaseClient? _supabase = SupabaseConfig.client;

  // Create invoice from order
  static Future<void> createInvoiceFromOrder(BuildContext context, Order order) async {
    try {
      print('✅ ${AppLocalizations.of(context).invoiceCreatedFromOrder(order.orderNumber ?? 'N/A')}');

      if (_supabase == null) {
        throw Exception('Supabase not initialized');
      }

      // Real Supabase implementation
      final invoiceData = {
        'invoice_number':
            'INV${order.orderNumber?.substring(2) ?? DateTime.now().millisecondsSinceEpoch}',
        'order_id': order.id,
        // 'customer_name': order.notes?.contains('Khách hàng:') == true
        //     ? order.notes!.split('Khách hàng: ')[1].split(' - ')[0]
        //     : null, // Column doesn't exist in DB
        'subtotal': order.subtotal,
        'tax_amount': order.taxAmount,
        'discount_amount': order.discountAmount,
        'total_amount': order.totalAmount,
        'status': 'paid',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase!.from('invoices').insert(invoiceData);
    } catch (e) {
      print('Error creating invoice: $e');
      throw Exception('${AppLocalizations.of(context).errorCreatingInvoice}: $e');
    }
  }
}
