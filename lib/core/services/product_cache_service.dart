import 'package:hive_flutter/hive_flutter.dart';

import '../../data/models/product.dart';
import '../../data/models/category.dart';

class ProductCacheService {
  static const String _productsBoxName = 'products_cache';
  static const String _categoriesBoxName = 'categories_cache';
  static const String _lastSyncKey = 'last_sync_timestamp';
  
  static Box<Product>? _productsBox;
  static Box<Category>? _categoriesBox;
  static Box<String>? _metadataBox;

  // Initialize cache service
  static Future<void> initialize() async {
    try {
      // Register adapters if not already registered
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(ProductAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(CategoryAdapter());
      }

      // Open boxes
      _productsBox = await Hive.openBox<Product>(_productsBoxName);
      _categoriesBox = await Hive.openBox<Category>(_categoriesBoxName);
      _metadataBox = await Hive.openBox<String>('cache_metadata');
      
      print('✅ ProductCacheService initialized successfully');
    } catch (e) {
      print('❌ Error initializing ProductCacheService: $e');
      rethrow;
    }
  }

  // =====================================================
  // PRODUCTS CACHE MANAGEMENT
  // =====================================================

  // Cache products from API
  static Future<void> cacheProducts(List<Product> products) async {
    try {
      if (_productsBox == null) await initialize();
      
      // Clear existing cache
      await _productsBox!.clear();
      
      // Cache new products
      for (int i = 0; i < products.length; i++) {
        await _productsBox!.put(products[i].id ?? i.toString(), products[i]);
      }
      
      // Update last sync timestamp
      await _updateLastSyncTimestamp();
      
      print('✅ Cached ${products.length} products');
    } catch (e) {
      print('❌ Error caching products: $e');
      rethrow;
    }
  }

  // Get cached products
  static Future<List<Product>> getCachedProducts({
    String? search,
    String? categoryId,
    bool? isActive,
  }) async {
    try {
      if (_productsBox == null) await initialize();
      
      List<Product> products = _productsBox!.values.toList();
      
      // Apply filters
      if (search != null && search.isNotEmpty) {
        final searchLower = search.toLowerCase();
        products = products.where((p) =>
          p.name.toLowerCase().contains(searchLower) ||
          (p.sku?.toLowerCase().contains(searchLower) ?? false) ||
          (p.barcode?.toLowerCase().contains(searchLower) ?? false)
        ).toList();
      }
      
      if (categoryId != null) {
        products = products.where((p) => p.categoryId == categoryId).toList();
      }
      
      if (isActive != null) {
        products = products.where((p) => p.isActive == isActive).toList();
      }
      
      print('📦 Retrieved ${products.length} cached products');
      return products;
    } catch (e) {
      print('❌ Error getting cached products: $e');
      return [];
    }
  }

  // Get cached product by ID
  static Future<Product?> getCachedProductById(String id) async {
    try {
      if (_productsBox == null) await initialize();
      return _productsBox!.get(id);
    } catch (e) {
      print('❌ Error getting cached product by ID: $e');
      return null;
    }
  }

  // Get cached product by SKU
  static Future<Product?> getCachedProductBySku(String sku) async {
    try {
      if (_productsBox == null) await initialize();
      
      for (final product in _productsBox!.values) {
        if (product.sku == sku) {
          return product;
        }
      }
      return null;
    } catch (e) {
      print('❌ Error getting cached product by SKU: $e');
      return null;
    }
  }

  // Get cached product by barcode
  static Future<Product?> getCachedProductByBarcode(String barcode) async {
    try {
      if (_productsBox == null) await initialize();
      
      for (final product in _productsBox!.values) {
        if (product.barcode == barcode || product.sku == barcode || 
            product.name.toLowerCase().contains(barcode.toLowerCase())) {
          return product;
        }
      }
      return null;
    } catch (e) {
      print('❌ Error getting cached product by barcode: $e');
      return null;
    }
  }

  // Update cached product
  static Future<void> updateCachedProduct(Product product) async {
    try {
      if (_productsBox == null) await initialize();
      if (product.id != null) {
        await _productsBox!.put(product.id!, product);
        print('✅ Updated cached product: ${product.name}');
      }
    } catch (e) {
      print('❌ Error updating cached product: $e');
    }
  }

  // Add new product to cache
  static Future<void> addProductToCache(Product product) async {
    try {
      if (_productsBox == null) await initialize();
      if (product.id != null) {
        await _productsBox!.put(product.id!, product);
        print('✅ Added product to cache: ${product.name}');
      }
    } catch (e) {
      print('❌ Error adding product to cache: $e');
    }
  }

  // Remove product from cache
  static Future<void> removeProductFromCache(String productId) async {
    try {
      if (_productsBox == null) await initialize();
      await _productsBox!.delete(productId);
      print('✅ Removed product from cache: $productId');
    } catch (e) {
      print('❌ Error removing product from cache: $e');
    }
  }

  // =====================================================
  // CATEGORIES CACHE MANAGEMENT
  // =====================================================

  // Cache categories from API
  static Future<void> cacheCategories(List<Category> categories) async {
    try {
      if (_categoriesBox == null) await initialize();
      
      // Clear existing cache
      await _categoriesBox!.clear();
      
      // Cache new categories
      for (int i = 0; i < categories.length; i++) {
        await _categoriesBox!.put(categories[i].id ?? i.toString(), categories[i]);
      }
      
      print('✅ Cached ${categories.length} categories');
    } catch (e) {
      print('❌ Error caching categories: $e');
      rethrow;
    }
  }

  // Get cached categories
  static Future<List<Category>> getCachedCategories() async {
    try {
      if (_categoriesBox == null) await initialize();
      
      List<Category> categories = _categoriesBox!.values.toList();
      print('📦 Retrieved ${categories.length} cached categories');
      return categories;
    } catch (e) {
      print('❌ Error getting cached categories: $e');
      return [];
    }
  }

  // Get cached category by ID
  static Future<Category?> getCachedCategoryById(String id) async {
    try {
      if (_categoriesBox == null) await initialize();
      return _categoriesBox!.get(id);
    } catch (e) {
      print('❌ Error getting cached category by ID: $e');
      return null;
    }
  }

  // =====================================================
  // CACHE METADATA MANAGEMENT
  // =====================================================

  // Update last sync timestamp
  static Future<void> _updateLastSyncTimestamp() async {
    try {
      if (_metadataBox == null) await initialize();
      await _metadataBox!.put(_lastSyncKey, DateTime.now().toIso8601String());
    } catch (e) {
      print('❌ Error updating last sync timestamp: $e');
    }
  }

  // Get last sync timestamp
  static Future<DateTime?> getLastSyncTimestamp() async {
    try {
      if (_metadataBox == null) await initialize();
      final timestamp = _metadataBox!.get(_lastSyncKey);
      return timestamp != null ? DateTime.parse(timestamp) : null;
    } catch (e) {
      print('❌ Error getting last sync timestamp: $e');
      return null;
    }
  }

  // Check if cache needs refresh (older than 1 hour)
  static Future<bool> needsRefresh() async {
    try {
      final lastSync = await getLastSyncTimestamp();
      if (lastSync == null) return true;
      
      final now = DateTime.now();
      final difference = now.difference(lastSync);
      
      // Refresh if cache is older than 1 hour
      return difference.inHours >= 1;
    } catch (e) {
      print('❌ Error checking cache refresh: $e');
      return true;
    }
  }

  // Check if cache is empty
  static Future<bool> isEmpty() async {
    try {
      if (_productsBox == null) await initialize();
      return _productsBox!.isEmpty;
    } catch (e) {
      print('❌ Error checking cache empty: $e');
      return true;
    }
  }

  // Clear all cache
  static Future<void> clearCache() async {
    try {
      if (_productsBox == null) await initialize();
      
      await _productsBox!.clear();
      await _categoriesBox!.clear();
      await _metadataBox!.clear();
      
      print('✅ Cache cleared successfully');
    } catch (e) {
      print('❌ Error clearing cache: $e');
    }
  }

  // Get cache statistics
  static Future<Map<String, dynamic>> getCacheStats() async {
    try {
      if (_productsBox == null) await initialize();
      
      final lastSync = await getLastSyncTimestamp();
      
      return {
        'productsCount': _productsBox!.length,
        'categoriesCount': _categoriesBox!.length,
        'lastSync': lastSync?.toIso8601String(),
        'needsRefresh': await needsRefresh(),
        'isEmpty': await isEmpty(),
      };
    } catch (e) {
      print('❌ Error getting cache stats: $e');
      return {
        'productsCount': 0,
        'categoriesCount': 0,
        'lastSync': null,
        'needsRefresh': true,
        'isEmpty': true,
        'error': e.toString(),
      };
    }
  }
}
