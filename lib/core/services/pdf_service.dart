import 'dart:io';

import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';

import '../../data/models/order.dart';

class PDFService {
  // Generate and share invoice PDF
  Future<void> generateAndShareInvoice(Order order) async {
    try {
      final pdfFile = await generateInvoicePDF(order);

      await Share.shareXFiles(
        [XFile(pdfFile.path)],
        text: 'Hóa đơn ${order.orderNumber ?? 'N/A'}',
        subject: 'Hóa đơn bán hàng - City POS',
      );
    } catch (e) {
      throw Exception('Lỗi khi tạo và chia sẻ PDF: $e');
    }
  }

  // Generate invoice PDF from order
  static Future<File> generateInvoicePDF(Order order) async {
    final pdf = pw.Document();

    // Load font for Vietnamese text
    final fontData = await rootBundle.load('assets/fonts/Roboto-Regular.ttf');
    final ttf = pw.Font.ttf(fontData);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(ttf),
              pw.SizedBox(height: 20),

              // Invoice info
              _buildInvoiceInfo(order, ttf),
              pw.SizedBox(height: 20),

              // Customer info
              _buildCustomerInfo(order, ttf),
              pw.SizedBox(height: 20),

              // Items table
              _buildItemsTable(order, ttf),
              pw.SizedBox(height: 20),

              // Total
              _buildTotal(order, ttf),
              pw.SizedBox(height: 30),

              // Footer
              _buildFooter(ttf),
            ],
          );
        },
      ),
    );

    // Save PDF to file
    final output = await getTemporaryDirectory();
    final file = File(
      '${output.path}/invoice_${order.orderNumber ?? DateTime.now().millisecondsSinceEpoch}.pdf',
    );
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  // Build header section
  static pw.Widget _buildHeader(pw.Font font) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            'CITY POS',
            style: pw.TextStyle(
              font: font,
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'HÓA ĐƠN BÁN HÀNG',
            style: pw.TextStyle(
              font: font,
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'SALES INVOICE',
            style: pw.TextStyle(font: font, fontSize: 14),
          ),
        ],
      ),
    );
  }

  // Build invoice info section
  static pw.Widget _buildInvoiceInfo(Order order, pw.Font font) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'Số hóa đơn: ${order.orderNumber ?? 'N/A'}',
              style: pw.TextStyle(font: font, fontSize: 12),
            ),
            pw.SizedBox(height: 5),
            pw.Text(
              'Ngày: ${_formatDate(order.createdAt)}',
              style: pw.TextStyle(font: font, fontSize: 12),
            ),
          ],
        ),
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            pw.Text(
              'Trạng thái: ${order.status}',
              style: pw.TextStyle(font: font, fontSize: 12),
            ),
            pw.SizedBox(height: 5),
            pw.Text(
              'Thanh toán: ${order.paymentMethod ?? 'Tiền mặt'}',
              style: pw.TextStyle(font: font, fontSize: 12),
            ),
          ],
        ),
      ],
    );
  }

  // Build customer info section
  static pw.Widget _buildCustomerInfo(Order order, pw.Font font) {
    String customerName = 'Khách lẻ';
    String customerPhone = '';

    // Extract customer info from notes if available
    if (order.notes != null && order.notes!.contains('Khách hàng:')) {
      final parts = order.notes!.split(' - ');
      if (parts.isNotEmpty) {
        customerName = parts[0].replaceAll('Khách hàng: ', '');
      }
      if (parts.length > 1) {
        customerPhone = parts[1].replaceAll('SĐT: ', '');
      }
    }

    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
        borderRadius: pw.BorderRadius.circular(5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'THÔNG TIN KHÁCH HÀNG',
            style: pw.TextStyle(
              font: font,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'Tên: $customerName',
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
          if (customerPhone.isNotEmpty) ...[
            pw.SizedBox(height: 3),
            pw.Text(
              'SĐT: $customerPhone',
              style: pw.TextStyle(font: font, fontSize: 11),
            ),
          ],
        ],
      ),
    );
  }

  // Build items table
  static pw.Widget _buildItemsTable(Order order, pw.Font font) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey),
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell('STT', font, isHeader: true),
            _buildTableCell('Tên sản phẩm', font, isHeader: true),
            _buildTableCell('SL', font, isHeader: true),
            _buildTableCell('Đơn giá', font, isHeader: true),
            _buildTableCell('Thành tiền', font, isHeader: true),
          ],
        ),
        // Items
        ...order.items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          return pw.TableRow(
            children: [
              _buildTableCell('${index + 1}', font),
              _buildTableCell(item.productName ?? 'N/A', font),
              _buildTableCell('${item.quantity}', font),
              _buildTableCell(_formatCurrency(item.unitPrice), font),
              _buildTableCell(_formatCurrency(item.totalAmount), font),
            ],
          );
        }),
      ],
    );
  }

  // Build table cell
  static pw.Widget _buildTableCell(
    String text,
    pw.Font font, {
    bool isHeader = false,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: isHeader ? 11 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  // Build total section
  static pw.Widget _buildTotal(Order order, pw.Font font) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Container(
            width: 200,
            child: pw.Column(
              children: [
                _buildTotalRow(
                  'Tạm tính:',
                  _formatCurrency(order.subtotal),
                  font,
                ),
                if (order.discountAmount > 0)
                  _buildTotalRow(
                    'Giảm giá:',
                    '-${_formatCurrency(order.discountAmount)}',
                    font,
                  ),
                pw.Divider(color: PdfColors.grey),
                _buildTotalRow(
                  'TỔNG CỘNG:',
                  _formatCurrency(order.totalAmount),
                  font,
                  isTotal: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build total row
  static pw.Widget _buildTotalRow(
    String label,
    String value,
    pw.Font font, {
    bool isTotal = false,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              font: font,
              fontSize: isTotal ? 12 : 11,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
              font: font,
              fontSize: isTotal ? 12 : 11,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  // Build footer
  static pw.Widget _buildFooter(pw.Font font) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            'Cảm ơn quý khách đã mua hàng!',
            style: pw.TextStyle(
              font: font,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'Thank you for your purchase!',
            style: pw.TextStyle(font: font, fontSize: 10),
          ),
        ],
      ),
    );
  }

  // Format date
  static String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  // Format currency
  static String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}₫';
  }
}
