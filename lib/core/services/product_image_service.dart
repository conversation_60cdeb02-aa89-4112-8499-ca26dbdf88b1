import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../config/supabase_config.dart';
import 'supabase_storage_service.dart';

class ProductImageService {
  static final SupabaseClient _supabase = SupabaseConfig.client;
  static const String bucketName = 'city-pos';

  // Upload product image
  static Future<Map<String, dynamic>> uploadProductImage({
    required String productId,
    required File imageFile,
    String? fileName,
    String? altText,
    String? description,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Ensure user exists in public.users table
      await _ensureUserExists(userId);

      // Generate file path: user_id/product/year/month/
      final now = DateTime.now();
      final year = now.year.toString();
      final month = now.month.toString().padLeft(2, '0');
      final filePath = '$userId/product/$year/$month';

      // Upload image to storage
      final uploadResult = await SupabaseStorageService.uploadImage(
        bucketName: bucketName,
        filePath: filePath,
        imageFile: imageFile,
        fileName: fileName,
        isPublic: true,
        maxWidth: 1200, // Max width for product images
        maxHeight: 1200, // Max height for product images
        quality: 85,
      );

      // Create storage_files record
      final storageFileData = {
        'file_name': uploadResult['file_name'],
        'file_path': uploadResult['file_path'],
        'bucket_name': bucketName,
        'file_type': 'image',
        'file_category': 'product',
        'mime_type': uploadResult['mime_type'],
        'file_size': uploadResult['file_size'],
        'file_extension': _getFileExtension(uploadResult['file_name']),
        'width': uploadResult['width'],
        'height': uploadResult['height'],
        'entity_type': 'product',
        'entity_id': productId,
        'is_public': true,
        'access_url': uploadResult['public_url'],
        'alt_text': altText ?? 'Product image',
        'description': description,
        'uploaded_by': userId,
      };

      final storageFileResponse = await _supabase
          .from('storage_files')
          .insert(storageFileData)
          .select()
          .single();

      // Update product with image_file_id
      await _supabase
          .from('products')
          .update({'image_file_id': storageFileResponse['id']})
          .eq('id', productId);

      return {
        'storage_file_id': storageFileResponse['id'],
        'file_path': uploadResult['file_path'],
        'public_url': uploadResult['public_url'],
        'file_size': uploadResult['file_size'],
        'width': uploadResult['width'],
        'height': uploadResult['height'],
      };
    } catch (e) {
      debugPrint('❌ Error uploading product image: $e');
      throw Exception('Failed to upload product image: $e');
    }
  }

  // Get product image info
  static Future<Map<String, dynamic>?> getProductImage(String productId) async {
    try {
      final response = await _supabase
          .from('storage_files')
          .select()
          .eq('entity_type', 'product')
          .eq('entity_id', productId)
          .eq('file_category', 'product')
          .maybeSingle();

      return response;
    } catch (e) {
      debugPrint('❌ Error getting product image: $e');
      return null;
    }
  }

  // Get product image URL
  static Future<String?> getProductImageUrl(String productId) async {
    try {
      final imageInfo = await getProductImage(productId);
      if (imageInfo == null) return null;

      if (imageInfo['is_public'] == true && imageInfo['access_url'] != null) {
        return imageInfo['access_url'];
      }

      // Generate signed URL for private images
      return await SupabaseStorageService.getSignedUrl(
        bucketName: imageInfo['bucket_name'],
        filePath: imageInfo['file_path'],
      );
    } catch (e) {
      debugPrint('❌ Error getting product image URL: $e');
      return null;
    }
  }

  // Update product image
  static Future<Map<String, dynamic>> updateProductImage({
    required String productId,
    required File imageFile,
    String? fileName,
    String? altText,
    String? description,
  }) async {
    try {
      // Delete old image first
      await deleteProductImage(productId);

      // Upload new image
      return await uploadProductImage(
        productId: productId,
        imageFile: imageFile,
        fileName: fileName,
        altText: altText,
        description: description,
      );
    } catch (e) {
      debugPrint('❌ Error updating product image: $e');
      throw Exception('Failed to update product image: $e');
    }
  }

  // Delete product image
  static Future<void> deleteProductImage(String productId) async {
    try {
      // Get current image info
      final imageInfo = await getProductImage(productId);
      if (imageInfo == null) return;

      // Delete from storage
      await SupabaseStorageService.deleteFile(
        bucketName: imageInfo['bucket_name'],
        filePath: imageInfo['file_path'],
      );

      // Delete storage_files record
      await _supabase.from('storage_files').delete().eq('id', imageInfo['id']);

      // Remove reference from product
      await _supabase
          .from('products')
          .update({'image_file_id': null})
          .eq('id', productId);
    } catch (e) {
      debugPrint('❌ Error deleting product image: $e');
      throw Exception('Failed to delete product image: $e');
    }
  }

  // Get multiple product images (for future use - multiple images per product)
  static Future<List<Map<String, dynamic>>> getProductImages(
    String productId,
  ) async {
    try {
      final response = await _supabase
          .from('storage_files')
          .select()
          .eq('entity_type', 'product')
          .eq('entity_id', productId)
          .eq('file_category', 'product')
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error getting product images: $e');
      return [];
    }
  }

  // Check if product has image
  static Future<bool> hasProductImage(String productId) async {
    try {
      final imageInfo = await getProductImage(productId);
      return imageInfo != null;
    } catch (e) {
      return false;
    }
  }

  // Get product image metadata
  static Future<Map<String, dynamic>?> getProductImageMetadata(
    String productId,
  ) async {
    try {
      final imageInfo = await getProductImage(productId);
      if (imageInfo == null) return null;

      return {
        'file_name': imageInfo['file_name'],
        'file_size': imageInfo['file_size'],
        'width': imageInfo['width'],
        'height': imageInfo['height'],
        'mime_type': imageInfo['mime_type'],
        'alt_text': imageInfo['alt_text'],
        'description': imageInfo['description'],
        'created_at': imageInfo['created_at'],
      };
    } catch (e) {
      debugPrint('❌ Error getting product image metadata: $e');
      return null;
    }
  }

  // Migrate existing image_url to storage system
  static Future<void> migrateProductImageUrl({
    required String productId,
    required String imageUrl,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Create storage_files record for existing URL
      final storageFileData = {
        'file_name': 'migrated-product-$productId.jpg',
        'file_path': imageUrl, // Store original URL as path for migration
        'bucket_name': 'external', // Mark as external URL
        'file_type': 'image',
        'file_category': 'product',
        'mime_type': 'image/jpeg',
        'entity_type': 'product',
        'entity_id': productId,
        'is_public': true,
        'access_url': imageUrl,
        'alt_text': 'Migrated product image',
        'uploaded_by': userId,
      };

      final storageFileResponse = await _supabase
          .from('storage_files')
          .insert(storageFileData)
          .select()
          .single();

      // Update product with image_file_id
      await _supabase
          .from('products')
          .update({'image_file_id': storageFileResponse['id']})
          .eq('id', productId);
    } catch (e) {
      debugPrint('❌ Error migrating product image URL: $e');
      throw Exception('Failed to migrate product image URL: $e');
    }
  }

  // Helper methods
  static String _getFileExtension(String fileName) {
    final parts = fileName.split('.');
    return parts.length > 1 ? '.${parts.last}' : '';
  }

  // Ensure user exists in public.users table
  static Future<void> _ensureUserExists(String userId) async {
    try {
      // Check if user exists in public.users
      final existingUser = await _supabase
          .from('users')
          .select('id')
          .eq('id', userId)
          .maybeSingle();

      if (existingUser != null) return; // User already exists

      // Get user info from auth
      final authUser = _supabase.auth.currentUser;
      if (authUser == null) return;

      // Create user record in public.users
      await _supabase.from('users').insert({
        'id': userId,
        'email': authUser.email ?? '',
        'full_name':
            authUser.userMetadata?['full_name'] ?? authUser.email ?? 'User',
        'role': 'user',
        'is_active': true,
      });

      debugPrint('✅ Created user record for $userId');
    } catch (e) {
      debugPrint('⚠️ Could not ensure user exists: $e');
      // Don't throw error, let the upload continue
    }
  }

  // Validate image file
  static bool isValidImageFile(File file) {
    final extension = _getFileExtension(file.path).toLowerCase();
    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    return validExtensions.contains(extension);
  }

  // Get image file size in MB
  static Future<double> getImageFileSizeMB(File file) async {
    final bytes = await file.length();
    return bytes / (1024 * 1024);
  }

  // Check if image file size is within limit
  static Future<bool> isImageSizeValid(
    File file, {
    double maxSizeMB = 5.0,
  }) async {
    final sizeMB = await getImageFileSizeMB(file);
    return sizeMB <= maxSizeMB;
  }

  // Update image product ID (for transferring from temp ID to real ID)
  static Future<void> updateImageProductId({
    required String imageId,
    required String newProductId,
  }) async {
    try {
      await _supabase
          .from('storage_files')
          .update({'entity_id': newProductId})
          .eq('id', imageId);

      debugPrint('✅ Updated image $imageId to product $newProductId');
    } catch (e) {
      debugPrint('❌ Error updating image product ID: $e');
      rethrow;
    }
  }
}
