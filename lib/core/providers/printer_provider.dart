import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/printer.dart';
import '../services/printer_service.dart';

// Printer list provider
final printerListProvider = StateNotifierProvider<PrinterListNotifier, List<Printer>>((ref) {
  return PrinterListNotifier();
});

// Default printer provider
final defaultPrinterProvider = Provider<Printer?>((ref) {
  final printers = ref.watch(printerListProvider);
  return printers.where((p) => p.isDefault && p.isActive).firstOrNull;
});

// Printer list notifier
class PrinterListNotifier extends StateNotifier<List<Printer>> {
  PrinterListNotifier() : super([]) {
    loadPrinters();
  }

  // Load printers from storage
  Future<void> loadPrinters() async {
    try {
      final printers = PrinterService.getAllPrinters();
      state = printers;
      debugPrint('✅ Loaded ${printers.length} printers');
    } catch (e) {
      debugPrint('❌ Error loading printers: $e');
      state = [];
    }
  }

  // Add new printer
  Future<bool> addPrinter(Printer printer) async {
    try {
      final success = await PrinterService.addPrinter(printer);
      if (success) {
        state = [...state, printer];
        debugPrint('✅ Added printer to state: ${printer.name}');
      }
      return success;
    } catch (e) {
      debugPrint('❌ Error adding printer to state: $e');
      return false;
    }
  }

  // Update printer
  Future<bool> updatePrinter(Printer printer) async {
    try {
      final success = await PrinterService.updatePrinter(printer);
      if (success) {
        state = state.map((p) => p.id == printer.id ? printer : p).toList();
        debugPrint('✅ Updated printer in state: ${printer.name}');
      }
      return success;
    } catch (e) {
      debugPrint('❌ Error updating printer in state: $e');
      return false;
    }
  }

  // Delete printer
  Future<bool> deletePrinter(String printerId) async {
    try {
      final success = await PrinterService.deletePrinter(printerId);
      if (success) {
        state = state.where((p) => p.id != printerId).toList();
        debugPrint('✅ Deleted printer from state: $printerId');
      }
      return success;
    } catch (e) {
      debugPrint('❌ Error deleting printer from state: $e');
      return false;
    }
  }

  // Set default printer
  Future<bool> setDefaultPrinter(String printerId) async {
    try {
      final success = await PrinterService.setDefaultPrinter(printerId);
      if (success) {
        state = state.map((p) {
          if (p.id == printerId) {
            return p.copyWith(isDefault: true);
          } else if (p.isDefault) {
            return p.copyWith(isDefault: false);
          }
          return p;
        }).toList();
        debugPrint('✅ Set default printer in state: $printerId');
      }
      return success;
    } catch (e) {
      debugPrint('❌ Error setting default printer in state: $e');
      return false;
    }
  }

  // Test printer connection
  Future<bool> testPrinterConnection(Printer printer) async {
    try {
      return await PrinterService.testPrinterConnection(printer);
    } catch (e) {
      debugPrint('❌ Error testing printer connection: $e');
      return false;
    }
  }

  // Toggle printer active status
  Future<bool> togglePrinterStatus(String printerId) async {
    try {
      final printer = state.where((p) => p.id == printerId).firstOrNull;
      if (printer == null) return false;

      final updatedPrinter = printer.copyWith(isActive: !printer.isActive);
      return await updatePrinter(updatedPrinter);
    } catch (e) {
      debugPrint('❌ Error toggling printer status: $e');
      return false;
    }
  }

  // Get active printers
  List<Printer> getActivePrinters() {
    return state.where((p) => p.isActive).toList();
  }

  // Get printer by ID
  Printer? getPrinterById(String id) {
    return state.where((p) => p.id == id).firstOrNull;
  }

  // Check if printer name exists
  bool isPrinterNameExists(String name, {String? excludeId}) {
    return state.any((p) => p.name.toLowerCase() == name.toLowerCase() && p.id != excludeId);
  }

  // Check if printer IP exists
  bool isPrinterIpExists(String ipAddress, int port, {String? excludeId}) {
    return state.any((p) => p.ipAddress == ipAddress && p.port == port && p.id != excludeId);
  }
}
