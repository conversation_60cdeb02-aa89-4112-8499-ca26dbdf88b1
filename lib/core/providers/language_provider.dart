import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageNotifier extends StateNotifier<Locale> {
  LanguageNotifier() : super(const Locale('vi', 'VN')) {
    _loadLanguage();
  }

  static const String _languageKey = 'selected_language';

  Future<void> _loadLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString(_languageKey) ?? 'vi';

    switch (languageCode) {
      case 'en':
        state = const Locale('en', 'US');
        break;
      case 'ja':
        state = const Locale('ja', 'JP');
        break;
      case 'vi':
      default:
        state = const Locale('vi', 'VN');
        break;
    }
  }

  Future<void> changeLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);

    switch (languageCode) {
      case 'en':
        state = const Locale('en', 'US');
        break;
      case 'ja':
        state = const Locale('ja', 'JP');
        break;
      case 'vi':
      default:
        state = const Locale('vi', 'VN');
        break;
    }
  }

  String get currentLanguageCode => state.languageCode;

  bool get isVietnamese => state.languageCode == 'vi';
  bool get isEnglish => state.languageCode == 'en';
  bool get isJapanese => state.languageCode == 'ja';
}

final languageProvider = StateNotifierProvider<LanguageNotifier, Locale>((ref) {
  return LanguageNotifier();
});
