import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/notifications/data/services/notification_service.dart';

// Provider for unread notification count
final unreadNotificationCountProvider =
    StateNotifierProvider<UnreadNotificationCountNotifier, int>((ref) {
      return UnreadNotificationCountNotifier();
    });

// Global provider instance for external refresh
UnreadNotificationCountNotifier? _globalUnreadCountNotifier;

class UnreadNotificationCountNotifier extends StateNotifier<int> {
  Timer? _refreshTimer;

  UnreadNotificationCountNotifier() : super(0) {
    _globalUnreadCountNotifier = this;
    loadUnreadCount();
    _startPeriodicRefresh();
  }

  Future<void> loadUnreadCount() async {
    try {
      final count = await NotificationService.getUnreadCount();
      state = count;
    } catch (e) {
      // Ignore errors, just keep previous count
    }
  }

  void _startPeriodicRefresh() {
    // Refresh unread count every 10 seconds for real-time updates
    _refreshTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      loadUnreadCount();
    });
  }

  void refresh() {
    loadUnreadCount();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    if (_globalUnreadCountNotifier == this) {
      _globalUnreadCountNotifier = null;
    }
    super.dispose();
  }
}

// Global function to refresh unread count from anywhere
void refreshGlobalUnreadCount() {
  _globalUnreadCountNotifier?.refresh();
}
