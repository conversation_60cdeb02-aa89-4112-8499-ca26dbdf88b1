import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Invoice settings state
class InvoiceSettings {
  final bool showInvoiceAfterPayment;

  const InvoiceSettings({
    this.showInvoiceAfterPayment = true, // Default to true for better UX
  });

  InvoiceSettings copyWith({bool? showInvoiceAfterPayment}) {
    return InvoiceSettings(
      showInvoiceAfterPayment:
          showInvoiceAfterPayment ?? this.showInvoiceAfterPayment,
    );
  }
}

// Invoice settings notifier
class InvoiceSettingsNotifier extends StateNotifier<InvoiceSettings> {
  static const String _showInvoiceKey = 'show_invoice_after_payment';

  InvoiceSettingsNotifier() : super(const InvoiceSettings()) {
    _loadSettings();
  }

  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final showInvoice =
          prefs.getBool(_showInvoiceKey) ?? true; // Default to true

      state = InvoiceSettings(showInvoiceAfterPayment: showInvoice);
    } catch (e) {
      // If error loading, keep default settings
      debugPrint('Error loading invoice settings: $e');
    }
  }

  // Toggle show invoice after payment setting
  Future<void> toggleShowInvoiceAfterPayment() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final newValue = !state.showInvoiceAfterPayment;

      await prefs.setBool(_showInvoiceKey, newValue);
      state = state.copyWith(showInvoiceAfterPayment: newValue);
    } catch (e) {
      debugPrint('Error saving invoice settings: $e');
    }
  }

  // Set show invoice after payment setting
  Future<void> setShowInvoiceAfterPayment(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_showInvoiceKey, value);
      state = state.copyWith(showInvoiceAfterPayment: value);
    } catch (e) {
      debugPrint('Error saving invoice settings: $e');
    }
  }
}

// Provider for invoice settings
final invoiceSettingsProvider =
    StateNotifierProvider<InvoiceSettingsNotifier, InvoiceSettings>(
      (ref) => InvoiceSettingsNotifier(),
    );
