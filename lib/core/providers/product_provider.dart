import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/category.dart';
import '../../data/models/product.dart';
import '../../data/services/category_service.dart';
import '../services/product_service.dart';

// Product state
class ProductState {
  final List<Product> products;
  final List<Category> categories;
  final bool isLoading;
  final String? error;
  final String? searchQuery;
  final String? selectedCategoryId;

  const ProductState({
    this.products = const [],
    this.categories = const [],
    this.isLoading = false,
    this.error,
    this.searchQuery,
    this.selectedCategoryId,
  });

  ProductState copyWith({
    List<Product>? products,
    List<Category>? categories,
    bool? isLoading,
    String? error,
    String? searchQuery,
    String? selectedCategoryId,
  }) {
    return ProductState(
      products: products ?? this.products,
      categories: categories ?? this.categories,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCategoryId: selectedCategoryId ?? this.selectedCategoryId,
    );
  }
}

// Product notifier
class ProductNotifier extends StateNotifier<ProductState> {
  ProductNotifier() : super(const ProductState()) {
    loadProducts();
    loadCategories();
  }

  Future<void> loadProducts({bool forceRefresh = false}) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final products = await ProductService.getProducts(
        search: state.searchQuery,
        categoryId: state.selectedCategoryId,
        isActive: true,
        forceRefresh: forceRefresh,
      );

      state = state.copyWith(products: products, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> loadCategories({bool forceRefresh = false}) async {
    try {
      final categories = await ProductService.getCategories(
        forceRefresh: forceRefresh,
      );
      state = state.copyWith(categories: categories);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> searchProducts(String query) async {
    state = state.copyWith(searchQuery: query);
    await loadProducts();
  }

  Future<void> filterByCategory(String? categoryId) async {
    try {
      // First update the selected category ID
      state = state.copyWith(
        selectedCategoryId: categoryId,
        isLoading: true,
        error: null,
      );

      // Then load products with the new filter
      final products = await ProductService.getProducts(
        search: state.searchQuery,
        categoryId: categoryId,
        isActive: true,
      );

      // Finally update the products list
      state = state.copyWith(products: products, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  // Force refresh from API (bypass cache)
  Future<void> refreshFromApi() async {
    await loadProducts(forceRefresh: true);
    await loadCategories(forceRefresh: true);
  }

  Future<Product?> createProduct(Product product) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final newProduct = await ProductService.createProduct(product);

      state = state.copyWith(
        products: [newProduct, ...state.products],
        isLoading: false,
      );

      return newProduct;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return null;
    }
  }

  Future<bool> updateProduct(String id, Product product) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final updatedProduct = await ProductService.updateProduct(id, product);

      final updatedProducts = state.products.map((p) {
        return p.id == id ? updatedProduct : p;
      }).toList();

      state = state.copyWith(products: updatedProducts, isLoading: false);

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  Future<bool> deleteProduct(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await ProductService.deleteProduct(id);

      final updatedProducts = state.products.where((p) => p.id != id).toList();

      state = state.copyWith(products: updatedProducts, isLoading: false);

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  Future<bool> createCategory(Category category) async {
    try {
      final newCategory = await ProductService.createCategory(category);

      state = state.copyWith(categories: [newCategory, ...state.categories]);

      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void clearSearch() {
    state = state.copyWith(searchQuery: null, selectedCategoryId: null);
    loadProducts();
  }

  // Category management methods
  Future<bool> addCategory(Category category) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final newCategory = await CategoryService.createCategory(category);
      final updatedCategories = [...state.categories, newCategory];
      state = state.copyWith(categories: updatedCategories, isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  Future<bool> updateCategory(String id, Category category) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final success = await CategoryService.updateCategory(id, category);
      if (success) {
        final updatedCategories = state.categories.map((c) {
          return c.id == id ? category.copyWith(id: id) : c;
        }).toList();

        state = state.copyWith(categories: updatedCategories, isLoading: false);
        return true;
      }
      return false;
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  Future<bool> deleteCategory(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final success = await CategoryService.deleteCategory(id);
      if (success) {
        final updatedCategories = state.categories
            .where((c) => c.id != id)
            .toList();
        state = state.copyWith(categories: updatedCategories, isLoading: false);
        return true;
      }
      return false;
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }
}

// Providers
final productProvider = StateNotifierProvider<ProductNotifier, ProductState>((
  ref,
) {
  return ProductNotifier();
});

// Convenience providers
final productsListProvider = Provider<List<Product>>((ref) {
  return ref.watch(productProvider.select((state) => state.products));
});

final categoriesListProvider = Provider<List<Category>>((ref) {
  return ref.watch(productProvider.select((state) => state.categories));
});

final isProductsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(productProvider.select((state) => state.isLoading));
});

final productErrorProvider = Provider<String?>((ref) {
  return ref.watch(productProvider.select((state) => state.error));
});

// Low stock products provider
final lowStockProductsProvider = FutureProvider<List<Product>>((ref) async {
  return await ProductService.getLowStockProducts();
});

// Product by ID provider
final productByIdProvider = FutureProvider.family<Product?, String>((
  ref,
  id,
) async {
  return await ProductService.getProductById(id);
});

// Product by SKU provider
final productBySkuProvider = FutureProvider.family<Product?, String>((
  ref,
  sku,
) async {
  return await ProductService.getProductBySku(sku);
});

// Product by barcode provider
final productByBarcodeProvider = FutureProvider.family<Product?, String>((
  ref,
  barcode,
) async {
  return await ProductService.getProductByBarcode(barcode);
});
