import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/product.dart';
import 'product_provider.dart';

// POS State
class PosState {
  final bool isLoading;
  final String? error;
  final List<Product> products;
  final List<Product> filteredProducts;
  final String selectedCategory;
  final String searchQuery;

  const PosState({
    this.isLoading = false,
    this.error,
    this.products = const [],
    this.filteredProducts = const [],
    this.selectedCategory = 'all',
    this.searchQuery = '',
  });

  PosState copyWith({
    bool? isLoading,
    String? error,
    List<Product>? products,
    List<Product>? filteredProducts,
    String? selectedCategory,
    String? searchQuery,
  }) {
    return PosState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      products: products ?? this.products,
      filteredProducts: filteredProducts ?? this.filteredProducts,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

// POS Notifier that syncs with ProductProvider
class PosNotifier extends StateNotifier<PosState> {
  final Ref ref;

  PosNotifier(this.ref) : super(const PosState()) {
    // Listen to product provider changes
    ref.listen<ProductState>(productProvider, (previous, next) {
      // Sync products from inventory to POS
      _syncProducts(next.products);
    });

    // Initial load
    loadProducts();
  }

  void _syncProducts(List<Product> products) {
    // Only show active products in POS
    final activeProducts = products.where((p) => p.isActive).toList();

    state = state.copyWith(
      products: activeProducts,
      filteredProducts: _applyFilters(activeProducts),
    );
  }

  Future<void> loadProducts() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Load products using ProductProvider to ensure consistency
      await ref.read(productProvider.notifier).loadProducts();

      // Products will be synced via listener
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void searchProducts(String query) {
    state = state.copyWith(searchQuery: query);
    _filterProducts();
  }

  void selectCategory(String category) {
    state = state.copyWith(selectedCategory: category);
    _filterProducts();
  }

  void _filterProducts() {
    final filtered = _applyFilters(state.products);
    state = state.copyWith(filteredProducts: filtered);
  }

  List<Product> _applyFilters(List<Product> products) {
    List<Product> filtered = products;

    // Filter by category
    if (state.selectedCategory != 'all' && state.selectedCategory.isNotEmpty) {
      filtered = filtered
          .where((p) => p.categoryId == state.selectedCategory)
          .toList();
    }

    // Filter by search query
    if (state.searchQuery.isNotEmpty) {
      final searchLower = state.searchQuery.toLowerCase();
      filtered = filtered
          .where(
            (p) =>
                p.name.toLowerCase().contains(searchLower) ||
                (p.sku?.toLowerCase().contains(searchLower) ?? false) ||
                (p.barcode?.toLowerCase().contains(searchLower) ?? false),
          )
          .toList();
    }

    return filtered;
  }

  // Get categories for filtering
  Future<List<Map<String, dynamic>>> getCategories() async {
    return []; // TODO: Implement categories
  }

  // Refresh products
  Future<void> refresh() async {
    await ref.read(productProvider.notifier).loadProducts();
  }
}

// POS Provider
final posProductProvider = StateNotifierProvider<PosNotifier, PosState>((ref) {
  return PosNotifier(ref);
});

// Convenience providers
final posProductsProvider = Provider<List<Product>>((ref) {
  return ref.watch(
    posProductProvider.select((state) => state.filteredProducts),
  );
});

final posLoadingProvider = Provider<bool>((ref) {
  return ref.watch(posProductProvider.select((state) => state.isLoading));
});

final posErrorProvider = Provider<String?>((ref) {
  return ref.watch(posProductProvider.select((state) => state.error));
});

final posSearchQueryProvider = Provider<String>((ref) {
  return ref.watch(posProductProvider.select((state) => state.searchQuery));
});

final posSelectedCategoryProvider = Provider<String>((ref) {
  return ref.watch(
    posProductProvider.select((state) => state.selectedCategory),
  );
});

// Categories provider for POS
final posCategoriesProvider = FutureProvider<List<Map<String, dynamic>>>((
  ref,
) async {
  return await ref.read(posProductProvider.notifier).getCategories();
});
