import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../services/auth_service.dart';
import '../services/storage_service.dart';

// Auth state enum
enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

// Auth state class
class AuthState {
  final AuthStatus status;
  final User? user;
  final String? error;
  final Map<String, dynamic>? userProfile;

  const AuthState({
    required this.status,
    this.user,
    this.error,
    this.userProfile,
  });

  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? error,
    Map<String, dynamic>? userProfile,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      error: error ?? this.error,
      userProfile: userProfile ?? this.userProfile,
    );
  }
}

// Auth notifier
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(const AuthState(status: AuthStatus.initial)) {
    _init();
  }

  void _init() async {
    // Initialize storage service
    await StorageService.init();

    // Check initial auth state
    final user = AuthService.currentUser;
    if (user != null) {
      state = state.copyWith(status: AuthStatus.authenticated, user: user);
      _loadUserProfile();
    } else {
      // Check for auto login
      await _checkAutoLogin();
    }

    // Listen to auth state changes
    AuthService.authStateChanges.listen((authState) {
      if (authState.event == AuthChangeEvent.signedIn) {
        state = state.copyWith(
          status: AuthStatus.authenticated,
          user: authState.session?.user,
          error: null,
        );
        _loadUserProfile();
      } else if (authState.event == AuthChangeEvent.signedOut) {
        state = state.copyWith(
          status: AuthStatus.unauthenticated,
          user: null,
          userProfile: null,
          error: null,
        );
      }
    });
  }

  Future<void> _checkAutoLogin() async {
    try {
      // Clear any old saved credentials to avoid auto-login errors
      await StorageService.clearAllLoginData();
      state = state.copyWith(status: AuthStatus.unauthenticated);
    } catch (e) {
      // If auto login fails, just set to unauthenticated
      state = state.copyWith(status: AuthStatus.unauthenticated);
    }
  }

  Future<void> _loadUserProfile() async {
    try {
      final profile = await AuthService.getUserProfile();
      state = state.copyWith(userProfile: profile);
    } catch (e) {
      // Profile loading error doesn't affect auth state
      // Error loading user profile: $e
    }
  }

  Future<bool> signIn({
    required String email,
    required String password,
    bool rememberLogin = false,
  }) async {
    state = state.copyWith(status: AuthStatus.loading, error: null);

    try {
      final response = await AuthService.signIn(
        email: email,
        password: password,
      );

      if (response.user != null) {
        state = state.copyWith(
          status: AuthStatus.authenticated,
          user: response.user,
          error: null,
        );
        await _loadUserProfile();

        // Save credentials if remember login is enabled
        if (rememberLogin) {
          await StorageService.setRememberLogin(true);
          await StorageService.saveLoginCredentials(
            email: email,
            password: password,
          );
        } else {
          // Clear saved credentials if remember login is disabled
          await StorageService.setRememberLogin(false);
          await StorageService.clearLoginCredentials();
        }

        return true;
      } else {
        state = state.copyWith(status: AuthStatus.error, error: 'Login failed');
        return false;
      }
    } catch (e) {
      state = state.copyWith(status: AuthStatus.error, error: e.toString());
      return false;
    }
  }

  Future<bool> signUp({
    required String email,
    required String password,
    required String fullName,
    String? phone,
  }) async {
    state = state.copyWith(status: AuthStatus.loading, error: null);

    try {
      final response = await AuthService.signUp(
        email: email,
        password: password,
        fullName: fullName,
        phone: phone,
      );

      if (response.user != null) {
        // For email confirmation flow, user might not be immediately confirmed
        if (response.session != null) {
          state = state.copyWith(
            status: AuthStatus.authenticated,
            user: response.user,
            error: null,
          );
          await _loadUserProfile();
        } else {
          state = state.copyWith(
            status: AuthStatus.unauthenticated,
            error: null,
          );
        }
        return true;
      } else {
        state = state.copyWith(
          status: AuthStatus.error,
          error: 'Registration failed',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(status: AuthStatus.error, error: e.toString());
      return false;
    }
  }

  Future<void> signOut() async {
    state = state.copyWith(status: AuthStatus.loading);

    try {
      await AuthService.signOut();

      // Clear all login data when signing out
      await StorageService.clearAllLoginData();

      state = state.copyWith(
        status: AuthStatus.unauthenticated,
        user: null,
        userProfile: null,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(status: AuthStatus.error, error: e.toString());
    }
  }

  Future<bool> resetPassword({required String email}) async {
    try {
      await AuthService.resetPassword(email: email);
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> updateProfile({String? fullName, String? phone}) async {
    try {
      await AuthService.updateProfile(fullName: fullName, phone: phone);
      await _loadUserProfile();
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Getters
  bool get isAuthenticated => state.status == AuthStatus.authenticated;
  bool get isLoading => state.status == AuthStatus.loading;
  bool get hasError => state.error != null;
  String? get error => state.error;
  User? get user => state.user;
  Map<String, dynamic>? get userProfile => state.userProfile;
  String? get userRole => state.userProfile?['role'];
  String? get userName => state.userProfile?['full_name'] ?? state.user?.email;
}

// Auth provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

// Convenience providers
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(
    authProvider.select((state) => state.status == AuthStatus.authenticated),
  );
});

final currentUserProvider = Provider<User?>((ref) {
  return ref.watch(authProvider.select((state) => state.user));
});

final userProfileProvider = Provider<Map<String, dynamic>?>((ref) {
  return ref.watch(authProvider.select((state) => state.userProfile));
});

final userRoleProvider = Provider<String?>((ref) {
  return ref.watch(authProvider.select((state) => state.userProfile?['role']));
});

final isLoadingProvider = Provider<bool>((ref) {
  return ref.watch(
    authProvider.select((state) => state.status == AuthStatus.loading),
  );
});

final userNameProvider = Provider<String?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.userProfile?['full_name'] ?? authState.user?.email;
});
