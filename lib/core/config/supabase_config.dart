import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  static const String _supabaseUrlKey = 'SUPABASE_URL';
  static const String _supabaseAnonKeyKey = 'SUPABASE_ANON_KEY';

  // Demo mode configuration
  static const bool isDemoMode =
      false; // Set to false when you have real Supabase credentials
  static const String demoUrl = 'https://demo.supabase.co';
  static const String demoKey = 'demo_key_for_testing';

  static String get supabaseUrl {
    if (isDemoMode) return demoUrl;
    return dotenv.env[_supabaseUrlKey] ?? demoUrl;
  }

  static String get supabaseAnonKey {
    if (isDemoMode) return demoKey;
    return dotenv.env[_supabaseAnonKeyKey] ?? demoKey;
  }

  static Future<void> initialize() async {
    try {
      if (!isDemoMode) {
        await dotenv.load(fileName: '.env');
      }

      // Only initialize Supabase if we have valid credentials
      if (!isDemoMode && supabaseUrl.isNotEmpty && supabaseAnonKey.isNotEmpty) {
        await Supabase.initialize(
          url: supabaseUrl,
          anonKey: supabaseAnonKey,
          debug: true,
        );
      } else {
        // Running in demo mode - Supabase not initialized
      }
    } catch (e) {
      // Supabase initialization failed: $e
      // Running in demo mode
    }
  }

  static SupabaseClient get client {
    try {
      return Supabase.instance.client;
    } catch (e) {
      throw Exception('Supabase client not initialized: $e');
    }
  }

  static GoTrueClient? get auth {
    try {
      if (isDemoMode) return null;
      return client?.auth;
    } catch (e) {
      return null;
    }
  }

  static SupabaseStorageClient? get storage {
    try {
      if (isDemoMode) return null;
      return client?.storage;
    } catch (e) {
      return null;
    }
  }

  static RealtimeClient? get realtime {
    try {
      if (isDemoMode) return null;
      return client?.realtime;
    } catch (e) {
      return null;
    }
  }
}
