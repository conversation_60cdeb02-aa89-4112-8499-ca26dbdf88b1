import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

// Import screens
import '../../features/auth/presentation/login_screen.dart';
import '../../features/auth/presentation/register_screen.dart';
import '../../features/dashboard/presentation/dashboard_screen.dart';
import '../../features/finance/presentation/add_finance_transaction_screen.dart';
import '../../features/finance/presentation/cash_flow_detail_screen.dart';
import '../../features/finance/presentation/finance_screen_simple.dart';
import '../../features/inventory/presentation/add_product_screen.dart';
import '../../features/inventory/presentation/add_stock_transaction_screen.dart';
import '../../features/inventory/presentation/categories_screen.dart';
import '../../features/inventory/presentation/inventory_screen.dart';
import '../../features/inventory/presentation/product_detail_screen.dart';
import '../../features/inventory/presentation/stock_transaction_detail_screen.dart';
import '../../features/inventory/presentation/stock_transactions_screen.dart';
import '../../features/invoices/presentation/invoice_detail_screen.dart';
import '../../features/invoices/presentation/invoices_screen.dart';
import '../../features/notifications/presentation/notifications_screen.dart';
import '../../features/partners/presentation/add_partner_screen.dart';
import '../../features/partners/presentation/partner_detail_screen.dart';
import '../../features/partners/presentation/partners_screen_simple.dart';
import '../../features/pos/presentation/pos_screen_v2.dart';
import '../../features/reports/presentation/reports_screen_simple.dart';
import '../../features/settings/presentation/settings_screen.dart';

class AppRoutes {
  // Auth routes
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';

  // Main routes
  static const String home = '/';
  static const String dashboard = '/dashboard';

  // Inventory routes
  static const String inventory = '/inventory';
  static const String products = '/products';
  static const String productDetail = '/products/:id';
  static const String addProduct = '/products/add';
  static const String editProduct = '/products/:id/edit';
  static const String categories = '/categories';
  static const String stockTransactions = '/stock-transactions';
  static const String addStockTransaction = '/stock-transactions/add';
  static const String stockTransactionDetail = '/stock-transaction/:id';

  // POS/Sales routes (gộp thành một)
  static const String pos = '/pos';
  static const String sales = '/pos'; // Alias cho POS
  static const String newSale = '/pos'; // Redirect to POS
  static const String saleDetail = '/pos/orders/:id';

  // Invoice routes
  static const String invoices = '/invoices';
  static const String invoiceDetail = '/invoice/:id';
  static const String createInvoice = '/invoices/create';

  // Finance routes
  static const String finance = '/finance';
  static const String cashFlowDetail = '/cash-flow/:id';
  static const String cashbook = '/finance/cashbook';
  static const String addTransaction = '/finance/add-transaction';

  // Partners routes
  static const String partners = '/partners';
  static const String customers = '/partners/customers';
  static const String suppliers = '/partners/suppliers';
  static const String partnerDetail = '/partners/:id';
  static const String addPartner = '/partners/add';

  // Reports routes
  static const String reports = '/reports';
  static const String salesReport = '/reports/sales';
  static const String inventoryReport = '/reports/inventory';
  static const String financeReport = '/reports/finance';

  // Notifications routes
  static const String notifications = '/notifications';

  // Settings routes
  static const String settings = '/settings';
  static const String profile = '/settings/profile';
  static const String users = '/settings/users';
}

// Temporary placeholder widget
class PlaceholderScreen extends StatelessWidget {
  final String title;

  const PlaceholderScreen({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(title, style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Text(
              'Tính năng đang được phát triển',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}

final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.login,
    routes: [
      // Auth routes
      GoRoute(
        path: AppRoutes.login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.register,
        builder: (context, state) => const RegisterScreen(),
      ),

      // Main routes
      GoRoute(
        path: AppRoutes.dashboard,
        builder: (context, state) => const DashboardScreen(),
      ),

      // Inventory routes
      GoRoute(
        path: AppRoutes.inventory,
        builder: (context, state) => const InventoryScreen(),
      ),
      GoRoute(
        path: AppRoutes.products,
        builder: (context, state) => const InventoryScreen(),
      ),
      GoRoute(
        path: AppRoutes.addProduct,
        builder: (context, state) => const AddProductScreen(),
      ),
      GoRoute(
        path: AppRoutes.productDetail,
        builder: (context, state) {
          final productId = state.pathParameters['id']!;
          return ProductDetailScreen(productId: productId);
        },
      ),
      GoRoute(
        path: AppRoutes.categories,
        builder: (context, state) => const CategoriesScreen(),
      ),
      GoRoute(
        path: AppRoutes.stockTransactions,
        builder: (context, state) => const StockTransactionsScreen(),
      ),
      GoRoute(
        path: AppRoutes.addStockTransaction,
        builder: (context, state) => const AddStockTransactionScreen(),
      ),
      GoRoute(
        path: AppRoutes.stockTransactionDetail,
        builder: (context, state) {
          final transactionId = state.pathParameters['id']!;
          return StockTransactionDetailScreen(transactionId: transactionId);
        },
      ),

      // POS/Sales routes (gộp thành một)
      GoRoute(
        path: AppRoutes.pos,
        builder: (context, state) => const POSScreenV2(),
      ),

      // Invoice routes
      GoRoute(
        path: AppRoutes.invoices,
        builder: (context, state) => const InvoicesScreen(),
      ),
      GoRoute(
        path: AppRoutes.invoiceDetail,
        builder: (context, state) {
          final invoiceId = state.pathParameters['id']!;
          return InvoiceDetailScreen(invoiceId: invoiceId);
        },
      ),

      // Finance routes
      GoRoute(
        path: AppRoutes.finance,
        builder: (context, state) => const FinanceScreenSimple(),
      ),
      GoRoute(
        path: AppRoutes.cashFlowDetail,
        builder: (context, state) {
          final cashFlowId = state.pathParameters['id']!;
          return CashFlowDetailScreen(cashFlowId: cashFlowId);
        },
      ),
      GoRoute(
        path: AppRoutes.addTransaction,
        builder: (context, state) => const AddFinanceTransactionScreen(),
      ),

      // Partners routes
      GoRoute(
        path: AppRoutes.partners,
        builder: (context, state) => const PartnersScreenSimple(),
      ),
      GoRoute(
        path: AppRoutes.addPartner,
        builder: (context, state) => const AddPartnerScreen(),
      ),
      GoRoute(
        path: AppRoutes.partnerDetail,
        builder: (context, state) {
          final partnerId = state.pathParameters['id']!;
          return PartnerDetailScreen(partnerId: partnerId);
        },
      ),

      // Notifications routes
      GoRoute(
        path: AppRoutes.notifications,
        builder: (context, state) => const NotificationsScreen(),
      ),

      // Reports routes
      GoRoute(
        path: AppRoutes.reports,
        builder: (context, state) => const ReportsScreenSimple(),
      ),

      // Settings routes
      GoRoute(
        path: AppRoutes.settings,
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
  );
});
