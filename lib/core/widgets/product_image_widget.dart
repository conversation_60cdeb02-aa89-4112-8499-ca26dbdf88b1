import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

import '../services/product_image_service.dart';

class ProductImageWidget extends StatefulWidget {
  final String productId;
  final String? initialImageUrl;
  final bool isEditable;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final Function(String? imageUrl)? onImageChanged;
  final Function(String error)? onError;

  const ProductImageWidget({
    super.key,
    required this.productId,
    this.initialImageUrl,
    this.isEditable = false,
    this.width,
    this.height,
    this.borderRadius,
    this.onImageChanged,
    this.onError,
  });

  @override
  State<ProductImageWidget> createState() => _ProductImageWidgetState();
}

class _ProductImageWidgetState extends State<ProductImageWidget> {
  String? _currentImageUrl;
  bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _currentImageUrl = widget.initialImageUrl;
    _loadProductImage();
  }

  @override
  void didUpdateWidget(ProductImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.productId != widget.productId ||
        oldWidget.initialImageUrl != widget.initialImageUrl) {
      _currentImageUrl = widget.initialImageUrl;
      _loadProductImage();
    }
  }

  Future<void> _loadProductImage() async {
    try {
      // Prioritize initialImageUrl if available (from product model)
      if (widget.initialImageUrl != null &&
          widget.initialImageUrl!.isNotEmpty) {
        if (mounted) {
          setState(() {
            _currentImageUrl = widget.initialImageUrl;
          });
        }
        return;
      }

      // Fallback to service if no initialImageUrl
      final imageUrl = await ProductImageService.getProductImageUrl(
        widget.productId,
      );
      if (mounted) {
        setState(() {
          _currentImageUrl = imageUrl;
        });
      }
    } catch (e) {
      // Silently handle errors, don't show error to user unless critical
      debugPrint('⚠️ Could not load product image: $e');
      if (mounted) {
        setState(() {
          _currentImageUrl = widget.initialImageUrl;
        });
      }
    }
  }

  Future<void> _retryLoadImage() async {
    // Only retry if we don't have initialImageUrl (avoid infinite retry)
    if (widget.initialImageUrl == null || widget.initialImageUrl!.isEmpty) {
      try {
        final imageUrl = await ProductImageService.getProductImageUrl(
          widget.productId,
        );
        if (mounted && imageUrl != null && imageUrl.isNotEmpty) {
          setState(() {
            _currentImageUrl = imageUrl;
          });
        }
      } catch (e) {
        debugPrint('⚠️ Retry load image failed: $e');
      }
    }
  }

  Future<void> _pickAndUploadImage() async {
    if (!widget.isEditable) return;

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (image == null) return;

      final imageFile = File(image.path);

      // Validate file
      if (!ProductImageService.isValidImageFile(imageFile)) {
        if (mounted && widget.onError != null) {
          widget.onError!(
            'Định dạng file không hợp lệ. Chỉ hỗ trợ JPG, PNG, GIF, WebP.',
          );
        }
        return;
      }

      // Check file size
      if (!await ProductImageService.isImageSizeValid(imageFile)) {
        if (mounted && widget.onError != null) {
          widget.onError!('Kích thước file quá lớn. Tối đa 5MB.');
        }
        return;
      }

      setState(() {
        _isLoading = true;
      });

      // Upload image
      final result = await ProductImageService.uploadProductImage(
        productId: widget.productId,
        imageFile: imageFile,
        altText: 'Product image',
      );

      if (mounted) {
        setState(() {
          _currentImageUrl = result['public_url'];
          _isLoading = false;
        });

        // Notify parent
        if (widget.onImageChanged != null) {
          widget.onImageChanged!(_currentImageUrl);
        }

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tải ảnh lên thành công!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (widget.onError != null) {
          widget.onError!('Không thể tải ảnh lên: $e');
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Lỗi tải ảnh: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteImage() async {
    if (!widget.isEditable) return;

    try {
      setState(() {
        _isLoading = true;
      });

      await ProductImageService.deleteProductImage(widget.productId);

      if (mounted) {
        setState(() {
          _currentImageUrl = null;
          _isLoading = false;
        });

        // Notify parent
        if (widget.onImageChanged != null) {
          widget.onImageChanged!(null);
        }

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã xóa ảnh sản phẩm!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (widget.onError != null) {
          widget.onError!('Không thể xóa ảnh: $e');
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Lỗi xóa ảnh: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showImageOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Chọn từ thư viện'),
              onTap: () {
                Navigator.pop(context);
                _pickAndUploadImage();
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Chụp ảnh'),
              onTap: () {
                Navigator.pop(context);
                _takePhoto();
              },
            ),
            if (_currentImageUrl != null)
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text(
                  'Xóa ảnh',
                  style: TextStyle(color: Colors.red),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _confirmDeleteImage();
                },
              ),
            ListTile(
              leading: const Icon(Icons.cancel),
              title: const Text('Hủy'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _takePhoto() async {
    if (!widget.isEditable) return;

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (image == null) return;

      final imageFile = File(image.path);

      setState(() {
        _isLoading = true;
      });

      // Upload image
      final result = await ProductImageService.uploadProductImage(
        productId: widget.productId,
        imageFile: imageFile,
        altText: 'Product image',
      );

      if (mounted) {
        setState(() {
          _currentImageUrl = result['public_url'];
          _isLoading = false;
        });

        // Notify parent
        if (widget.onImageChanged != null) {
          widget.onImageChanged!(_currentImageUrl);
        }

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chụp ảnh thành công!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (widget.onError != null) {
          widget.onError!('Không thể chụp ảnh: $e');
        }
      }
    }
  }

  void _confirmDeleteImage() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: const Text('Bạn có chắc chắn muốn xóa ảnh này không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteImage();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width ?? double.infinity,
      height: widget.height ?? 200,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Stack(
        children: [
          // Image or placeholder
          Positioned.fill(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _currentImageUrl != null && _currentImageUrl!.isNotEmpty
                ? ClipRRect(
                    borderRadius:
                        widget.borderRadius ?? BorderRadius.circular(8),
                    child: Image.network(
                      _currentImageUrl!,
                      fit: BoxFit.cover,
                      // Add cache headers for better performance
                      headers: const {'Cache-Control': 'max-age=3600'},
                      errorBuilder: (context, error, stackTrace) {
                        debugPrint(
                          '🖼️ Image load error for ${widget.productId}: $error',
                        );
                        // Try to reload image from service on error
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _retryLoadImage();
                        });
                        return !widget.isEditable
                            ? _buildPlaceholder()
                            : const SizedBox();
                      },
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Center(
                          child: CircularProgressIndicator(
                            value: loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                : null,
                          ),
                        );
                      },
                    ),
                  )
                : (!widget.isEditable ? _buildPlaceholder() : const SizedBox()),
          ),

          // Edit button overlay
          if (widget.isEditable && !_isLoading)
            // Tap to add image when no image exists
            if (_currentImageUrl == null && widget.isEditable && !_isLoading)
              Positioned.fill(
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _showImageOptions,
                    borderRadius:
                        widget.borderRadius ?? BorderRadius.circular(8),
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_photo_alternate,
                            size: 48,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Thêm ảnh sản phẩm',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildPlaceholder() {
    return const Center(child: Icon(Icons.image, size: 64, color: Colors.grey));
  }
}
