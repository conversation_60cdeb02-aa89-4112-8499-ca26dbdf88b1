import '../models/category.dart';

class CategoryService {
  static final List<Category> _categories = [
    Category(
      id: '1',
      name: '<PERSON><PERSON> uống',
      description: '<PERSON><PERSON><PERSON> lo<PERSON><PERSON> đồ uống',
      color: '#2196F3',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    Category(
      id: '2',
      name: '<PERSON><PERSON><PERSON><PERSON> ăn',
      description: '<PERSON><PERSON>c món ăn',
      color: '#4CAF50',
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now().subtract(const Duration(days: 25)),
    ),
    Category(
      id: '3',
      name: '<PERSON><PERSON><PERSON><PERSON> tử',
      description: 'Thiết bị điện tử',
      color: '#FF9800',
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now().subtract(const Duration(days: 20)),
    ),
  ];

  static Future<List<Category>> getCategories() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return List.from(_categories);
  }

  static Future<Category?> getCategoryById(String id) async {
    await Future.delayed(const Duration(milliseconds: 200));
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  static Future<Category?> createCategory(Category category) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    final newCategory = category.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    _categories.add(newCategory);
    return newCategory;
  }

  static Future<bool> updateCategory(String id, Category category) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    final index = _categories.indexWhere((c) => c.id == id);
    if (index != -1) {
      _categories[index] = category.copyWith(
        id: id,
        updatedAt: DateTime.now(),
      );
      return true;
    }
    return false;
  }

  static Future<bool> deleteCategory(String id) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    final index = _categories.indexWhere((c) => c.id == id);
    if (index != -1) {
      _categories.removeAt(index);
      return true;
    }
    return false;
  }

  static Future<List<Category>> searchCategories(String query) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    if (query.isEmpty) {
      return List.from(_categories);
    }
    
    return _categories.where((category) {
      return category.name.toLowerCase().contains(query.toLowerCase()) ||
          (category.description?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }
}
