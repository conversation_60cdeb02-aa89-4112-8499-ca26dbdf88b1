import 'category.dart';
import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';

class Product {
  final String? id;
  final String name;
  final String? description;
  final String? sku;
  final String? barcode;
  final String? categoryId;
  final Category? category;
  final double price;
  final double cost;
  final int stockQuantity;
  final int minStockLevel;
  final int? maxStockLevel;
  final String unit;
  final String? imageUrl;
  final bool isActive;
  final String? createdBy;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Product({
    this.id,
    required this.name,
    this.description,
    this.sku,
    this.barcode,
    this.categoryId,
    this.category,
    this.price = 0.0,
    this.cost = 0.0,
    this.stockQuantity = 0,
    this.minStockLevel = 0,
    this.maxStockLevel,
    this.unit = 'pcs',
    this.imageUrl,
    this.isActive = true,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as String?,
      name: json['name'] as String,
      description: json['description'] as String?,
      sku: json['sku'] as String?,
      barcode: json['barcode'] as String?,
      categoryId: json['category_id'] as String?,
      category: json['category'] != null
          ? Category.fromJson(json['category'])
          : null,
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      cost: (json['cost'] as num?)?.toDouble() ?? 0.0,
      stockQuantity: json['stock_quantity'] as int? ?? 0,
      minStockLevel: json['min_stock_level'] as int? ?? 0,
      maxStockLevel: json['max_stock_level'] as int?,
      unit: json['unit'] as String? ?? 'pcs',
      imageUrl: json['image_url'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdBy: json['created_by'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'name': name,
      if (description != null) 'description': description,
      if (sku != null) 'sku': sku,
      if (barcode != null) 'barcode': barcode,
      if (categoryId != null) 'category_id': categoryId,
      'price': price,
      'cost': cost,
      'stock_quantity': stockQuantity,
      'min_stock_level': minStockLevel,
      if (maxStockLevel != null) 'max_stock_level': maxStockLevel,
      'unit': unit,
      if (imageUrl != null) 'image_url': imageUrl,
      'is_active': isActive,
      if (createdBy != null) 'created_by': createdBy,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  Product copyWith({
    String? id,
    String? name,
    String? description,
    String? sku,
    String? barcode,
    String? categoryId,
    Category? category,
    double? price,
    double? cost,
    int? stockQuantity,
    int? minStockLevel,
    int? maxStockLevel,
    String? unit,
    String? imageUrl,
    bool? isActive,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      sku: sku ?? this.sku,
      barcode: barcode ?? this.barcode,
      categoryId: categoryId ?? this.categoryId,
      category: category ?? this.category,
      price: price ?? this.price,
      cost: cost ?? this.cost,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      minStockLevel: minStockLevel ?? this.minStockLevel,
      maxStockLevel: maxStockLevel ?? this.maxStockLevel,
      unit: unit ?? this.unit,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Product(id: $id, name: $name, sku: $sku, price: $price, stock: $stockQuantity)';
  }

  String get categoryCode => category?.name ?? 'no_category';
  String get formattedPrice => '${price.toStringAsFixed(0)}đ';
  String get formattedCost => '${cost.toStringAsFixed(0)}đ';
  String get formattedProfitMargin => '${profitMargin.toStringAsFixed(1)}%';
  String get formattedTotalValue => '${totalValue.toStringAsFixed(0)}đ';
  String get stockStatusCode {
    if (isOutOfStock) return 'out_of_stock';
    if (isLowStock) return 'low_stock';
    return 'in_stock';
  }
}

// Extension for Product
extension ProductExtension on Product {
  // Get display name
  String get displayName => name;

  // Get category display text
  String get categoryText => category?.name ?? '';

  // Get formatted price
  String get formattedPrice => '${price.toStringAsFixed(0)}đ';

  // Get formatted cost
  String get formattedCost => '${cost.toStringAsFixed(0)}đ';

  // Get profit margin
  double get profitMargin {
    if (cost == 0) return 0;
    return ((price - cost) / cost) * 100;
  }

  // Get formatted profit margin
  String get formattedProfitMargin => '${profitMargin.toStringAsFixed(1)}%';

  // Get total value
  double get totalValue => stockQuantity * cost;

  // Get formatted total value
  String get formattedTotalValue => '${totalValue.toStringAsFixed(0)}đ';

  // Check if product is out of stock
  bool get isOutOfStock => stockQuantity <= 0;

  // Check if product is low in stock
  bool get isLowStock => stockQuantity > 0 && stockQuantity <= 10;

  // Get stock status code
  String get stockStatusCode {
    if (isOutOfStock) return 'out_of_stock';
    if (isLowStock) return 'low_stock';
    return 'in_stock';
  }

  // Get stock status color
  String get stockStatusColor {
    if (isOutOfStock) return '#EF4444'; // Red
    if (isLowStock) return '#F59E0B'; // Orange
    return '#10B981'; // Green
  }

  // Check if product is valid
  bool get isValid => name.isNotEmpty && price >= 0 && cost >= 0;

  // Create copy for editing
  Product copyForEdit() {
    return copyWith(
      id: null,
      createdBy: null,
      createdAt: null,
      updatedAt: null,
    );
  }

  // Create copy with new stock quantity
  Product copyWithStock(int newQuantity) {
    return copyWith(stockQuantity: newQuantity);
  }

  // Create copy with new price
  Product copyWithPrice(double newPrice) {
    return copyWith(price: newPrice);
  }

  // Check if SKU is unique (for validation)
  bool hasValidSku() {
    return sku != null && sku!.isNotEmpty;
  }

  // Check if barcode is valid
  bool hasValidBarcode() {
    return barcode != null && barcode!.isNotEmpty;
  }

  // Get search keywords for filtering
  List<String> get searchKeywords {
    final keywords = <String>[];
    keywords.add(name.toLowerCase());
    if (sku != null) keywords.add(sku!.toLowerCase());
    if (barcode != null) keywords.add(barcode!.toLowerCase());
    if (description != null) keywords.add(description!.toLowerCase());
    if (category?.name != null) keywords.add(category!.name.toLowerCase());
    return keywords;
  }

  // Check if product matches search query
  bool matchesSearch(String query) {
    if (query.isEmpty) return true;
    final lowerQuery = query.toLowerCase();
    return searchKeywords.any((keyword) => keyword.contains(lowerQuery));
  }
}
