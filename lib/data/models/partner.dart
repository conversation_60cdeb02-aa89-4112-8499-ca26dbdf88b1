class Partner {
  final String? id;
  final String name;
  final String type; // customer, supplier, both
  final String? email;
  final String? phone;
  final String? address;
  final String? taxNumber;
  final double creditLimit;
  final double currentBalance;
  final bool isActive;
  final String? notes;
  final String? createdBy;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Partner({
    this.id,
    required this.name,
    this.type = 'customer',
    this.email,
    this.phone,
    this.address,
    this.taxNumber,
    this.creditLimit = 0.0,
    this.currentBalance = 0.0,
    this.isActive = true,
    this.notes,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
  });

  factory Partner.fromJson(Map<String, dynamic> json) {
    return Partner(
      id: json['id'] as String?,
      name: json['name'] as String,
      type: json['type'] as String? ?? 'customer',
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      taxNumber: json['tax_number'] as String?,
      creditLimit: (json['credit_limit'] as num?)?.toDouble() ?? 0.0,
      currentBalance: (json['current_balance'] as num?)?.toDouble() ?? 0.0,
      isActive: json['is_active'] as bool? ?? true,
      notes: json['notes'] as String?,
      createdBy: json['created_by'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'type': type,
      if (email != null) 'email': email,
      if (phone != null) 'phone': phone,
      if (address != null) 'address': address,
      if (taxNumber != null) 'tax_number': taxNumber,
      'credit_limit': creditLimit,
      'current_balance': currentBalance,
      'is_active': isActive,
      if (notes != null) 'notes': notes,
      if (createdBy != null) 'created_by': createdBy,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  Partner copyWith({
    String? id,
    String? name,
    String? type,
    String? email,
    String? phone,
    String? address,
    String? taxNumber,
    double? creditLimit,
    double? currentBalance,
    bool? isActive,
    String? notes,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Partner(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      taxNumber: taxNumber ?? this.taxNumber,
      creditLimit: creditLimit ?? this.creditLimit,
      currentBalance: currentBalance ?? this.currentBalance,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Partner && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Partner(id: $id, name: $name, type: $type, balance: $currentBalance)';
  }
}

// Extension for Partner
extension PartnerExtension on Partner {
  // Get display name
  String get displayName => name;

  // Get type display text
  String get typeDisplayText {
    switch (type) {
      case 'customer':
        return 'Khách hàng';
      case 'supplier':
        return 'Nhà cung cấp';
      case 'both':
        return 'Khách hàng & NCC';
      default:
        return type;
    }
  }

  // Get formatted credit limit
  String get formattedCreditLimit => '${creditLimit.toStringAsFixed(0)}đ';

  // Get formatted current balance
  String get formattedCurrentBalance => '${currentBalance.toStringAsFixed(0)}đ';

  // Check if partner is customer
  bool get isCustomer => type == 'customer' || type == 'both';

  // Check if partner is supplier
  bool get isSupplier => type == 'supplier' || type == 'both';

  // Check if partner has credit limit
  bool get hasCreditLimit => creditLimit > 0;

  // Check if partner is over credit limit
  bool get isOverCreditLimit => hasCreditLimit && currentBalance > creditLimit;

  // Get remaining credit
  double get remainingCredit {
    if (!hasCreditLimit) return double.infinity;
    return creditLimit - currentBalance;
  }

  // Get formatted remaining credit
  String get formattedRemainingCredit {
    if (!hasCreditLimit) return 'Không giới hạn';
    return '${remainingCredit.toStringAsFixed(0)}đ';
  }

  // Get credit status color
  String get creditStatusColor {
    if (!hasCreditLimit) return '#10B981'; // Green
    if (isOverCreditLimit) return '#EF4444'; // Red
    if (remainingCredit < creditLimit * 0.2) return '#F59E0B'; // Orange
    return '#10B981'; // Green
  }

  // Check if partner is valid
  bool get isValid => name.isNotEmpty;

  // Get contact info
  String get contactInfo {
    final parts = <String>[];
    if (phone != null && phone!.isNotEmpty) parts.add(phone!);
    if (email != null && email!.isNotEmpty) parts.add(email!);
    return parts.join(' • ');
  }

  // Get full address
  String get fullAddress => address ?? '';

  // Check if has contact info
  bool get hasContactInfo =>
      (phone != null && phone!.isNotEmpty) ||
      (email != null && email!.isNotEmpty);

  // Create copy for editing
  Partner copyForEdit() {
    return copyWith(
      id: null,
      createdBy: null,
      createdAt: null,
      updatedAt: null,
    );
  }

  // Create copy with new balance
  Partner copyWithBalance(double newBalance) {
    return copyWith(currentBalance: newBalance);
  }

  // Add to balance
  Partner addToBalance(double amount) {
    return copyWith(currentBalance: currentBalance + amount);
  }

  // Subtract from balance
  Partner subtractFromBalance(double amount) {
    return copyWith(currentBalance: currentBalance - amount);
  }

  // Check if can make purchase with amount
  bool canMakePurchase(double amount) {
    if (!hasCreditLimit) return true;
    return (currentBalance + amount) <= creditLimit;
  }
}
