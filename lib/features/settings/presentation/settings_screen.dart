import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/providers/invoice_settings_provider.dart';
import '../../../core/providers/language_provider.dart';
import '../../../core/routers/app_router.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/responsive.dart';
import '../../../generated/l10n/app_localizations.dart';
import 'printer_settings_screen.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final languageNotifier = ref.read(languageProvider.notifier);
    final currentLocale = ref.watch(languageProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.settings),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
      ),
      body: SingleChildScrollView(
        padding: Responsive.responsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Language Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.language,
                          size: Responsive.responsiveIconSize(context),
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        SizedBox(width: Responsive.responsiveSpacing(context)),
                        Text(
                          l10n.language,
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(
                                fontSize: Responsive.responsiveFontSize(
                                  context,
                                  mobile: 18,
                                  desktop: 20,
                                ),
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    Text(
                      l10n.selectYourPreferredLanguage,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingL),

                    // Language Options
                    _buildLanguageOption(
                      context,
                      ref,
                      l10n.vietnamese,
                      'vi',
                      Icons.flag_outlined,
                      currentLocale.languageCode == 'vi',
                      languageNotifier,
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildLanguageOption(
                      context,
                      ref,
                      l10n.english,
                      'en',
                      Icons.flag_outlined,
                      currentLocale.languageCode == 'en',
                      languageNotifier,
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildLanguageOption(
                      context,
                      ref,
                      '日本語',
                      'ja',
                      Icons.flag_outlined,
                      currentLocale.languageCode == 'ja',
                      languageNotifier,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),

            // App Info Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: Responsive.responsiveIconSize(context),
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        SizedBox(width: Responsive.responsiveSpacing(context)),
                        Text(
                          l10n.appInformation,
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(
                                fontSize: Responsive.responsiveFontSize(
                                  context,
                                  mobile: 18,
                                  desktop: 20,
                                ),
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppTheme.spacingL),

                    _buildInfoRow(context, l10n.appName, AppConstants.appName),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildInfoRow(
                      context,
                      l10n.version,
                      AppConstants.appVersion,
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    _buildInfoRow(
                      context,
                      l10n.description,
                      l10n.pointOfSaleManagementSystem,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),

            // Other Settings Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.settings,
                          size: Responsive.responsiveIconSize(context),
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        SizedBox(width: Responsive.responsiveSpacing(context)),
                        Text(
                          l10n.otherSettings,
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(
                                fontSize: Responsive.responsiveFontSize(
                                  context,
                                  mobile: 18,
                                  desktop: 20,
                                ),
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppTheme.spacingL),

                    // Invoice Settings Section
                    _buildInvoiceSettings(context, ref, l10n),
                    const SizedBox(height: AppTheme.spacingL),

                    // Printer Settings
                    ListTile(
                      leading: const Icon(Icons.print),
                      title: const Text('Thiết lập máy in'),
                      subtitle: const Text('Quản lý máy in để in hóa đơn'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const PrinterSettingsScreen(),
                          ),
                        );
                      },
                    ),

                    ListTile(
                      leading: const Icon(Icons.person),
                      title: Text(l10n.profile),
                      subtitle: Text(l10n.manageYourProfile),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(l10n.profileSettingsComingSoon),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.people),
                      title: Text(l10n.users),
                      subtitle: Text(l10n.manageUsers),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(l10n.userManagementComingSoon),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.logout),
                      title: Text(l10n.logout),
                      subtitle: Text(l10n.signOutOfYourAccount),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        _showLogoutDialog(context);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    WidgetRef ref,
    String title,
    String languageCode,
    IconData icon,
    bool isSelected,
    LanguageNotifier languageNotifier,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).dividerColor,
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        color: isSelected
            ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
            : null,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).iconTheme.color,
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? Theme.of(context).colorScheme.primary : null,
          ),
        ),
        trailing: isSelected
            ? Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.primary,
              )
            : null,
        onTap: () async {
          await languageNotifier.changeLanguage(languageCode);
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  languageCode == 'vi'
                      ? 'Đã chuyển sang tiếng Việt'
                      : languageCode == 'en'
                      ? 'Language changed to English'
                      : '言語を日本語に変更しました',
                ),
                backgroundColor: AppTheme.successColor,
              ),
            );
          }
        },
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            textAlign: TextAlign.end,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  void _showLogoutDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(l10n.logout),
          content: Text(l10n.areYouSureLogout),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.cancel),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.go(AppRoutes.login);
              },
              child: Text(l10n.logout),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInvoiceSettings(
    BuildContext context,
    WidgetRef ref,
    AppLocalizations l10n,
  ) {
    final invoiceSettings = ref.watch(invoiceSettingsProvider);
    final invoiceSettingsNotifier = ref.read(invoiceSettingsProvider.notifier);

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(AppTheme.spacingM),
            child: Row(
              children: [
                const Icon(Icons.receipt_long, color: AppTheme.primaryColor),
                const SizedBox(width: AppTheme.spacingS),
                Text(
                  'Cài đặt hóa đơn',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          SwitchListTile(
            title: const Text('Hiển thị hóa đơn sau thanh toán'),
            subtitle: const Text(
              'Tự động mở màn hình hóa đơn sau khi thanh toán thành công',
            ),
            value: invoiceSettings.showInvoiceAfterPayment,
            onChanged: (value) async {
              await invoiceSettingsNotifier.setShowInvoiceAfterPayment(value);
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      value
                          ? 'Đã bật hiển thị hóa đơn sau thanh toán'
                          : 'Đã tắt hiển thị hóa đơn sau thanh toán',
                    ),
                    backgroundColor: AppTheme.successColor,
                  ),
                );
              }
            },
            secondary: const Icon(Icons.receipt),
          ),
        ],
      ),
    );
  }
}
