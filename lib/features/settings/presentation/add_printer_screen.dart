import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/printer_provider.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/responsive.dart';
import '../../../data/models/printer.dart';

class AddPrinterScreen extends ConsumerStatefulWidget {
  final Printer? printer; // For editing existing printer

  const AddPrinterScreen({super.key, this.printer});

  @override
  ConsumerState<AddPrinterScreen> createState() => _AddPrinterScreenState();
}

class _AddPrinterScreenState extends ConsumerState<AddPrinterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ipController = TextEditingController();
  final _portController = TextEditingController();

  bool _isDefault = false;
  bool _isActive = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.printer != null) {
      _populateFields();
    } else {
      _portController.text = '9100'; // Default port for ESC/POS printers
    }
  }

  void _populateFields() {
    final printer = widget.printer!;
    _nameController.text = printer.name;
    _ipController.text = printer.ipAddress;
    _portController.text = printer.port.toString();
    _isDefault = printer.isDefault;
    _isActive = printer.isActive;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ipController.dispose();
    _portController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.printer != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Chỉnh sửa máy in' : 'Thêm máy in'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: Responsive.responsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header info
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppTheme.spacingL),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          isEditing ? Icons.edit : Icons.add,
                          color: AppTheme.primaryColor,
                          size: Responsive.responsiveIconSize(context),
                        ),
                        const SizedBox(width: AppTheme.spacingS),
                        Text(
                          isEditing ? 'Chỉnh sửa thông tin máy in' : 'Thêm máy in mới',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppTheme.spacingS),
                    Text(
                      'Nhập thông tin kết nối máy in qua mạng LAN. Đảm bảo máy in và thiết bị này cùng mạng.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppTheme.spacingXL),

              // Form fields
              _buildFormFields(),
              const SizedBox(height: AppTheme.spacingXL),

              // Action buttons
              _buildActionButtons(context, isEditing),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin máy in',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),

            // Printer name
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Tên máy in *',
                hintText: 'Ví dụ: Máy in quầy 1',
                prefixIcon: Icon(Icons.print),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Vui lòng nhập tên máy in';
                }
                
                final printerNotifier = ref.read(printerListProvider.notifier);
                if (printerNotifier.isPrinterNameExists(value.trim(), excludeId: widget.printer?.id)) {
                  return 'Tên máy in đã tồn tại';
                }
                
                return null;
              },
            ),
            const SizedBox(height: AppTheme.spacingL),

            // IP Address
            TextFormField(
              controller: _ipController,
              decoration: const InputDecoration(
                labelText: 'Địa chỉ IP *',
                hintText: 'Ví dụ: *************',
                prefixIcon: Icon(Icons.wifi),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Vui lòng nhập địa chỉ IP';
                }
                
                if (!_isValidIpAddress(value.trim())) {
                  return 'Địa chỉ IP không hợp lệ';
                }
                
                return null;
              },
            ),
            const SizedBox(height: AppTheme.spacingL),

            // Port
            TextFormField(
              controller: _portController,
              decoration: const InputDecoration(
                labelText: 'Cổng (Port) *',
                hintText: '9100',
                prefixIcon: Icon(Icons.settings_ethernet),
                border: OutlineInputBorder(),
                helperText: 'Cổng mặc định cho máy in ESC/POS là 9100',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Vui lòng nhập cổng';
                }
                
                final port = int.tryParse(value.trim());
                if (port == null || port <= 0 || port > 65535) {
                  return 'Cổng phải từ 1 đến 65535';
                }
                
                final printerNotifier = ref.read(printerListProvider.notifier);
                if (printerNotifier.isPrinterIpExists(
                  _ipController.text.trim(),
                  port,
                  excludeId: widget.printer?.id,
                )) {
                  return 'Máy in với IP và cổng này đã tồn tại';
                }
                
                return null;
              },
            ),
            const SizedBox(height: AppTheme.spacingL),

            // Settings
            const Divider(),
            const SizedBox(height: AppTheme.spacingM),
            
            SwitchListTile(
              title: const Text('Đặt làm máy in mặc định'),
              subtitle: const Text('Máy in này sẽ được sử dụng tự động khi in hóa đơn'),
              value: _isDefault,
              onChanged: (value) {
                setState(() {
                  _isDefault = value;
                });
              },
              secondary: const Icon(Icons.star),
            ),
            
            SwitchListTile(
              title: const Text('Kích hoạt máy in'),
              subtitle: const Text('Cho phép sử dụng máy in này'),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
              secondary: const Icon(Icons.power_settings_new),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, bool isEditing) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => context.pop(),
            child: const Text('Hủy'),
          ),
        ),
        const SizedBox(width: AppTheme.spacingM),
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : _testConnection,
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Kiểm tra'),
          ),
        ),
        const SizedBox(width: AppTheme.spacingM),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _savePrinter,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(isEditing ? 'Cập nhật' : 'Thêm máy in'),
          ),
        ),
      ],
    );
  }

  bool _isValidIpAddress(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return false;
    
    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }
    
    return true;
  }

  Future<void> _testConnection() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final testPrinter = Printer(
        id: 'test',
        name: _nameController.text.trim(),
        ipAddress: _ipController.text.trim(),
        port: int.parse(_portController.text.trim()),
        createdAt: DateTime.now(),
      );

      final printerNotifier = ref.read(printerListProvider.notifier);
      final success = await printerNotifier.testPrinterConnection(testPrinter);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Kết nối thành công!'
                  : 'Không thể kết nối. Kiểm tra lại IP và cổng.',
            ),
            backgroundColor: success ? AppTheme.successColor : AppTheme.errorColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi kiểm tra kết nối: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _savePrinter() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final printerNotifier = ref.read(printerListProvider.notifier);
      final isEditing = widget.printer != null;

      final printer = Printer(
        id: isEditing ? widget.printer!.id : DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        ipAddress: _ipController.text.trim(),
        port: int.parse(_portController.text.trim()),
        isDefault: _isDefault,
        isActive: _isActive,
        createdAt: isEditing ? widget.printer!.createdAt : DateTime.now(),
        lastUsedAt: widget.printer?.lastUsedAt,
      );

      final success = isEditing
          ? await printerNotifier.updatePrinter(printer)
          : await printerNotifier.addPrinter(printer);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isEditing
                    ? 'Đã cập nhật máy in thành công!'
                    : 'Đã thêm máy in thành công!',
              ),
              backgroundColor: AppTheme.successColor,
            ),
          );
          context.pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Không thể lưu máy in. Vui lòng thử lại.'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi lưu máy in: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
