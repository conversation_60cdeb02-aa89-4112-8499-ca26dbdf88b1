import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/printer_provider.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/responsive.dart';
import '../../../data/models/printer.dart';
import '../../../generated/l10n/app_localizations.dart';
import 'add_printer_screen.dart';

class PrinterSettingsScreen extends ConsumerWidget {
  const PrinterSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final printers = ref.watch(printerListProvider);
    final printerNotifier = ref.read(printerListProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Thiết lập máy in'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => printerNotifier.loadPrinters(),
            tooltip: 'Làm mới',
          ),
        ],
      ),
      body: Column(
        children: [
          // Header info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppTheme.spacingL),
            margin: const EdgeInsets.all(AppTheme.spacingM),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
              border: Border.all(
                color: AppTheme.primaryColor.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.print,
                      color: AppTheme.primaryColor,
                      size: Responsive.responsiveIconSize(context),
                    ),
                    const SizedBox(width: AppTheme.spacingS),
                    Text(
                      'Quản lý máy in',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacingS),
                Text(
                  'Thiết lập và quản lý các máy in để in hóa đơn. Máy in mặc định sẽ được sử dụng tự động khi in.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),

          // Printer list
          Expanded(
            child: printers.isEmpty
                ? _buildEmptyState(context)
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingM,
                    ),
                    itemCount: printers.length,
                    itemBuilder: (context, index) {
                      final printer = printers[index];
                      return _buildPrinterCard(context, ref, printer);
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddPrinterScreen(context),
        icon: const Icon(Icons.add),
        label: const Text('Thêm máy in'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.print_disabled, size: 80, color: Colors.grey[400]),
          const SizedBox(height: AppTheme.spacingL),
          Text(
            'Chưa có máy in nào',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            'Thêm máy in đầu tiên để bắt đầu in hóa đơn',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingXL),
          ElevatedButton.icon(
            onPressed: () => _showAddPrinterScreen(context),
            icon: const Icon(Icons.add),
            label: const Text('Thêm máy in'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingXL,
                vertical: AppTheme.spacingM,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrinterCard(
    BuildContext context,
    WidgetRef ref,
    Printer printer,
  ) {
    final printerNotifier = ref.read(printerListProvider.notifier);

    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              children: [
                // Printer icon and status
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingS),
                  decoration: BoxDecoration(
                    color: printer.isActive
                        ? AppTheme.successColor.withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusS),
                  ),
                  child: Icon(
                    printer.isActive ? Icons.print : Icons.print_disabled,
                    color: printer.isActive
                        ? AppTheme.successColor
                        : Colors.grey,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingM),

                // Printer info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              printer.name,
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                          ),
                          if (printer.isDefault)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppTheme.spacingS,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.primaryColor,
                                borderRadius: BorderRadius.circular(
                                  AppTheme.radiusS,
                                ),
                              ),
                              child: const Text(
                                'Mặc định',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 2),
                      Text(
                        printer.displayAddress,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      Text(
                        printer.statusText,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: printer.isActive
                              ? AppTheme.successColor
                              : Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                // Actions menu
                PopupMenuButton<String>(
                  onSelected: (value) =>
                      _handlePrinterAction(context, ref, printer, value),
                  itemBuilder: (context) => [
                    if (!printer.isDefault)
                      const PopupMenuItem(
                        value: 'set_default',
                        child: Row(
                          children: [
                            Icon(Icons.star, size: 18),
                            SizedBox(width: 8),
                            Text('Đặt làm mặc định'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'test',
                      child: Row(
                        children: [
                          Icon(Icons.wifi_find, size: 18),
                          SizedBox(width: 8),
                          Text('Kiểm tra kết nối'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 18),
                          SizedBox(width: 8),
                          Text('Chỉnh sửa'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'toggle_status',
                      child: Row(
                        children: [
                          Icon(
                            printer.isActive ? Icons.pause : Icons.play_arrow,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(printer.isActive ? 'Tạm dừng' : 'Kích hoạt'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 18, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Xóa', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showAddPrinterScreen(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AddPrinterScreen()));
  }

  void _handlePrinterAction(
    BuildContext context,
    WidgetRef ref,
    Printer printer,
    String action,
  ) async {
    final printerNotifier = ref.read(printerListProvider.notifier);

    switch (action) {
      case 'set_default':
        await _setDefaultPrinter(context, printerNotifier, printer);
        break;
      case 'test':
        await _testPrinterConnection(context, printerNotifier, printer);
        break;
      case 'edit':
        _editPrinter(context, printer);
        break;
      case 'toggle_status':
        await _togglePrinterStatus(context, printerNotifier, printer);
        break;
      case 'delete':
        await _deletePrinter(context, printerNotifier, printer);
        break;
    }
  }

  Future<void> _setDefaultPrinter(
    BuildContext context,
    PrinterListNotifier notifier,
    Printer printer,
  ) async {
    final success = await notifier.setDefaultPrinter(printer.id);
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'Đã đặt ${printer.name} làm máy in mặc định'
                : 'Không thể đặt máy in mặc định',
          ),
          backgroundColor: success
              ? AppTheme.successColor
              : AppTheme.errorColor,
        ),
      );
    }
  }

  Future<void> _testPrinterConnection(
    BuildContext context,
    PrinterListNotifier notifier,
    Printer printer,
  ) async {
    // Show loading
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    final success = await notifier.testPrinterConnection(printer);

    if (context.mounted) {
      Navigator.of(context).pop(); // Close loading

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'Kết nối thành công với ${printer.name}'
                : 'Không thể kết nối với ${printer.name}',
          ),
          backgroundColor: success
              ? AppTheme.successColor
              : AppTheme.errorColor,
        ),
      );
    }
  }

  void _editPrinter(BuildContext context, Printer printer) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddPrinterScreen(printer: printer),
      ),
    );
  }

  Future<void> _togglePrinterStatus(
    BuildContext context,
    PrinterListNotifier notifier,
    Printer printer,
  ) async {
    final success = await notifier.togglePrinterStatus(printer.id);
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? '${printer.name} đã được ${printer.isActive ? 'tạm dừng' : 'kích hoạt'}'
                : 'Không thể thay đổi trạng thái máy in',
          ),
          backgroundColor: success
              ? AppTheme.successColor
              : AppTheme.errorColor,
        ),
      );
    }
  }

  Future<void> _deletePrinter(
    BuildContext context,
    PrinterListNotifier notifier,
    Printer printer,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text('Bạn có chắc chắn muốn xóa máy in "${printer.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Xóa', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await notifier.deletePrinter(printer.id);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Đã xóa máy in ${printer.name}'
                  : 'Không thể xóa máy in',
            ),
            backgroundColor: success
                ? AppTheme.successColor
                : AppTheme.errorColor,
          ),
        );
      }
    }
  }
}
