import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/providers/invoice_settings_provider.dart';
import '../../../core/routers/app_router.dart';
import '../../../core/services/product_service.dart';
import '../../../core/services/sales_service.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/product_image_widget.dart';
import '../../../data/models/order.dart';
import '../../../data/models/order_item.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../data/services/pos_persistence_service.dart';
import 'payment_screen.dart';
import 'widgets/barcode_scanner_screen.dart';

// Simple Tab Data for Multi-Tab POS
class POSTabData {
  final String id;
  final String name;
  final List<CartItem> cartItems;
  final String? customerName;
  final String? customerPhone;
  final double discount;
  final String paymentMethod;

  const POSTabData({
    required this.id,
    required this.name,
    this.cartItems = const [],
    this.customerName,
    this.customerPhone,
    this.discount = 0.0,
    this.paymentMethod = 'cash',
  });

  double get subtotal =>
      cartItems.fold(0.0, (sum, item) => sum + item.totalPrice);
  double get tax => subtotal * 0.1;
  double get total => subtotal + tax - discount;
  int get totalItems => cartItems.fold(0, (sum, item) => sum + item.quantity);

  POSTabData copyWith({
    String? id,
    String? name,
    List<CartItem>? cartItems,
    String? customerName,
    String? customerPhone,
    double? discount,
    String? paymentMethod,
  }) {
    return POSTabData(
      id: id ?? this.id,
      name: name ?? this.name,
      cartItems: cartItems ?? this.cartItems,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      discount: discount ?? this.discount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
    );
  }
}

// Simple Product Model for POS
class SimpleProduct {
  final String id;
  final String name;
  final String? code;
  final String? sku;
  final double salePrice;
  final int stockQuantity;
  final String? imageUrl;

  const SimpleProduct({
    required this.id,
    required this.name,
    this.code,
    this.sku,
    required this.salePrice,
    this.stockQuantity = 0,
    this.imageUrl,
  });

  String get displayCode => code ?? sku ?? id;
}

// Cart Item Model
class CartItem {
  final SimpleProduct product;
  int quantity;
  double unitPrice;

  CartItem({required this.product, this.quantity = 1, double? unitPrice})
    : unitPrice = unitPrice ?? product.salePrice;

  double get totalPrice => quantity * unitPrice;

  CartItem copyWith({
    SimpleProduct? product,
    int? quantity,
    double? unitPrice,
  }) {
    return CartItem(
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
    );
  }
}

// POS State
class POSState {
  final bool isLoading;
  final String? error;
  final List<SimpleProduct> products;
  final List<CartItem> cartItems;
  final String? customerName;
  final String? customerPhone;
  final double discount;
  final String paymentMethod;
  final String searchQuery;
  final List<POSTabData> tabs;
  final int activeTabIndex;

  const POSState({
    this.isLoading = false,
    this.error,
    this.products = const [],
    this.cartItems = const [],
    this.customerName,
    this.customerPhone,
    this.discount = 0.0,
    this.paymentMethod = 'cash',
    this.searchQuery = '',
    this.tabs = const [],
    this.activeTabIndex = 0,
  });

  double get subtotal =>
      cartItems.fold(0.0, (sum, item) => sum + item.totalPrice);
  double get tax => subtotal * 0.1; // 10% VAT
  double get total => subtotal + tax - discount;
  int get totalItems => cartItems.fold(0, (sum, item) => sum + item.quantity);

  POSState copyWith({
    bool? isLoading,
    String? error,
    List<SimpleProduct>? products,
    List<CartItem>? cartItems,
    String? customerName,
    String? customerPhone,
    double? discount,
    String? paymentMethod,
    String? searchQuery,
    List<POSTabData>? tabs,
    int? activeTabIndex,
  }) {
    return POSState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      products: products ?? this.products,
      cartItems: cartItems ?? this.cartItems,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      discount: discount ?? this.discount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      searchQuery: searchQuery ?? this.searchQuery,
      tabs: tabs ?? this.tabs,
      activeTabIndex: activeTabIndex ?? this.activeTabIndex,
    );
  }
}

// POS Notifier with Multi-Tab Support
class POSNotifier extends StateNotifier<POSState> {
  POSNotifier() : super(const POSState()) {
    loadProducts();
    _initializeTabs();
  }

  // Initialize with default tabs or load saved tabs
  Future<void> _initializeTabs() async {
    final savedData = await POSPersistenceService.loadTabs();

    if (savedData != null) {
      // Load saved tabs
      final tabs = savedData['tabs'] as List<POSTabData>;
      final activeTabIndex = savedData['activeTabIndex'] as int;

      // Ensure activeTabIndex is valid
      final validActiveIndex = activeTabIndex < tabs.length
          ? activeTabIndex
          : 0;

      // Load active tab data to current state
      final activeTab = tabs[validActiveIndex];

      state = state.copyWith(
        tabs: tabs,
        activeTabIndex: validActiveIndex,
        cartItems: activeTab.cartItems,
        customerName: activeTab.customerName,
        customerPhone: activeTab.customerPhone,
        discount: activeTab.discount,
        paymentMethod: activeTab.paymentMethod,
      );

      print('✅ Restored ${tabs.length} tabs from storage');
    } else {
      // Create default tab
      final defaultTabs = [POSTabData(id: '1', name: 'Đơn 1')];
      state = state.copyWith(tabs: defaultTabs, activeTabIndex: 0);
      print('📝 Created default tab');
    }
  }

  Future<void> loadProducts() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Load products from ProductService (same as Inventory)
      final productList = await ProductService.getProducts(isActive: true);

      // Convert Product to SimpleProduct
      final products = productList
          .map(
            (product) => SimpleProduct(
              id: product.id ?? '',
              name: product.name,
              code: product.sku,
              sku: product.barcode,
              salePrice: product.price,
              stockQuantity: product.stockQuantity,
              imageUrl: product.imageUrl,
            ),
          )
          .toList();

      state = state.copyWith(isLoading: false, products: products);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void searchProducts(String query) {
    state = state.copyWith(searchQuery: query);
  }

  void addToCart(SimpleProduct product) {
    final existingIndex = state.cartItems.indexWhere(
      (item) => item.product.id == product.id,
    );

    if (existingIndex >= 0) {
      // Increase quantity if product already in cart
      final updatedItems = List<CartItem>.from(state.cartItems);
      updatedItems[existingIndex] = updatedItems[existingIndex].copyWith(
        quantity: updatedItems[existingIndex].quantity + 1,
      );
      state = state.copyWith(cartItems: updatedItems);
    } else {
      // Add new item to cart
      final newItem = CartItem(product: product);
      state = state.copyWith(cartItems: [...state.cartItems, newItem]);
    }

    // Sync with active tab
    _syncCurrentStateToActiveTab();
  }

  // Sync current state to active tab
  void _syncCurrentStateToActiveTab() {
    if (state.tabs.isNotEmpty && state.activeTabIndex < state.tabs.length) {
      final activeTab = state.tabs[state.activeTabIndex];
      final updatedTab = activeTab.copyWith(
        cartItems: state.cartItems,
        customerName: state.customerName,
        customerPhone: state.customerPhone,
        discount: state.discount,
        paymentMethod: state.paymentMethod,
      );

      final updatedTabs = List<POSTabData>.from(state.tabs);
      updatedTabs[state.activeTabIndex] = updatedTab;

      state = state.copyWith(tabs: updatedTabs);

      // Auto-save to storage
      _saveTabsToStorage();
    }
  }

  // Save tabs to storage (async but non-blocking)
  void _saveTabsToStorage() {
    POSPersistenceService.saveTabsQuick(state.tabs, state.activeTabIndex);
  }

  void updateCartItemQuantity(String productId, int quantity) {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }

    final updatedItems = state.cartItems.map((item) {
      if (item.product.id == productId) {
        return item.copyWith(quantity: quantity);
      }
      return item;
    }).toList();

    state = state.copyWith(cartItems: updatedItems);
    _syncCurrentStateToActiveTab();
  }

  void removeFromCart(String productId) {
    final updatedItems = state.cartItems
        .where((item) => item.product.id != productId)
        .toList();
    state = state.copyWith(cartItems: updatedItems);
    _syncCurrentStateToActiveTab();
  }

  void clearCart() {
    if (state.tabs.isNotEmpty && state.activeTabIndex < state.tabs.length) {
      // Clear active tab's cart
      final activeTab = state.tabs[state.activeTabIndex];
      final clearedTab = activeTab.copyWith(
        cartItems: [],
        customerName: null,
        customerPhone: null,
        discount: 0.0,
      );

      final updatedTabs = List<POSTabData>.from(state.tabs);
      updatedTabs[state.activeTabIndex] = clearedTab;

      state = state.copyWith(
        tabs: updatedTabs,
        cartItems: [],
        customerName: null,
        customerPhone: null,
        discount: 0.0,
      );
    } else {
      // Fallback to old behavior
      state = state.copyWith(
        cartItems: [],
        customerName: null,
        customerPhone: null,
        discount: 0.0,
      );
    }
  }

  // Clear completed order and create new tab for next order
  void _clearCompletedOrder() {
    if (state.tabs.length == 1) {
      // If only one tab, clear it and create a new clean tab
      final newTab = POSTabData(
        id: '1',
        name: 'Đơn 1',
        cartItems: [],
        customerName: null,
        customerPhone: null,
        discount: 0.0,
        paymentMethod: 'cash',
      );

      state = state.copyWith(
        tabs: [newTab],
        activeTabIndex: 0,
        cartItems: [],
        customerName: null,
        customerPhone: null,
        discount: 0.0,
        paymentMethod: 'cash',
      );

      _saveTabsToStorage();
    } else {
      // If multiple tabs, close the completed tab and switch to another
      final currentTabIndex = state.activeTabIndex;

      // Remove the completed tab
      final updatedTabs = List<POSTabData>.from(state.tabs);
      updatedTabs.removeAt(currentTabIndex);

      // Determine new active tab index
      int newActiveIndex = currentTabIndex;
      if (newActiveIndex >= updatedTabs.length) {
        newActiveIndex = updatedTabs.length - 1;
      }

      // Load the new active tab's data
      final newActiveTab = updatedTabs[newActiveIndex];

      state = state.copyWith(
        tabs: updatedTabs,
        activeTabIndex: newActiveIndex,
        cartItems: newActiveTab.cartItems,
        customerName: newActiveTab.customerName,
        customerPhone: newActiveTab.customerPhone,
        discount: newActiveTab.discount,
        paymentMethod: newActiveTab.paymentMethod,
      );

      _saveTabsToStorage();
    }
  }

  // Tab Management Methods
  void switchTab(int tabIndex) {
    if (tabIndex >= 0 && tabIndex < state.tabs.length) {
      final activeTab = state.tabs[tabIndex];
      state = state.copyWith(
        activeTabIndex: tabIndex,
        cartItems: activeTab.cartItems,
        customerName: activeTab.customerName,
        customerPhone: activeTab.customerPhone,
        discount: activeTab.discount,
        paymentMethod: activeTab.paymentMethod,
      );
      _saveTabsToStorage();
    }
  }

  void addNewTab() {
    final newTabNumber = state.tabs.length + 1;
    final newTab = POSTabData(
      id: newTabNumber.toString(),
      name: 'Đơn $newTabNumber',
    );

    final updatedTabs = List<POSTabData>.from(state.tabs)..add(newTab);

    state = state.copyWith(
      tabs: updatedTabs,
      activeTabIndex: updatedTabs.length - 1,
      cartItems: [],
      customerName: null,
      customerPhone: null,
      discount: 0.0,
      paymentMethod: 'cash',
    );
    _saveTabsToStorage();
  }

  void closeTab(int tabIndex) {
    if (state.tabs.length <= 1) return; // Always keep at least one tab

    final updatedTabs = List<POSTabData>.from(state.tabs);
    updatedTabs.removeAt(tabIndex);

    int newActiveIndex = state.activeTabIndex;

    // If closing active tab, switch to another tab
    if (tabIndex == state.activeTabIndex) {
      if (tabIndex > 0) {
        newActiveIndex = tabIndex - 1;
      } else {
        newActiveIndex = 0;
      }
    } else if (tabIndex < state.activeTabIndex) {
      newActiveIndex = state.activeTabIndex - 1;
    }

    // Update state with new tabs and switch to new active tab
    state = state.copyWith(tabs: updatedTabs, activeTabIndex: newActiveIndex);

    // Load the new active tab's data
    if (newActiveIndex < updatedTabs.length) {
      final newActiveTab = updatedTabs[newActiveIndex];
      state = state.copyWith(
        cartItems: newActiveTab.cartItems,
        customerName: newActiveTab.customerName,
        customerPhone: newActiveTab.customerPhone,
        discount: newActiveTab.discount,
        paymentMethod: newActiveTab.paymentMethod,
      );
    }

    _saveTabsToStorage();
  }

  void setCustomerInfo(String? name, String? phone) {
    state = state.copyWith(customerName: name, customerPhone: phone);
    _syncCurrentStateToActiveTab();
  }

  void setDiscount(double discount) {
    state = state.copyWith(discount: discount);
    _syncCurrentStateToActiveTab();
  }

  void setPaymentMethod(String method) {
    state = state.copyWith(paymentMethod: method);
    _syncCurrentStateToActiveTab();
  }

  Future<Order?> createOrder({bool isDraft = false}) async {
    print('🔥 POS V2 PAYMENT DEBUG: Starting order creation...');
    print('🔥 Cart items: ${state.cartItems.length}');
    print('🔥 Is draft: $isDraft');
    print('🔥 Payment method: ${state.paymentMethod}');
    print('🔥 Total: ${state.total}');

    if (state.cartItems.isEmpty) {
      print('🔥 ERROR: Cart is empty!');
      return null;
    }

    // Set loading state
    state = state.copyWith(isLoading: true, error: null);
    print('🔥 Loading state set to true');

    try {
      final orderItems = state.cartItems
          .map(
            (cartItem) => OrderItem(
              id: '550e8400-e29b-41d4-a716-${DateTime.now().millisecondsSinceEpoch.toString().substring(0, 12)}',
              orderId: 'temp_order',
              productId: cartItem.product.id,
              productName: cartItem.product.name,
              quantity: cartItem.quantity,
              unitPrice: cartItem.unitPrice,
              totalAmount: cartItem.totalPrice,
              totalPrice: cartItem.totalPrice,
            ),
          )
          .toList();

      print('🔥 Order items created: ${orderItems.length}');

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final order = Order(
        id: '550e8400-e29b-41d4-a716-${timestamp.toString().substring(0, 12)}',
        orderNumber: 'HD$timestamp',
        type: 'sale',
        status: isDraft ? 'pending' : 'completed',
        subtotal: state.subtotal,
        taxAmount: state.tax,
        discountAmount: state.discount,
        totalAmount: state.total,
        paymentStatus: isDraft ? 'pending' : 'paid',
        paymentMethod: state.paymentMethod,
        items: orderItems,
        notes: state.customerName != null
            ? 'Khách hàng: ${state.customerName}${state.customerPhone != null ? ' - SĐT: ${state.customerPhone}' : ''}'
            : null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      print('🔥 Order object created, calling SalesService...');
      final createdOrder = await SalesService.createOrder(order);
      print('🔥 Order created successfully: ${createdOrder.orderNumber}');

      // Clear loading state on success
      state = state.copyWith(isLoading: false);

      if (!isDraft) {
        _clearCompletedOrder();
        print('🔥 Completed order cleared and new tab created');
      }

      return createdOrder;
    } catch (e) {
      print('🔥 ERROR in order creation: $e');
      // Clear loading state on error
      state = state.copyWith(isLoading: false, error: e.toString());
      return null;
    }
  }
}

// Provider
final posProviderV2 = StateNotifierProvider<POSNotifier, POSState>(
  (ref) => POSNotifier(),
);

class POSScreenV2 extends ConsumerWidget {
  const POSScreenV2({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final state = ref.watch(posProviderV2);
    final responsive = Responsive(context);

    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => context.go(AppRoutes.dashboard),
            ),
            title: Text(l10n.posScreenTitle),
            actions: [
              // Barcode Scanner
              PopupMenuButton<String>(
                icon: const Icon(Icons.qr_code_scanner),
                tooltip: l10n.scanBarcode,
                onSelected: (value) {
                  if (value == 'camera') {
                    _openCameraScanner(context);
                  } else if (value == 'manual') {
                    _showBarcodeScanner(context, ref);
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'camera',
                    child: Row(
                      children: [
                        const Icon(Icons.camera_alt),
                        const SizedBox(width: 8),
                        const Text('Scan with Camera'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'manual',
                    child: Row(
                      children: [
                        const Icon(Icons.keyboard),
                        const SizedBox(width: 8),
                        const Text('Enter Manually'),
                      ],
                    ),
                  ),
                ],
              ),

              // Discount
              IconButton(
                icon: const Icon(Icons.percent),
                onPressed: () => _showDiscountDialog(context, ref),
                tooltip: l10n.discountTooltip,
              ),

              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () =>
                    ref.read(posProviderV2.notifier).loadProducts(),
                tooltip: l10n.refresh,
              ),
              if (state.cartItems.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.clear_all),
                  onPressed: () => _confirmClearCart(context, ref),
                  tooltip: l10n.clearCartTooltip,
                ),
            ],
          ),
          body: Column(
            children: [
              // Tab Bar
              _buildTabBar(context, ref, state, responsive),

              // Content
              Expanded(
                child: responsive.isDesktop
                    ? _buildDesktopLayout(context, ref, state, l10n)
                    : _buildMobileLayout(context, ref, state, l10n),
              ),
            ],
          ),
        ),

        // Loading overlay when processing payment
        if (state.isLoading)
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: Center(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      Text(
                        'Đang xử lý thanh toán...',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    WidgetRef ref,
    POSState state,
    AppLocalizations l10n,
  ) {
    return Row(
      children: [
        // Products Section (Left)
        Expanded(
          flex: 2,
          child: _buildProductsSection(context, ref, state, l10n),
        ),

        // Cart Section (Right)
        Expanded(flex: 1, child: _buildCartSection(context, ref, state, l10n)),
      ],
    );
  }

  Widget _buildMobileLayout(
    BuildContext context,
    WidgetRef ref,
    POSState state,
    AppLocalizations l10n,
  ) {
    return Column(
      children: [
        // Cart Summary (Top)
        _buildCartSummary(context, ref, state, l10n),

        // Products Section (Bottom)
        Expanded(child: _buildProductsSection(context, ref, state, l10n)),
      ],
    );
  }

  Widget _buildProductsSection(
    BuildContext context,
    WidgetRef ref,
    POSState state,
    AppLocalizations l10n,
  ) {
    final responsive = Responsive(context);

    return Column(
      children: [
        // Search Bar
        Container(
          padding: EdgeInsets.all(responsive.isMobile ? 12.0 : 16.0),
          child: TextField(
            decoration: InputDecoration(
              labelText: l10n.posSearchProducts,
              hintText: l10n.posSearchProductsHint,
              prefixIcon: const Icon(Icons.search),
              border: const OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: responsive.isMobile ? 12.0 : 16.0,
              ),
            ),
            onChanged: (value) {
              ref.read(posProviderV2.notifier).searchProducts(value);
            },
          ),
        ),

        // Products Grid
        Expanded(child: _buildProductsGrid(context, ref, state, l10n)),
      ],
    );
  }

  Widget _buildProductsGrid(
    BuildContext context,
    WidgetRef ref,
    POSState state,
    AppLocalizations l10n,
  ) {
    final filteredProducts = _getFilteredProducts(
      state.products,
      state.searchQuery,
    );

    if (filteredProducts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              state.searchQuery.isNotEmpty
                  ? l10n.posNoProductsFound
                  : l10n.posNoProductsYet,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    final responsive = Responsive(context);

    return GridView.builder(
      padding: EdgeInsets.symmetric(
        horizontal: responsive.isMobile ? 8.0 : 16.0,
        vertical: responsive.isMobile ? 4.0 : 8.0,
      ),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getPOSGridCount(context),
        childAspectRatio: _getPOSCardAspectRatio(context),
        crossAxisSpacing: responsive.isMobile ? 6.0 : 12.0,
        mainAxisSpacing: responsive.isMobile ? 6.0 : 12.0,
      ),
      itemCount: filteredProducts.length,
      itemBuilder: (context, index) {
        final product = filteredProducts[index];
        return _buildProductCard(context, ref, product, l10n);
      },
    );
  }

  List<SimpleProduct> _getFilteredProducts(
    List<SimpleProduct> products,
    String query,
  ) {
    if (query.isEmpty) return products;

    final lowerQuery = query.toLowerCase();
    return products.where((product) {
      return product.name.toLowerCase().contains(lowerQuery) ||
          (product.code?.toLowerCase().contains(lowerQuery) ?? false) ||
          (product.sku?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  Widget _buildProductCard(
    BuildContext context,
    WidgetRef ref,
    SimpleProduct product,
    AppLocalizations l10n,
  ) {
    final responsive = Responsive(context);

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => ref.read(posProviderV2.notifier).addToCart(product),
        onLongPress: () => _showQuantityDialog(context, ref, product, l10n),
        borderRadius: BorderRadius.circular(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Expanded(
              flex: 3,
              child: ProductImageWidget(
                productId: product.id,
                initialImageUrl: product.imageUrl,
                isEditable: false,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
            ),

            // Product Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.all(responsive.isMobile ? 6.0 : 8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Product Name
                    Flexible(
                      child: Text(
                        product.name,
                        style: responsive.isMobile
                            ? Theme.of(context).textTheme.labelLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              )
                            : Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        maxLines: responsive.isMobile ? 1 : 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(height: responsive.isMobile ? 2.0 : 4.0),

                    // SKU - Always show if available
                    if (product.sku != null && product.sku!.isNotEmpty)
                      Flexible(
                        child: Text(
                          'SKU: ${product.sku}',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                    if (product.sku != null && product.sku!.isNotEmpty)
                      SizedBox(height: responsive.isMobile ? 2.0 : 4.0),

                    // Spacer - Use Flexible instead of Spacer for better control
                    const Flexible(child: SizedBox(height: 4)),

                    // Price and Stock
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                NumberFormat.currency(
                                  locale: 'vi_VN',
                                  symbol: '₫',
                                ).format(product.salePrice),
                                style: responsive.isMobile
                                    ? Theme.of(
                                        context,
                                      ).textTheme.labelMedium?.copyWith(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      )
                                    : Theme.of(
                                        context,
                                      ).textTheme.titleMedium?.copyWith(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              if (!responsive.isMobile)
                                Text(
                                  'Nhấn giữ để chọn số lượng',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: Colors.grey[500],
                                        fontSize: 10,
                                      ),
                                ),
                            ],
                          ),
                        ),
                        _buildStockBadgeForSimpleProduct(
                          context,
                          product,
                          responsive,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStockBadgeForSimpleProduct(
    BuildContext context,
    SimpleProduct product,
    Responsive responsive,
  ) {
    Color badgeColor;
    String stockText;

    if (product.stockQuantity <= 0) {
      badgeColor = Colors.red;
      stockText = '0';
    } else if (product.stockQuantity <= 5) {
      // Assume low stock threshold
      badgeColor = Colors.orange;
      stockText = '${product.stockQuantity}';
    } else {
      badgeColor = Colors.green;
      stockText = '${product.stockQuantity}';
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: responsive.isMobile ? 4.0 : 6.0,
        vertical: responsive.isMobile ? 1.0 : 2.0,
      ),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(responsive.isMobile ? 3.0 : 4.0),
        border: Border.all(color: badgeColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        stockText,
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          color: badgeColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildCartSection(
    BuildContext context,
    WidgetRef ref,
    POSState state,
    AppLocalizations l10n,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border(left: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          // Cart Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.shopping_cart,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  '${l10n.cartWithCount} (${state.totalItems})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Cart Items
          Expanded(child: _buildCartItems(context, ref, state, l10n)),

          // Quick Actions
          _buildQuickActions(context, ref, state, l10n),

          // Cart Footer
          _buildCartFooter(context, ref, state, l10n),
        ],
      ),
    );
  }

  Widget _buildQuickActions(
    BuildContext context,
    WidgetRef ref,
    POSState state,
    AppLocalizations l10n,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _openCameraScanner(context),
              icon: const Icon(Icons.qr_code_scanner, size: 18),
              label: Text(
                l10n.scanBarcode,
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _showCustomerDialog(context, ref),
              icon: const Icon(Icons.person_add, size: 18),
              label: Text(
                l10n.customerInfoTooltip,
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCartSummary(
    BuildContext context,
    WidgetRef ref,
    POSState state,
    AppLocalizations l10n,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final responsive = Responsive(context);

    return AppCard(
      margin: EdgeInsets.all(responsive.isMobile ? 8.0 : 16.0),
      child: Padding(
        padding: EdgeInsets.all(responsive.isMobile ? 12.0 : 16.0),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // If too narrow, use column layout
            if (constraints.maxWidth < 400) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.shopping_cart,
                        color: Theme.of(context).primaryColor,
                      ),
                      SizedBox(
                        width: Responsive.responsiveSpacing(context) * 0.5,
                      ),
                      Expanded(
                        child: Text(
                          '${state.totalItems} ${l10n.itemCountLabel}',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontSize: Responsive.responsiveFontSize(
                                  context,
                                  mobile: 12.0,
                                  tablet: 14.0,
                                  desktop: 16.0,
                                ),
                              ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: Responsive.responsiveSpacing(context) * 0.5),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          currencyFormat.format(state.total),
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                                fontSize: Responsive.responsiveFontSize(
                                  context,
                                  mobile: 14.0,
                                  tablet: 16.0,
                                  desktop: 18.0,
                                ),
                              ),
                        ),
                      ),
                      SizedBox(width: Responsive.responsiveSpacing(context)),
                      ElevatedButton(
                        onPressed: state.cartItems.isNotEmpty
                            ? () => _showCheckoutDialog(context, ref)
                            : null,
                        child: Text(
                          l10n.checkoutButtonLabel,
                          style: TextStyle(
                            fontSize: Responsive.responsiveFontSize(
                              context,
                              mobile: 11.0,
                              tablet: 12.0,
                              desktop: 13.0,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              );
            } else {
              return Row(
                children: [
                  Icon(
                    Icons.shopping_cart,
                    color: Theme.of(context).primaryColor,
                  ),
                  SizedBox(width: Responsive.responsiveSpacing(context)),
                  Text(
                    '${state.totalItems} ${l10n.itemCountLabel}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const Spacer(),
                  Text(
                    currencyFormat.format(state.total),
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  SizedBox(width: Responsive.responsiveSpacing(context)),
                  ElevatedButton(
                    onPressed: state.cartItems.isNotEmpty
                        ? () => _showCheckoutDialog(context, ref)
                        : null,
                    child: Text(l10n.checkoutButtonLabel),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildCartItems(
    BuildContext context,
    WidgetRef ref,
    POSState state,
    AppLocalizations l10n,
  ) {
    if (state.cartItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              l10n.emptyCartMessage,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              l10n.selectProductsToAddToCart,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: state.cartItems.length,
      itemBuilder: (context, index) {
        final cartItem = state.cartItems[index];
        return _buildCartItemCard(context, ref, cartItem);
      },
    );
  }

  Widget _buildCartItemCard(
    BuildContext context,
    WidgetRef ref,
    CartItem cartItem,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Name and Remove Button
            Row(
              children: [
                Expanded(
                  child: Text(
                    cartItem.product.name,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  onPressed: () => ref
                      .read(posProviderV2.notifier)
                      .removeFromCart(cartItem.product.id),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Quantity Controls and Price
            Row(
              children: [
                // Quantity Controls
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      InkWell(
                        onTap: () => ref
                            .read(posProviderV2.notifier)
                            .updateCartItemQuantity(
                              cartItem.product.id,
                              cartItem.quantity - 1,
                            ),
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            cartItem.quantity > 1
                                ? Icons.remove
                                : Icons.delete_outline,
                            size: 16,
                            color: cartItem.quantity > 1
                                ? Theme.of(context).primaryColor
                                : Colors.red[600],
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        child: Text(
                          cartItem.quantity.toString(),
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                      ),
                      InkWell(
                        onTap: () => ref
                            .read(posProviderV2.notifier)
                            .updateCartItemQuantity(
                              cartItem.product.id,
                              cartItem.quantity + 1,
                            ),
                        child: const Padding(
                          padding: EdgeInsets.all(8),
                          child: Icon(Icons.add, size: 16),
                        ),
                      ),
                    ],
                  ),
                ),

                const Spacer(),

                // Price
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      currencyFormat.format(cartItem.unitPrice),
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                    Text(
                      currencyFormat.format(cartItem.totalPrice),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartFooter(
    BuildContext context,
    WidgetRef ref,
    POSState state,
    AppLocalizations l10n,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          // Totals
          Row(
            children: [
              Expanded(child: Text('${l10n.posSubtotal}:')),
              Flexible(
                child: Text(
                  currencyFormat.format(state.subtotal),
                  textAlign: TextAlign.end,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(child: Text('${l10n.posTax}:')),
              Flexible(
                child: Text(
                  currencyFormat.format(state.tax),
                  textAlign: TextAlign.end,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          if (state.discount > 0) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Expanded(child: Text('${l10n.posDiscount}:')),
                Flexible(
                  child: Text(
                    '-${currencyFormat.format(state.discount)}',
                    textAlign: TextAlign.end,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
          const Divider(),
          Row(
            children: [
              Expanded(
                child: Text(
                  '${l10n.posGrandTotal}:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Flexible(
                child: Text(
                  currencyFormat.format(state.total),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                  textAlign: TextAlign.end,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: state.cartItems.isNotEmpty
                      ? () => _saveDraft(context, ref, l10n)
                      : null,
                  child: Text(l10n.posSaveDraft),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton(
                  onPressed: state.cartItems.isNotEmpty
                      ? () => _showCheckoutDialog(context, ref)
                      : null,
                  child: Text(l10n.posCheckout),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _saveDraft(
    BuildContext context,
    WidgetRef ref,
    AppLocalizations l10n,
  ) async {
    final order = await ref
        .read(posProviderV2.notifier)
        .createOrder(isDraft: true);

    if (order != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(l10n.posDraftSaved),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _openCameraScanner(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const BarcodeScannerScreen()),
    );
  }

  void _showBarcodeScanner(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.scanBarcodeTitle),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: l10n.enterBarcodeLabel,
                hintText: l10n.scanOrEnterBarcodeHint,
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.qr_code),
              ),
              autofocus: true,
              onSubmitted: (barcode) {
                if (barcode.isNotEmpty) {
                  _scanBarcode(context, ref, barcode);
                  Navigator.of(context).pop();
                }
              },
            ),
            const SizedBox(height: 16),
            Text(
              l10n.orUseCameraToScan,
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.barcodeDialogCancelLabel),
          ),
        ],
      ),
    );
  }

  void _scanBarcode(BuildContext context, WidgetRef ref, String barcode) {
    final state = ref.read(posProviderV2);
    final l10n = AppLocalizations.of(context);

    // Debug: Print all products and their codes
    print('=== BARCODE SCAN DEBUG ===');
    print('Scanning for barcode: $barcode');
    print('Total products: ${state.products.length}');
    for (final p in state.products.take(5)) {
      print('Product: ${p.name}');
      print('  - code: ${p.code}');
      print('  - sku: ${p.sku}');
      print('  - displayCode: ${p.displayCode}');
    }

    try {
      // Find product by barcode, code, sku, displayCode, or name
      final product = state.products.firstWhere(
        (p) =>
            p.code?.toLowerCase() == barcode.toLowerCase() ||
            p.sku?.toLowerCase() == barcode.toLowerCase() ||
            p.displayCode.toLowerCase() == barcode.toLowerCase() ||
            p.id.toLowerCase() == barcode.toLowerCase() ||
            p.name.toLowerCase().contains(barcode.toLowerCase()) ||
            p.name.toLowerCase() == barcode.toLowerCase(),
      );

      print('Found product: ${product.name}');
      ref.read(posProviderV2.notifier).addToCart(product);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(l10n.addedToCartMessage(product.name)),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      print('Product not found for barcode: $barcode');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(l10n.productNotFoundMessage(barcode)),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showCustomerDialog(BuildContext context, WidgetRef ref) {
    final state = ref.read(posProviderV2);
    final l10n = AppLocalizations.of(context);
    final nameController = TextEditingController(text: state.customerName);
    final phoneController = TextEditingController(text: state.customerPhone);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.customerInfoDialogTitle),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: l10n.customerNameFieldLabel,
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.person),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: phoneController,
              decoration: InputDecoration(
                labelText: l10n.customerPhoneFieldLabel,
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancelButton),
          ),
          ElevatedButton(
            onPressed: () {
              ref
                  .read(posProviderV2.notifier)
                  .setCustomerInfo(
                    nameController.text.trim().isEmpty
                        ? null
                        : nameController.text.trim(),
                    phoneController.text.trim().isEmpty
                        ? null
                        : phoneController.text.trim(),
                  );
              Navigator.of(context).pop();
            },
            child: Text(l10n.saveButtonLabel),
          ),
        ],
      ),
    );
  }

  void _showDiscountDialog(BuildContext context, WidgetRef ref) {
    final state = ref.read(posProviderV2);
    final l10n = AppLocalizations.of(context);
    final discountController = TextEditingController(
      text: state.discount > 0 ? state.discount.toString() : '',
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.discountDialogTitle),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: discountController,
              decoration: InputDecoration(
                labelText: l10n.discountAmountFieldLabel,
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.money_off),
                suffixText: '₫',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 8),
            Text(
              '${l10n.subtotalDisplayLabel}: ${NumberFormat.currency(locale: 'vi_VN', symbol: '₫').format(state.subtotal)}',
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancelButton),
          ),
          ElevatedButton(
            onPressed: () {
              final discount = double.tryParse(discountController.text) ?? 0.0;
              ref.read(posProviderV2.notifier).setDiscount(discount);
              Navigator.of(context).pop();
            },
            child: Text(l10n.applyButtonLabel),
          ),
        ],
      ),
    );
  }

  void _showCheckoutDialog(BuildContext context, WidgetRef ref) {
    final state = ref.read(posProviderV2);

    // Navigate to payment screen instead of showing dialog
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PaymentScreen(
          total: state.total,
          cartItems: state.cartItems
              .map(
                (item) => {
                  'id': item.product.id,
                  'name': item.product.name,
                  'price': item.unitPrice,
                  'quantity': item.quantity,
                },
              )
              .toList(),
          onQuantityChanged: (productId, newQuantity) {
            ref
                .read(posProviderV2.notifier)
                .updateCartItemQuantity(productId, newQuantity);
          },
          onPayment:
              (
                paymentMethod,
                amountPaid,
                customerName,
                customerPhone,
                partner,
              ) async {
                // Update payment method
                ref
                    .read(posProviderV2.notifier)
                    .setPaymentMethod(paymentMethod);

                // Update customer info if provided
                if (customerName != null || customerPhone != null) {
                  ref
                      .read(posProviderV2.notifier)
                      .setCustomerInfo(customerName, customerPhone);
                }

                // Create order
                final order = await ref
                    .read(posProviderV2.notifier)
                    .createOrder();

                if (order != null && context.mounted) {
                  // Check invoice display setting
                  final invoiceSettings = ref.read(invoiceSettingsProvider);

                  if (invoiceSettings.showInvoiceAfterPayment) {
                    // Navigate to invoice display screen
                    context.go(AppRoutes.invoiceDisplay, extra: order);
                  } else {
                    // Show receipt option (original behavior)
                    _showReceiptDialog(context, order);
                  }
                }
              },
        ),
      ),
    );
  }

  String _getPaymentMethodName(String method, BuildContext context) {
    final l10n = AppLocalizations.of(context);
    switch (method) {
      case 'cash':
        return l10n.cashMethodLabel;
      case 'card':
        return l10n.creditCardMethodLabel;
      case 'transfer':
        return l10n.bankTransferMethodLabel;
      case 'other':
        return l10n.otherMethodLabel;
      default:
        return l10n.cashMethodLabel;
    }
  }

  void _showReceiptDialog(BuildContext context, Order order) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.printReceiptDialogTitle),
        content: Text(l10n.printReceiptDialogContent),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.noButtonLabel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _printReceipt(context, order);
            },
            child: Text(l10n.printReceiptButtonLabel),
          ),
        ],
      ),
    );
  }

  void _printReceipt(BuildContext context, Order order) {
    final l10n = AppLocalizations.of(context);
    // For now, just show a dialog with receipt preview
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.invoiceTitle),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Column(
                  children: [
                    const Text(
                      'CITY POS',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(l10n.salesInvoice),
                    Text('${l10n.invoiceNumber}: ${order.orderNumber}'),
                    Text(
                      '${l10n.dateLabel}: ${DateFormat('dd/MM/yyyy HH:mm').format(order.createdAt ?? DateTime.now())}',
                    ),
                  ],
                ),
              ),
              const Divider(),
              if (order.notes != null) ...[
                Text(order.notes!),
                const SizedBox(height: 8),
              ],
              Text(
                '${l10n.orderDetailsLabel}:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              ...order.items.map(
                (item) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text('${item.productName} x${item.quantity}'),
                      ),
                      Text(
                        NumberFormat.currency(
                          locale: 'vi_VN',
                          symbol: '₫',
                        ).format(item.totalAmount),
                      ),
                    ],
                  ),
                ),
              ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('${l10n.subtotalLabel}:'),
                  Text(
                    NumberFormat.currency(
                      locale: 'vi_VN',
                      symbol: '₫',
                    ).format(order.subtotal),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('${l10n.taxReceiptLabel}:'),
                  Text(
                    NumberFormat.currency(
                      locale: 'vi_VN',
                      symbol: '₫',
                    ).format(order.taxAmount),
                  ),
                ],
              ),
              if (order.discountAmount > 0)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('${l10n.discountReceiptLabel}:'),
                    Text(
                      '-${NumberFormat.currency(locale: 'vi_VN', symbol: '₫').format(order.discountAmount)}',
                    ),
                  ],
                ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${l10n.totalAmountLabel}:',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    NumberFormat.currency(
                      locale: 'vi_VN',
                      symbol: '₫',
                    ).format(order.totalAmount),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '${l10n.paymentMethodLabel}: ${_getPaymentMethodName(order.paymentMethod ?? 'cash', context)}',
              ),
              const SizedBox(height: 16),
              Center(child: Text(l10n.thankYouMessage)),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.closeDialogButton),
          ),
        ],
      ),
    );
  }

  void _confirmClearCart(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.confirmationTitle),
        content: Text(l10n.clearCartConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancelButton),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(posProviderV2.notifier).clearCart();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(l10n.deleteButton),
          ),
        ],
      ),
    );
  }

  // POS-specific responsive methods
  int _getPOSGridCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    // For POS, we want larger cards that are readable
    // Calculate based on minimum card width of 180px for better readability
    const double minCardWidth = 180.0;
    const double spacing = 12.0;
    const double padding = 32.0;

    final availableWidth = width - padding;
    final maxColumns = (availableWidth / (minCardWidth + spacing)).floor();

    // Conservative grid count for POS - prioritize readability over density
    if (width < 600) return 2; // Mobile - 2 columns
    if (width < 900) return 3; // Small tablet - 3 columns
    if (width < 1200) return 4; // Large tablet - 4 columns
    if (width < 1600) return 5; // Desktop - 5 columns
    return maxColumns.clamp(5, 6); // Large desktop - max 6 columns
  }

  double _getPOSCardAspectRatio(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final gridCount = _getPOSGridCount(context);

    // Taller aspect ratios for better content display
    if (width < 600) {
      // Mobile: taller cards for better readability
      return 0.75; // 3:4 ratio
    } else if (width < 1200) {
      // Tablet: balanced ratio
      return 0.85; // Slightly taller
    } else {
      // Desktop: adjust based on grid density
      if (gridCount <= 4) {
        return 0.9; // More square for fewer columns
      } else {
        return 0.8; // Taller for more columns
      }
    }
  }

  // Tab Bar for Multi-Tab POS
  Widget _buildTabBar(
    BuildContext context,
    WidgetRef ref,
    POSState state,
    Responsive responsive,
  ) {
    final tabs = state.tabs;
    final activeTabIndex = state.activeTabIndex;

    return Container(
      height: responsive.isMobile ? 48 : 56,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Tabs
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(
                horizontal: responsive.isMobile ? 8 : 16,
                vertical: 4,
              ),
              itemCount: tabs.length,
              itemBuilder: (context, index) {
                final tab = tabs[index];
                final isActive = index == activeTabIndex;
                final hasItems = tab.totalItems > 0;

                return Container(
                  margin: const EdgeInsets.only(right: 4),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        // Real switch tab logic
                        ref.read(posProviderV2.notifier).switchTab(index);
                      },
                      borderRadius: BorderRadius.circular(8),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        padding: EdgeInsets.symmetric(
                          horizontal: responsive.isMobile ? 8 : 12,
                          vertical: responsive.isMobile ? 6 : 8,
                        ),
                        decoration: BoxDecoration(
                          color: isActive
                              ? Theme.of(
                                  context,
                                ).primaryColor.withValues(alpha: 0.1)
                              : Colors.transparent,
                          border: Border.all(
                            color: isActive
                                ? Theme.of(context).primaryColor
                                : Colors.grey.withValues(alpha: 0.3),
                            width: isActive ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Tab Name
                            Text(
                              tab.name,
                              style: TextStyle(
                                color: isActive
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey[700],
                                fontSize: responsive.isMobile ? 12 : 13,
                                fontWeight: isActive
                                    ? FontWeight.bold
                                    : FontWeight.w500,
                              ),
                            ),

                            // Item Count Badge
                            if (hasItems) ...[
                              const SizedBox(width: 6),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: isActive
                                      ? Theme.of(context).primaryColor
                                      : Colors.grey[400],
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Text(
                                  '${tab.totalItems}',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: responsive.isMobile ? 10 : 11,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],

                            // Close Button (only if more than 1 tab and not active)
                            if (tabs.length > 1 && !isActive) ...[
                              const SizedBox(width: 6),
                              InkWell(
                                onTap: () {
                                  // Real close tab logic
                                  ref
                                      .read(posProviderV2.notifier)
                                      .closeTab(index);
                                },
                                borderRadius: BorderRadius.circular(12),
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  child: Icon(
                                    Icons.close,
                                    size: responsive.isMobile ? 14 : 16,
                                    color: Colors.grey[500],
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Add Tab Button
          Container(
            margin: EdgeInsets.symmetric(
              horizontal: responsive.isMobile ? 8 : 16,
              vertical: 4,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  // Real add new tab logic
                  ref.read(posProviderV2.notifier).addNewTab();
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: responsive.isMobile ? 12 : 16,
                    vertical: responsive.isMobile ? 8 : 12,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Theme.of(
                        context,
                      ).primaryColor.withValues(alpha: 0.3),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.add,
                        size: responsive.isMobile ? 16 : 18,
                        color: Theme.of(context).primaryColor,
                      ),
                      if (!responsive.isMobile) ...[
                        const SizedBox(width: 4),
                        Text(
                          'Đơn mới',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showQuantityDialog(
    BuildContext context,
    WidgetRef ref,
    SimpleProduct product,
    AppLocalizations l10n,
  ) {
    int quantity = 1;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(
                    Icons.shopping_cart,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Thêm vào giỏ hàng',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Info
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.inventory_2_outlined,
                            color: Colors.grey[600],
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                product.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              if (product.sku != null &&
                                  product.sku!.isNotEmpty)
                                Text(
                                  'SKU: ${product.sku}',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                              Text(
                                NumberFormat.currency(
                                  locale: 'vi_VN',
                                  symbol: '₫',
                                ).format(product.salePrice),
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Quantity Controls
                  Row(
                    children: [
                      Text(
                        'Số lượng:',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                          color: Colors.grey[700],
                        ),
                      ),
                      const Spacer(),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            InkWell(
                              onTap: quantity > 1
                                  ? () {
                                      setState(() {
                                        quantity--;
                                      });
                                    }
                                  : null,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(8),
                                bottomLeft: Radius.circular(8),
                              ),
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                child: Icon(
                                  Icons.remove,
                                  size: 20,
                                  color: quantity > 1
                                      ? Theme.of(context).primaryColor
                                      : Colors.grey[400],
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                border: Border.symmetric(
                                  vertical: BorderSide(
                                    color: Colors.grey[300]!,
                                  ),
                                ),
                              ),
                              child: Text(
                                '$quantity',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                setState(() {
                                  quantity++;
                                });
                              },
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(8),
                                bottomRight: Radius.circular(8),
                              ),
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                child: Icon(
                                  Icons.add,
                                  size: 20,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Total Price
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Theme.of(
                          context,
                        ).primaryColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Tổng cộng:',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        Text(
                          NumberFormat.currency(
                            locale: 'vi_VN',
                            symbol: '₫',
                          ).format(product.salePrice * quantity),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('Hủy', style: TextStyle(color: Colors.grey[600])),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    // Add multiple items to cart
                    for (int i = 0; i < quantity; i++) {
                      ref.read(posProviderV2.notifier).addToCart(product);
                    }
                    Navigator.of(context).pop();

                    // Show success message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Đã thêm $quantity ${product.name} vào giỏ hàng',
                        ),
                        backgroundColor: Colors.green,
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                  icon: const Icon(Icons.add_shopping_cart),
                  label: const Text('Thêm vào giỏ'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
