import 'package:flutter/material.dart';

import '../../../../generated/l10n/app_localizations.dart';

class CategoryTabsWidget extends StatelessWidget {
  final String selectedCategory;
  final Function(String) onCategorySelected;

  const CategoryTabsWidget({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final categories = [
      {'id': 'all', 'name': l10n.allProducts, 'icon': Icons.apps},
      {'id': 'drinks', 'name': l10n.drinks, 'icon': Icons.local_drink},
      {'id': 'food', 'name': l10n.food, 'icon': Icons.restaurant},
      {'id': 'snacks', 'name': l10n.snacks, 'icon': Icons.cake},
      {'id': 'other', 'name': l10n.other, 'icon': Icons.more_horiz},
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: categories.map((category) {
          final isSelected = selectedCategory == category['id'];

          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              selected: isSelected,
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    category['icon'] as IconData,
                    size: 16,
                    color: isSelected ? Colors.white : Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(category['name'] as String),
                ],
              ),
              onSelected: (selected) {
                if (selected) {
                  onCategorySelected(category['id'] as String);
                }
              },
              selectedColor: Theme.of(context).primaryColor,
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
