import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

import '../../../core/utils/responsive.dart';
import '../../../generated/l10n/app_localizations.dart';

class PaymentScreen extends StatefulWidget {
  final double total;
  final List<Map<String, dynamic>> cartItems;
  final Function(String, double, String?, String?) onPayment;
  final Function(String productId, int newQuantity)? onQuantityChanged;

  const PaymentScreen({
    super.key,
    required this.total,
    required this.cartItems,
    required this.onPayment,
    this.onQuantityChanged,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen>
    with TickerProviderStateMixin {
  String _selectedPaymentMethod = 'cash';
  final _amountController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isProcessing = false;
  bool _showCustomerInfo = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Local cart items state for real-time updates
  late List<Map<String, dynamic>> _localCartItems;
  late double _localTotal;

  @override
  void initState() {
    super.initState();

    // Initialize local state
    _localCartItems = List<Map<String, dynamic>>.from(widget.cartItems);
    _localTotal = widget.total;

    _amountController.text = _localTotal.toStringAsFixed(0);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _notesController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _updateQuantity(Map<String, dynamic> item, int change) {
    final currentQuantity = item['quantity'] ?? 0;
    final newQuantity = currentQuantity + change;

    setState(() {
      final itemIndex = _localCartItems.indexWhere(
        (cartItem) => cartItem['id'] == item['id'],
      );

      if (itemIndex >= 0) {
        if (newQuantity <= 0) {
          // Remove item from cart when quantity reaches 0
          _localCartItems.removeAt(itemIndex);

          // Show removal message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Đã xóa ${item['name']} khỏi giỏ hàng'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 2),
            ),
          );
        } else {
          // Update quantity
          _localCartItems[itemIndex]['quantity'] = newQuantity;
        }

        // Recalculate total
        _localTotal = _localCartItems.fold(0.0, (sum, cartItem) {
          return sum +
              ((cartItem['price'] ?? 0.0) * (cartItem['quantity'] ?? 0));
        });

        // Update amount controller
        _amountController.text = _localTotal.toStringAsFixed(0);
      }
    });

    // Notify parent widget
    if (widget.onQuantityChanged != null) {
      if (newQuantity <= 0) {
        // Remove from parent cart
        widget.onQuantityChanged!(item['id'] ?? '', 0);
      } else {
        // Update quantity in parent cart
        widget.onQuantityChanged!(item['id'] ?? '', newQuantity);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive(context);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(context, l10n),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: responsive.isMobile
            ? _buildMobileLayout(context, responsive, l10n)
            : _buildDesktopLayout(context, responsive, l10n),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
    BuildContext context,
    AppLocalizations l10n,
  ) {
    return AppBar(
      title: Row(
        children: [
          Icon(Icons.payment, color: Colors.white),
          const SizedBox(width: 8),
          Text(
            l10n.paymentTitle,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 1,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        if (!Responsive(context).isMobile)
          TextButton.icon(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
            label: Text(
              l10n.cancelButton,
              style: const TextStyle(color: Colors.white),
            ),
          ),
      ],
    );
  }

  Widget _buildMobileLayout(
    BuildContext context,
    Responsive responsive,
    AppLocalizations l10n,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOrderSummaryCard(context, l10n, true),
          const SizedBox(height: 16),
          _buildPaymentMethodSection(context, l10n, true),
          const SizedBox(height: 16),
          _buildAmountSection(context, l10n, true),
          const SizedBox(height: 16),
          _buildCustomerInfoSection(context, l10n, true),
          const SizedBox(height: 24),
          _buildActionButtons(context, l10n, true),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    Responsive responsive,
    AppLocalizations l10n,
  ) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left side - Order Summary
          Expanded(
            flex: 2,
            child: _buildOrderSummaryCard(context, l10n, false),
          ),
          const SizedBox(width: 24),
          // Right side - Payment Details
          Expanded(
            flex: 3,
            child: Column(
              children: [
                _buildPaymentMethodSection(context, l10n, false),
                const SizedBox(height: 24),
                _buildAmountSection(context, l10n, false),
                const SizedBox(height: 24),
                _buildCustomerInfoSection(context, l10n, false),
                const SizedBox(height: 32),
                _buildActionButtons(context, l10n, false),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummaryCard(
    BuildContext context,
    AppLocalizations l10n,
    bool isMobile,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 16 : 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.receipt_long, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Chi tiết đơn hàng',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Cart Items
            Container(
              constraints: BoxConstraints(maxHeight: isMobile ? 200 : 300),
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: _localCartItems.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final item = _localCartItems[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              flex: 3,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item['name'] ?? '',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    '${currencyFormat.format(item['price'] ?? 0)} / đơn vị',
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                currencyFormat.format(
                                  (item['price'] ?? 0) *
                                      (item['quantity'] ?? 0),
                                ),
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // Quantity Controls
                        Row(
                          children: [
                            Text(
                              'Số lượng:',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                            const Spacer(),
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  InkWell(
                                    onTap: () => _updateQuantity(item, -1),
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(8),
                                      bottomLeft: Radius.circular(8),
                                    ),
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      child: Icon(
                                        (item['quantity'] ?? 0) > 1
                                            ? Icons.remove
                                            : Icons.delete_outline,
                                        size: 16,
                                        color: (item['quantity'] ?? 0) > 1
                                            ? Theme.of(context).primaryColor
                                            : Colors.red[600],
                                      ),
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      border: Border.symmetric(
                                        vertical: BorderSide(
                                          color: Colors.grey[300]!,
                                        ),
                                      ),
                                    ),
                                    child: Text(
                                      '${item['quantity'] ?? 0}',
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleSmall
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () => _updateQuantity(item, 1),
                                    borderRadius: const BorderRadius.only(
                                      topRight: Radius.circular(8),
                                      bottomRight: Radius.circular(8),
                                    ),
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      child: Icon(
                                        Icons.add,
                                        size: 16,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            const Divider(thickness: 2),

            // Total
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Tổng cộng:',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    currencyFormat.format(_localTotal),
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodSection(
    BuildContext context,
    AppLocalizations l10n,
    bool isMobile,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 16 : 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.payment, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Phương thức thanh toán',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Payment Methods Grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: isMobile ? 2 : 3,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: isMobile ? 1.5 : 1.2,
              children: [
                _buildPaymentMethodCard(
                  'cash',
                  'Tiền mặt',
                  Icons.money,
                  Colors.green,
                ),
                _buildPaymentMethodCard(
                  'card',
                  'Thẻ tín dụng',
                  Icons.credit_card,
                  Colors.blue,
                ),
                _buildPaymentMethodCard(
                  'transfer',
                  'Chuyển khoản',
                  Icons.account_balance,
                  Colors.purple,
                ),
                _buildPaymentMethodCard(
                  'qr',
                  'QR Code',
                  Icons.qr_code,
                  Colors.orange,
                ),
                _buildPaymentMethodCard(
                  'ewallet',
                  'Ví điện tử',
                  Icons.account_balance_wallet,
                  Colors.teal,
                ),
                _buildPaymentMethodCard(
                  'mixed',
                  'Kết hợp',
                  Icons.merge_type,
                  Colors.indigo,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodCard(
    String method,
    String label,
    IconData icon,
    Color color,
  ) {
    final isSelected = _selectedPaymentMethod == method;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedPaymentMethod = method;
          if (method != 'cash' && method != 'mixed') {
            _amountController.text = _localTotal.toStringAsFixed(0);
          }
        });
      },
      borderRadius: BorderRadius.circular(12),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: EdgeInsets.all(isSelected ? 8 : 12), // Thu gọn khi được chọn
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? color : Colors.grey.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.white,
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: EdgeInsets.all(
                isSelected ? 6 : 8,
              ), // Thu gọn icon container
              decoration: BoxDecoration(
                color: isSelected
                    ? color.withValues(alpha: 0.2)
                    : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isSelected ? color : Colors.grey[600],
                size: isSelected ? 20 : 24, // Thu gọn icon
              ),
            ),
            SizedBox(height: isSelected ? 4 : 8), // Thu gọn spacing
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                fontSize: isSelected ? 11 : 12, // Thu gọn text
              ),
              textAlign: TextAlign.center,
            ),
            if (isSelected)
              Container(
                margin: const EdgeInsets.only(top: 4),
                child: Icon(
                  Icons.check_circle,
                  color: color,
                  size: 14,
                ), // Thu gọn checkmark
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSection(
    BuildContext context,
    AppLocalizations l10n,
    bool isMobile,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final amountPaid = double.tryParse(_amountController.text) ?? 0;
    final change = amountPaid - _localTotal;

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 16 : 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calculate, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Số tiền thanh toán',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Amount Input
            TextField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
              decoration: InputDecoration(
                labelText: 'Số tiền khách đưa',
                border: const OutlineInputBorder(),
                suffixText: '₫',
                prefixIcon: const Icon(Icons.money),
                hintText: 'Nhập số tiền...',
                filled: true,
                fillColor: Colors.grey[50],
              ),
              onChanged: (value) {
                setState(() {});
              },
            ),

            // Quick Amount Buttons for Cash
            if (_selectedPaymentMethod == 'cash') ...[
              const SizedBox(height: 16),
              Text(
                'Số tiền gợi ý:',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildQuickAmountButton(_localTotal, 'Vừa đủ'),
                  _buildQuickAmountButton(_localTotal + 10000, '+10K'),
                  _buildQuickAmountButton(_localTotal + 20000, '+20K'),
                  _buildQuickAmountButton(_localTotal + 50000, '+50K'),
                  _buildQuickAmountButton(_localTotal + 100000, '+100K'),
                  _buildQuickAmountButton(_localTotal + 200000, '+200K'),
                ],
              ),
            ],

            const SizedBox(height: 16),

            // Change Amount
            if (change >= 0) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: change > 0
                      ? Colors.blue.withValues(alpha: 0.1)
                      : Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: change > 0
                        ? Colors.blue.withValues(alpha: 0.3)
                        : Colors.green.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      change > 0 ? Icons.change_circle : Icons.check_circle,
                      color: change > 0 ? Colors.blue[700] : Colors.green[700],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            change > 0
                                ? 'Tiền thối lại:'
                                : 'Thanh toán chính xác',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: change > 0
                                  ? Colors.blue[700]
                                  : Colors.green[700],
                            ),
                          ),
                          if (change > 0)
                            Text(
                              currencyFormat.format(change),
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue[700],
                                  ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.red[700]),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Số tiền chưa đủ',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.red[700],
                            ),
                          ),
                          Text(
                            'Còn thiếu: ${currencyFormat.format(-change)}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAmountButton(double amount, String label) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return OutlinedButton(
      onPressed: () {
        _amountController.text = amount.toStringAsFixed(0);
        setState(() {});
      },
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        side: BorderSide(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 10, fontWeight: FontWeight.w500),
          ),
          Text(
            currencyFormat.format(amount),
            style: const TextStyle(fontSize: 11),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerInfoSection(
    BuildContext context,
    AppLocalizations l10n,
    bool isMobile,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 16 : 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header với responsive layout
            isMobile
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.person,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Thông tin khách hàng',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            '(Tùy chọn)',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                          const Spacer(),
                          Switch(
                            value: _showCustomerInfo,
                            onChanged: (value) {
                              setState(() {
                                _showCustomerInfo = value;
                                if (!value) {
                                  _customerNameController.clear();
                                  _customerPhoneController.clear();
                                }
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  )
                : Row(
                    children: [
                      Icon(Icons.person, color: Theme.of(context).primaryColor),
                      const SizedBox(width: 8),
                      Text(
                        'Thông tin khách hàng',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '(Tùy chọn)',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Switch(
                        value: _showCustomerInfo,
                        onChanged: (value) {
                          setState(() {
                            _showCustomerInfo = value;
                            if (!value) {
                              _customerNameController.clear();
                              _customerPhoneController.clear();
                            }
                          });
                        },
                      ),
                    ],
                  ),

            if (_showCustomerInfo) ...[
              const SizedBox(height: 16),
              // Customer fields với responsive layout
              isMobile
                  ? Column(
                      children: [
                        TextField(
                          controller: _customerNameController,
                          decoration: const InputDecoration(
                            labelText: 'Tên khách hàng',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.person_outline),
                            hintText: 'Nhập tên khách hàng...',
                          ),
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: _customerPhoneController,
                          keyboardType: TextInputType.phone,
                          decoration: const InputDecoration(
                            labelText: 'Số điện thoại',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.phone_outlined),
                            hintText: 'Nhập số điện thoại...',
                          ),
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _customerNameController,
                            decoration: const InputDecoration(
                              labelText: 'Tên khách hàng',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.person_outline),
                              hintText: 'Nhập tên khách hàng...',
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextField(
                            controller: _customerPhoneController,
                            keyboardType: TextInputType.phone,
                            decoration: const InputDecoration(
                              labelText: 'Số điện thoại',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.phone_outlined),
                              hintText: 'Nhập số điện thoại...',
                            ),
                          ),
                        ),
                      ],
                    ),
              const SizedBox(height: 16),
              TextField(
                controller: _notesController,
                maxLines: 2,
                decoration: const InputDecoration(
                  labelText: 'Ghi chú',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note_outlined),
                  hintText: 'Ghi chú thêm về đơn hàng...',
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    AppLocalizations l10n,
    bool isMobile,
  ) {
    final amountPaid = double.tryParse(_amountController.text) ?? 0;
    final canProcess = amountPaid >= widget.total;

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 16 : 24),
        child: Column(
          children: [
            // Payment Summary
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Phương thức:'),
                      Text(
                        _getPaymentMethodLabel(_selectedPaymentMethod),
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Trạng thái:'),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: canProcess
                              ? Colors.green.withValues(alpha: 0.1)
                              : Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          canProcess
                              ? 'Sẵn sàng thanh toán'
                              : 'Chưa đủ số tiền',
                          style: TextStyle(
                            color: canProcess
                                ? Colors.green[700]
                                : Colors.red[700],
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Action Buttons
            if (isMobile) ...[
              // Mobile: Stacked buttons
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isProcessing || !canProcess
                      ? null
                      : _processPayment,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isProcessing
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            ),
                            SizedBox(width: 12),
                            Text('Đang xử lý...'),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.payment),
                            const SizedBox(width: 8),
                            Text(
                              'Thanh toán ${NumberFormat.currency(locale: 'vi_VN', symbol: '₫').format(widget.total)}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: _isProcessing
                      ? null
                      : () => Navigator.of(context).pop(),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Hủy bỏ'),
                ),
              ),
            ] else ...[
              // Desktop: Side by side buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isProcessing
                          ? null
                          : () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Hủy bỏ'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _isProcessing || !canProcess
                          ? null
                          : _processPayment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: _isProcessing
                          ? const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 12),
                                Text('Đang xử lý...'),
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.payment),
                                const SizedBox(width: 8),
                                Text(
                                  'Thanh toán ${NumberFormat.currency(locale: 'vi_VN', symbol: '₫').format(widget.total)}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getPaymentMethodLabel(String method) {
    switch (method) {
      case 'cash':
        return 'Tiền mặt';
      case 'card':
        return 'Thẻ tín dụng';
      case 'transfer':
        return 'Chuyển khoản';
      case 'qr':
        return 'QR Code';
      case 'ewallet':
        return 'Ví điện tử';
      case 'mixed':
        return 'Kết hợp';
      default:
        return 'Không xác định';
    }
  }

  void _processPayment() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    setState(() {
      _isProcessing = true;
    });

    try {
      final amountPaid = double.parse(_amountController.text);
      final customerName = _customerNameController.text.trim();
      final customerPhone = _customerPhoneController.text.trim();

      // Simulate processing time
      await Future.delayed(const Duration(seconds: 2));

      // Call payment callback first (which will handle navigation)
      await widget.onPayment(
        _selectedPaymentMethod,
        amountPaid,
        customerName.isEmpty ? null : customerName,
        customerPhone.isEmpty ? null : customerPhone,
      );

      // Then close payment screen
      if (mounted) {
        navigator.pop();
      }

      // Show success message
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text(
                'Thanh toán thành công ${NumberFormat.currency(locale: 'vi_VN', symbol: '₫').format(widget.total)}',
              ),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Text('Lỗi thanh toán: $e'),
            ],
          ),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
}
