enum PartnerType { customer, supplier, both }

extension PartnerTypeExtension on PartnerType {
  String get displayName {
    switch (this) {
      case PartnerType.customer:
        return 'Khách hàng';
      case PartnerType.supplier:
        return 'Nhà cung cấp';
      case PartnerType.both:
        return 'Khách hàng & NCC';
    }
  }

  String get value {
    switch (this) {
      case PartnerType.customer:
        return 'customer';
      case PartnerType.supplier:
        return 'supplier';
      case PartnerType.both:
        return 'both';
    }
  }
}

class Partner {
  final String id;
  final String name;
  final PartnerType type;
  final String? email;
  final String? phone;
  final String? address;
  final String? taxNumber;
  final double creditLimit;
  final double currentBalance;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;

  const Partner({
    required this.id,
    required this.name,
    required this.type,
    this.email,
    this.phone,
    this.address,
    this.taxNumber,
    this.creditLimit = 0.0,
    this.currentBalance = 0.0,
    this.isActive = true,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
  });

  factory Partner.fromJson(Map<String, dynamic> json) {
    return Partner(
      id: json['id'] as String,
      name: json['name'] as String,
      type: PartnerType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => PartnerType.customer,
      ),
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      taxNumber: json['tax_number'] as String?,
      creditLimit: (json['credit_limit'] as num?)?.toDouble() ?? 0.0,
      currentBalance: (json['current_balance'] as num?)?.toDouble() ?? 0.0,
      isActive: json['is_active'] as bool? ?? true,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      createdBy: json['created_by'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'email': email,
      'phone': phone,
      'address': address,
      'tax_number': taxNumber,
      'credit_limit': creditLimit,
      'current_balance': currentBalance,
      'is_active': isActive,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'created_by': createdBy,
    };
  }

  String get typeLabel {
    switch (type) {
      case PartnerType.customer:
        return 'Khách hàng';
      case PartnerType.supplier:
        return 'Nhà cung cấp';
      case PartnerType.both:
        return 'Khách hàng & NCC';
    }
  }

  String get fullAddress {
    return address ?? '';
  }

  String get displayCode => id.substring(0, 8).toUpperCase();

  bool get hasDebt => currentBalance > 0;

  bool get isOverCreditLimit => currentBalance > creditLimit && creditLimit > 0;

  double get availableCredit =>
      creditLimit > 0 ? creditLimit - currentBalance : 0;

  Partner copyWith({
    String? id,
    String? name,
    PartnerType? type,
    String? email,
    String? phone,
    String? address,
    String? taxNumber,
    double? creditLimit,
    double? currentBalance,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return Partner(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      taxNumber: taxNumber ?? this.taxNumber,
      creditLimit: creditLimit ?? this.creditLimit,
      currentBalance: currentBalance ?? this.currentBalance,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  @override
  String toString() {
    return 'Partner(id: $id, name: $name, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Partner && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods for search
  bool matchesSearch(String query) {
    if (query.isEmpty) return true;
    final lowerQuery = query.toLowerCase();
    return name.toLowerCase().contains(lowerQuery) ||
        (phone?.contains(query) ?? false) ||
        (email?.toLowerCase().contains(lowerQuery) ?? false) ||
        (taxNumber?.contains(query) ?? false);
  }

  bool matchesType(PartnerType? filterType) {
    if (filterType == null) return true;
    return type == filterType || type == PartnerType.both;
  }

  List<String> get searchKeywords {
    final keywords = <String>[];
    keywords.add(name.toLowerCase());
    if (phone != null) keywords.add(phone!);
    if (email != null) keywords.add(email!.toLowerCase());
    if (taxNumber != null) keywords.add(taxNumber!);
    return keywords;
  }

  // Credit limit related methods
  bool get hasCreditLimit => creditLimit > 0;

  bool canMakePurchase(double amount) {
    if (!hasCreditLimit) return true;
    return (currentBalance + amount) <= creditLimit;
  }

  double get remainingCredit {
    if (!hasCreditLimit) return double.infinity;
    return creditLimit - currentBalance;
  }

  String get formattedCreditLimit => '${creditLimit.toStringAsFixed(0)}đ';
  String get formattedCurrentBalance => '${currentBalance.toStringAsFixed(0)}đ';
  String get formattedRemainingCredit {
    if (!hasCreditLimit) return 'Không giới hạn';
    return '${remainingCredit.toStringAsFixed(0)}đ';
  }
}
