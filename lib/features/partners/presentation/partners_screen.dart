import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../data/services/partner_service.dart';
import '../domain/entities/partner.dart';

final partnersProvider = StateNotifierProvider<PartnersNotifier, PartnersState>(
  (ref) {
    return PartnersNotifier();
  },
);

class PartnersState {
  final bool isLoading;
  final String? error;
  final List<Partner> partners;
  final PartnerType? selectedType;
  final String searchQuery;
  final bool? isActiveFilter;
  final Map<String, dynamic> stats;

  const PartnersState({
    this.isLoading = false,
    this.error,
    this.partners = const [],
    this.selectedType,
    this.searchQuery = '',
    this.isActiveFilter,
    this.stats = const {},
  });

  PartnersState copyWith({
    bool? isLoading,
    String? error,
    List<Partner>? partners,
    PartnerType? selectedType,
    String? searchQuery,
    bool? isActiveFilter,
    Map<String, dynamic>? stats,
  }) {
    return PartnersState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      partners: partners ?? this.partners,
      selectedType: selectedType ?? this.selectedType,
      searchQuery: searchQuery ?? this.searchQuery,
      isActiveFilter: isActiveFilter ?? this.isActiveFilter,
      stats: stats ?? this.stats,
    );
  }
}

class PartnersNotifier extends StateNotifier<PartnersState> {
  PartnersNotifier() : super(const PartnersState()) {
    loadPartners();
    loadStats();
  }

  Future<void> loadPartners() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final partners = await PartnerService.getPartners(
        type: state.selectedType,
        searchQuery: state.searchQuery.isNotEmpty ? state.searchQuery : null,
        isActive: state.isActiveFilter,
      );

      state = state.copyWith(isLoading: false, partners: partners);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> loadStats() async {
    try {
      final stats = await PartnerService.getPartnerStats();
      state = state.copyWith(stats: stats);
    } catch (e) {
      // Stats loading error doesn't affect main UI
    }
  }

  void setFilters({PartnerType? type, String? searchQuery, bool? isActive}) {
    state = state.copyWith(
      selectedType: type,
      searchQuery: searchQuery ?? state.searchQuery,
      isActiveFilter: isActive,
    );
    loadPartners();
  }

  void searchPartners(String query) {
    state = state.copyWith(searchQuery: query);
    loadPartners();
  }

  Future<void> createPartner(Partner partner) async {
    try {
      await PartnerService.createPartner(partner);
      await loadPartners();
      await loadStats();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> updatePartner(String id, Partner partner) async {
    try {
      await PartnerService.updatePartner(id, partner);
      await loadPartners();
      await loadStats();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deletePartner(String id) async {
    try {
      await PartnerService.deletePartner(id);
      await loadPartners();
      await loadStats();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }
}

class PartnersScreen extends ConsumerWidget {
  const PartnersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(partnersProvider);
    final responsive = Responsive(context);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: Text(l10n.partners),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddPartnerDialog(context, ref),
            tooltip: l10n.addPartnerTooltip,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(partnersProvider.notifier).loadPartners(),
            tooltip: l10n.refreshTooltip,
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
          ? AppErrorWidget(
              message: state.error!,
              onRetry: () => ref.read(partnersProvider.notifier).loadPartners(),
            )
          : _buildContent(context, ref, state, responsive),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    PartnersState state,
    Responsive responsive,
  ) {
    if (responsive.isMobile) {
      return _buildMobileLayout(context, ref, state);
    } else {
      return _buildDesktopLayout(context, ref, state, responsive);
    }
  }

  Widget _buildMobileLayout(
    BuildContext context,
    WidgetRef ref,
    PartnersState state,
  ) {
    return Column(
      children: [
        // Stats Cards
        _buildStatsCards(context, state),

        // Filters
        _buildFilters(context, ref, state),

        // Partners List
        Expanded(child: _buildPartnersList(context, ref, state)),
      ],
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    WidgetRef ref,
    PartnersState state,
    Responsive responsive,
  ) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(flex: 1, child: _buildStatsCards(context, state)),
              const SizedBox(width: 24),
              Expanded(flex: 2, child: _buildFilters(context, ref, state)),
            ],
          ),
          const SizedBox(height: 24),
          Expanded(child: _buildPartnersList(context, ref, state)),
        ],
      ),
    );
  }

  Widget _buildFilters(
    BuildContext context,
    WidgetRef ref,
    PartnersState state,
  ) {
    final l10n = AppLocalizations.of(context);
    return AppCard(
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: l10n.searchPartners,
                    prefixIcon: const Icon(Icons.search),
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    ref.read(partnersProvider.notifier).searchPartners(value);
                  },
                ),
              ),
              const SizedBox(width: 16),
              DropdownButton<PartnerType?>(
                value: state.selectedType,
                items: [
                  DropdownMenuItem(value: null, child: Text(l10n.all)),
                  DropdownMenuItem(
                    value: PartnerType.customer,
                    child: Text(l10n.customer),
                  ),
                  DropdownMenuItem(
                    value: PartnerType.supplier,
                    child: Text(l10n.supplier),
                  ),
                ],
                onChanged: (value) {
                  ref.read(partnersProvider.notifier).setFilters(type: value);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showAddPartnerDialog(BuildContext context, WidgetRef ref) {
    // Navigate to AddPartnerScreen
    context.go(AppRoutes.addPartner);
  }

  void _showEditPartnerDialog(
    BuildContext context,
    WidgetRef ref,
    Partner partner,
  ) {
    // Navigate to PartnerDetailScreen for editing
    context.go('${AppRoutes.partners}/${partner.id}');
  }

  void _confirmDelete(BuildContext context, WidgetRef ref, Partner partner) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deletePartner),
        content: Text(l10n.deletePartnerConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(partnersProvider.notifier).deletePartner(partner.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCards(BuildContext context, PartnersState state) {
    final l10n = AppLocalizations.of(context);
    return Row(
      children: [
        Expanded(
          child: AppCard(
            child: Column(
              children: [
                Text(
                  l10n.totalPartners,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  '${state.partners.length}',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: AppCard(
            child: Column(
              children: [
                Text(
                  l10n.customersLabel,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  '${state.partners.where((p) => p.type == PartnerType.customer).length}',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: AppCard(
            child: Column(
              children: [
                Text(
                  l10n.suppliersLabel,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  '${state.partners.where((p) => p.type == PartnerType.supplier).length}',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPartnersList(
    BuildContext context,
    WidgetRef ref,
    PartnersState state,
  ) {
    final l10n = AppLocalizations.of(context);
    final filteredPartners = state.partners.where((partner) {
      final matchesSearch =
          state.searchQuery.isEmpty ||
          partner.name.toLowerCase().contains(
            state.searchQuery.toLowerCase(),
          ) ||
          (partner.email?.toLowerCase().contains(
                state.searchQuery.toLowerCase(),
              ) ??
              false) ||
          (partner.phone?.toLowerCase().contains(
                state.searchQuery.toLowerCase(),
              ) ??
              false);

      final matchesType =
          state.selectedType == null || partner.type == state.selectedType;

      return matchesSearch && matchesType;
    }).toList();

    if (filteredPartners.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.people_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              l10n.noPartnersFound,
              style: const TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredPartners.length,
      itemBuilder: (context, index) {
        final partner = filteredPartners[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: partner.type == PartnerType.customer
                  ? Colors.green
                  : Colors.blue,
              child: Icon(
                partner.type == PartnerType.customer
                    ? Icons.person
                    : Icons.business,
                color: Colors.white,
              ),
            ),
            title: Text(partner.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(partner.email ?? ''),
                Text(partner.phone ?? ''),
                Text(
                  partner.type == PartnerType.customer
                      ? l10n.customer
                      : l10n.supplier,
                  style: TextStyle(
                    color: partner.type == PartnerType.customer
                        ? Colors.green
                        : Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () =>
                      _showEditPartnerDialog(context, ref, partner),
                  tooltip: l10n.editTooltip,
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _confirmDelete(context, ref, partner),
                  tooltip: l10n.deleteTooltip,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
