import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../data/services/partner_service.dart';
import '../domain/entities/partner.dart';

final partnersProvider = StateNotifierProvider<PartnersNotifier, PartnersState>(
  (ref) {
    return PartnersNotifier();
  },
);

class PartnersState {
  final bool isLoading;
  final String? error;
  final List<Partner> partners;
  final PartnerType? selectedType;
  final String searchQuery;
  final bool? isActiveFilter;
  final Map<String, dynamic> stats;

  const PartnersState({
    this.isLoading = false,
    this.error,
    this.partners = const [],
    this.selectedType,
    this.searchQuery = '',
    this.isActiveFilter,
    this.stats = const {},
  });

  PartnersState copyWith({
    bool? isLoading,
    String? error,
    List<Partner>? partners,
    PartnerType? selectedType,
    String? searchQuery,
    bool? isActiveFilter,
    Map<String, dynamic>? stats,
  }) {
    return PartnersState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      partners: partners ?? this.partners,
      selectedType: selectedType ?? this.selectedType,
      searchQuery: searchQuery ?? this.searchQuery,
      isActiveFilter: isActiveFilter ?? this.isActiveFilter,
      stats: stats ?? this.stats,
    );
  }
}

class PartnersNotifier extends StateNotifier<PartnersState> {
  PartnersNotifier() : super(const PartnersState()) {
    loadPartners();
    loadStats();
  }

  Future<void> loadPartners() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final partners = await PartnerService.getPartners(
        type: state.selectedType,
        searchQuery: state.searchQuery.isNotEmpty ? state.searchQuery : null,
        isActive: state.isActiveFilter,
      );

      state = state.copyWith(isLoading: false, partners: partners);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> loadStats() async {
    try {
      final stats = await PartnerService.getPartnerStats();
      state = state.copyWith(stats: stats);
    } catch (e) {
      // Stats loading error doesn't affect main UI
    }
  }

  void setFilters({PartnerType? type, String? searchQuery, bool? isActive}) {
    state = state.copyWith(
      selectedType: type,
      searchQuery: searchQuery ?? state.searchQuery,
      isActiveFilter: isActive,
    );
    loadPartners();
  }

  void searchPartners(String query) {
    state = state.copyWith(searchQuery: query);
    loadPartners();
  }

  Future<void> createPartner(Partner partner) async {
    try {
      await PartnerService.createPartner(partner);
      await loadPartners();
      await loadStats();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> updatePartner(String id, Partner partner) async {
    try {
      await PartnerService.updatePartner(id, partner);
      await loadPartners();
      await loadStats();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deletePartner(String id) async {
    try {
      await PartnerService.deletePartner(id);
      await loadPartners();
      await loadStats();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }
}

class PartnersScreen extends ConsumerWidget {
  const PartnersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(partnersProvider);
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: const Text('Đối tác'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddPartnerDialog(context, ref),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(partnersProvider.notifier).loadPartners(),
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
          ? AppErrorWidget(
              message: state.error!,
              onRetry: () => ref.read(partnersProvider.notifier).loadPartners(),
            )
          : _buildContent(context, ref, state, responsive),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    PartnersState state,
    Responsive responsive,
  ) {
    if (responsive.isMobile) {
      return _buildMobileLayout(context, ref, state);
    } else {
      return _buildDesktopLayout(context, ref, state, responsive);
    }
  }

  Widget _buildMobileLayout(
    BuildContext context,
    WidgetRef ref,
    PartnersState state,
  ) {
    return Column(
      children: [
        // Stats Cards
        _buildStatsCards(context, state),

        // Filters
        _buildFilters(context, ref, state),

        // Partners List
        Expanded(child: _buildPartnersList(context, ref, state)),
      ],
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    WidgetRef ref,
    PartnersState state,
    Responsive responsive,
  ) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(flex: 1, child: _buildStatsCards(context, state)),
              const SizedBox(width: 24),
              Expanded(flex: 2, child: _buildFilters(context, ref, state)),
            ],
          ),
          const SizedBox(height: 24),
          Expanded(child: _buildPartnersList(context, ref, state)),
        ],
      ),
    );
  }

  Widget _buildFilters(
    BuildContext context,
    WidgetRef ref,
    PartnersState state,
  ) {
    return AppCard(
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'Tìm kiếm đối tác...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    ref.read(partnersProvider.notifier).searchPartners(value);
                  },
                ),
              ),
              const SizedBox(width: 16),
              DropdownButton<PartnerType?>(
                value: state.selectedType,
                items: const [
                  DropdownMenuItem(value: null, child: Text('Tất cả')),
                  DropdownMenuItem(
                    value: PartnerType.customer,
                    child: Text('Khách hàng'),
                  ),
                  DropdownMenuItem(
                    value: PartnerType.supplier,
                    child: Text('Nhà cung cấp'),
                  ),
                ],
                onChanged: (value) {
                  ref.read(partnersProvider.notifier).setFilters(type: value);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showAddPartnerDialog(BuildContext context, WidgetRef ref) {
    // Navigate to AddPartnerScreen
    context.go(AppRoutes.addPartner);
  }

  void _showEditPartnerDialog(
    BuildContext context,
    WidgetRef ref,
    Partner partner,
  ) {
    // Navigate to PartnerDetailScreen for editing
    context.go('${AppRoutes.partners}/${partner.id}');
  }

  void _confirmDelete(BuildContext context, WidgetRef ref, Partner partner) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text('Bạn có chắc chắn muốn xóa đối tác "${partner.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(partnersProvider.notifier).deletePartner(partner.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCards(BuildContext context, PartnersState state) {
    return Row(
      children: [
        Expanded(
          child: AppCard(
            child: Column(
              children: [
                Text(
                  'Tổng đối tác',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  '${state.partners.length}',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: AppCard(
            child: Column(
              children: [
                Text(
                  'Khách hàng',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  '${state.partners.where((p) => p.type == PartnerType.customer).length}',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: AppCard(
            child: Column(
              children: [
                Text(
                  'Nhà cung cấp',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  '${state.partners.where((p) => p.type == PartnerType.supplier).length}',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPartnersList(
    BuildContext context,
    WidgetRef ref,
    PartnersState state,
  ) {
    final filteredPartners = state.partners.where((partner) {
      final matchesSearch =
          state.searchQuery.isEmpty ||
          partner.name.toLowerCase().contains(
            state.searchQuery.toLowerCase(),
          ) ||
          (partner.email?.toLowerCase().contains(
                state.searchQuery.toLowerCase(),
              ) ??
              false) ||
          (partner.phone?.toLowerCase().contains(
                state.searchQuery.toLowerCase(),
              ) ??
              false);

      final matchesType =
          state.selectedType == null || partner.type == state.selectedType;

      return matchesSearch && matchesType;
    }).toList();

    if (filteredPartners.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Không có đối tác nào',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredPartners.length,
      itemBuilder: (context, index) {
        final partner = filteredPartners[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: partner.type == PartnerType.customer
                  ? Colors.green
                  : Colors.blue,
              child: Icon(
                partner.type == PartnerType.customer
                    ? Icons.person
                    : Icons.business,
                color: Colors.white,
              ),
            ),
            title: Text(partner.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(partner.email ?? ''),
                Text(partner.phone ?? ''),
                Text(
                  partner.type == PartnerType.customer
                      ? 'Khách hàng'
                      : 'Nhà cung cấp',
                  style: TextStyle(
                    color: partner.type == PartnerType.customer
                        ? Colors.green
                        : Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () =>
                      _showEditPartnerDialog(context, ref, partner),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _confirmDelete(context, ref, partner),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
