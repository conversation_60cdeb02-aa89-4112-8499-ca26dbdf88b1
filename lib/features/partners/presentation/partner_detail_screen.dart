import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../data/services/partner_service.dart';
import '../domain/entities/partner.dart';

class PartnerDetailScreen extends ConsumerStatefulWidget {
  final String partnerId;

  const PartnerDetailScreen({super.key, required this.partnerId});

  @override
  ConsumerState<PartnerDetailScreen> createState() =>
      _PartnerDetailScreenState();
}

class _PartnerDetailScreenState extends ConsumerState<PartnerDetailScreen> {
  Partner? _partner;
  bool _isLoading = true;
  bool _isEditing = false;
  bool _isSaving = false;
  String? _error;

  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadPartner();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadPartner() async {
    print('🔍 Loading partner with ID: ${widget.partnerId}');
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final partner = await PartnerService.getPartnerById(widget.partnerId);
      print('📦 Partner loaded: ${partner?.name ?? 'null'}');
      if (mounted) {
        setState(() {
          _partner = partner;
          _nameController.text = partner?.name ?? '';
          _phoneController.text = partner?.phone ?? '';
          _emailController.text = partner?.email ?? '';
          _addressController.text = partner?.address ?? '';
          _notesController.text = partner?.notes ?? '';
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Error loading partner: $e');
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive(context);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(_partner?.name ?? l10n.partnerDetails),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.partners),
        ),
        actions: [
          if (_partner != null && !_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
              tooltip: l10n.editTooltip,
            ),
          if (_isEditing)
            TextButton(
              onPressed: _isSaving ? null : _saveChanges,
              child: _isSaving
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(l10n.save),
            ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _showDeleteConfirmation();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    const Icon(Icons.delete, color: Colors.red),
                    const SizedBox(width: 8),
                    Text(l10n.deletePartner),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildBody(responsive),
    );
  }

  Widget _buildBody(Responsive responsive) {
    final l10n = AppLocalizations.of(context);
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppTheme.errorColor),
            const SizedBox(height: 16),
            Text(
              l10n.errorOccurred,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPartner,
              child: Text(l10n.tryAgain),
            ),
          ],
        ),
      );
    }

    if (_partner == null) {
      return Center(child: Text(l10n.partnerNotFound));
    }

    return responsive.isMobile ? _buildMobileLayout() : _buildDesktopLayout();
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildPartnerInfo(),
          const SizedBox(height: 16),
          _buildContactInfo(),
          const SizedBox(height: 16),
          _buildStatistics(),
          if (_isEditing) ...[
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1000),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        _buildPartnerInfo(),
                        const SizedBox(height: 16),
                        _buildContactInfo(),
                      ],
                    ),
                  ),
                  const SizedBox(width: 24),
                  Expanded(child: _buildStatistics()),
                ],
              ),
              if (_isEditing) ...[
                const SizedBox(height: 32),
                Center(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: _buildActionButtons(),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPartnerInfo() {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    final l10n = AppLocalizations.of(context);

    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.partnerInfo,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_isEditing)
              TextField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: l10n.partnerName,
                  border: const OutlineInputBorder(),
                ),
              )
            else
              _buildInfoRow(l10n.partnerName, _partner!.name),
            const SizedBox(height: 12),
            _buildInfoRow(
              l10n.partnerType,
              _partner!.type == PartnerType.customer
                  ? l10n.customer
                  : l10n.supplier,
              valueColor: _partner!.type == PartnerType.customer
                  ? Colors.blue
                  : Colors.orange,
            ),
            _buildInfoRow(l10n.createdDate, dateFormat.format(_partner!.createdAt)),
            const SizedBox(height: 16),
            Text(
              l10n.notes,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            if (_isEditing)
              TextField(
                controller: _notesController,
                decoration: InputDecoration(
                  hintText: l10n.enterNotes,
                  border: const OutlineInputBorder(),
                ),
                maxLines: 3,
              )
            else
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  _partner!.notes?.isNotEmpty == true
                      ? _partner!.notes!
                      : l10n.noNotes,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: _partner!.notes?.isNotEmpty == true
                        ? null
                        : Colors.grey[600],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfo() {
    final l10n = AppLocalizations.of(context);
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.contactInfo,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_isEditing) ...[
              TextField(
                controller: _phoneController,
                decoration: InputDecoration(
                  labelText: l10n.phoneNumber,
                  border: const OutlineInputBorder(),
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _emailController,
                decoration: InputDecoration(
                  labelText: l10n.email,
                  border: const OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _addressController,
                decoration: InputDecoration(
                  labelText: l10n.address,
                  border: const OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ] else ...[
              if (_partner!.phone?.isNotEmpty == true)
                _buildInfoRow(l10n.phoneNumber, _partner!.phone!)
              else
                _buildInfoRow(
                  l10n.phoneNumber,
                  l10n.notAvailable,
                  valueColor: Colors.grey,
                ),
              if (_partner!.email?.isNotEmpty == true)
                _buildInfoRow(l10n.email, _partner!.email!)
              else
                _buildInfoRow(l10n.email, l10n.notAvailable, valueColor: Colors.grey),
              if (_partner!.address?.isNotEmpty == true)
                _buildInfoRow(l10n.address, _partner!.address!)
              else
                _buildInfoRow(l10n.address, l10n.notAvailable, valueColor: Colors.grey),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatistics() {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final l10n = AppLocalizations.of(context);

    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.statistics,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildStatRow(
              l10n.creditLimit,
              currencyFormat.format(_partner!.creditLimit),
            ),
            _buildStatRow(
              l10n.currentBalance,
              currencyFormat.format(_partner!.currentBalance),
            ),
            _buildStatRow(
              l10n.status,
              _partner!.isActive ? l10n.active : l10n.inactive,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: valueColor,
                fontWeight: valueColor != null ? FontWeight.w500 : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    final l10n = AppLocalizations.of(context);
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isSaving
                ? null
                : () {
                    setState(() {
                      _isEditing = false;
                      _nameController.text = _partner!.name;
                      _phoneController.text = _partner!.phone ?? '';
                      _emailController.text = _partner!.email ?? '';
                      _addressController.text = _partner!.address ?? '';
                      _notesController.text = _partner!.notes ?? '';
                    });
                  },
            child: Text(l10n.cancel),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isSaving ? null : _saveChanges,
            child: Text(l10n.save),
          ),
        ),
      ],
    );
  }

  Future<void> _saveChanges() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final updatedPartner = _partner!.copyWith(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty
            ? null
            : _addressController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      await PartnerService.updatePartner(_partner!.id, updatedPartner);

      if (mounted) {
        setState(() {
          _partner = updatedPartner;
          _isEditing = false;
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã cập nhật đối tác thành công'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi cập nhật đối tác: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _showDeleteConfirmation() {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deletePartner),
        content: Text(l10n.deletePartnerConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deletePartner();
            },
            child: Text(l10n.delete),
          ),
        ],
      ),
    );
  }

  Future<void> _deletePartner() async {
    try {
      await PartnerService.deletePartner(_partner!.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã xóa đối tác thành công'),
            backgroundColor: AppTheme.successColor,
          ),
        );
        context.go(AppRoutes.partners);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi xóa đối tác: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }
}
