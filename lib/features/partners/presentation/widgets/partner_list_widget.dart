import 'package:flutter/material.dart';

import '../../../../core/widgets/app_card.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../../data/models/partner.dart';

class PartnerListWidget extends StatelessWidget {
  final List<Partner> partners;
  final Function(Partner) onEdit;
  final Function(Partner) onDelete;

  const PartnerListWidget({
    super.key,
    required this.partners,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                l10n.partnerList,
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                l10n.partnerCount(partners.length),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (partners.isEmpty)
            _buildEmptyState(context)
          else
            Expanded(child: _buildPartnerList(context)),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 48,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            l10n.noPartnersFound,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.addFirstPartner,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPartnerList(BuildContext context) {
    return ListView.separated(
      itemCount: partners.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final partner = partners[index];
        return _buildPartnerItem(context, partner);
      },
    );
  }

  Widget _buildPartnerItem(BuildContext context, Partner partner) {
    final l10n = AppLocalizations.of(context);
    final isCustomer = partner.type == 'customer';
    final icon = isCustomer ? Icons.person : Icons.business;
    final color = isCustomer ? Colors.blue : Colors.green;

    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(icon, color: color, size: 20),
      ),
      title: Text(
        partner.name,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isCustomer ? l10n.customer : l10n.supplier,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (partner.email != null) ...[
            const SizedBox(height: 2),
            Text(
              partner.email!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.withValues(alpha: 0.7),
              ),
            ),
          ],
          if (partner.phone != null) ...[
            const SizedBox(height: 2),
            Text(
              partner.phone!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.withValues(alpha: 0.7),
              ),
            ),
          ],
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: partner.isActive
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              partner.isActive ? l10n.active : l10n.inactive,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: partner.isActive ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  onEdit(partner);
                  break;
                case 'delete':
                  onDelete(partner);
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    const Icon(Icons.edit, size: 16),
                    const SizedBox(width: 8),
                    Text(l10n.edit),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    const Icon(Icons.delete, size: 16, color: Colors.red),
                    const SizedBox(width: 8),
                    Text(l10n.delete, style: const TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      onTap: () => _showPartnerDetails(context, partner),
    );
  }

  void _showPartnerDetails(BuildContext context, Partner partner) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.partnerDetails),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow(l10n.nameLabel, partner.name),
            _buildDetailRow(
              l10n.typeLabel,
              partner.type == 'customer' ? l10n.customer : l10n.supplier,
            ),
            if (partner.email != null)
              _buildDetailRow(l10n.emailLabel, partner.email!),
            if (partner.phone != null)
              _buildDetailRow(l10n.phoneLabel, partner.phone!),
            if (partner.address != null)
              _buildDetailRow(l10n.addressLabel, partner.address!),
            if (partner.taxNumber != null)
              _buildDetailRow(l10n.taxNumberLabel, partner.taxNumber!),
            _buildDetailRow(
              l10n.statusLabel,
              partner.isActive ? l10n.active : l10n.inactive,
            ),
            if (partner.notes != null)
              _buildDetailRow(l10n.notesLabel, partner.notes!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.close),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onEdit(partner);
            },
            child: Text(l10n.edit),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
