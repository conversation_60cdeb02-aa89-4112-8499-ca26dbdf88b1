import 'package:flutter/material.dart';

import '../../../../core/widgets/app_card.dart';
import '../../domain/entities/partner.dart';

class PartnerFiltersWidget extends StatelessWidget {
  final PartnerType? selectedType;
  final String searchQuery;
  final bool? isActiveFilter;
  final Function(PartnerType?, String?, bool?) onFiltersChanged;
  final Function(String) onSearchChanged;

  const PartnerFiltersWidget({
    super.key,
    this.selectedType,
    required this.searchQuery,
    this.isActiveFilter,
    required this.onFiltersChanged,
    required this.onSearchChanged,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tìm kiếm và lọc',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Search Field
            TextField(
              decoration: const InputDecoration(
                labelText: 'Tìm kiếm đối tác',
                hintText: 'Nhập tên, mã, số điện thoại, email...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: onSearchChanged,
            ),
            
            const SizedBox(height: 16),
            
            // Partner Type Filter
            _buildTypeFilter(context),
            
            const SizedBox(height: 16),
            
            // Active Status Filter
            _buildActiveStatusFilter(context),
            
            const SizedBox(height: 16),
            
            // Clear Filters Button
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      onFiltersChanged(null, null, null);
                      onSearchChanged('');
                    },
                    child: const Text('Xóa bộ lọc'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeFilter(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loại đối tác',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildTypeChip(
              context,
              'Tất cả',
              null,
              selectedType == null,
            ),
            _buildTypeChip(
              context,
              'Khách hàng',
              PartnerType.customer,
              selectedType == PartnerType.customer,
            ),
            _buildTypeChip(
              context,
              'Nhà cung cấp',
              PartnerType.supplier,
              selectedType == PartnerType.supplier,
            ),
            _buildTypeChip(
              context,
              'Cả hai',
              PartnerType.both,
              selectedType == PartnerType.both,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTypeChip(
    BuildContext context,
    String label,
    PartnerType? type,
    bool isSelected,
  ) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          onFiltersChanged(type, searchQuery, isActiveFilter);
        }
      },
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  Widget _buildActiveStatusFilter(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Trạng thái',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildStatusChip(
              context,
              'Tất cả',
              null,
              isActiveFilter == null,
            ),
            _buildStatusChip(
              context,
              'Hoạt động',
              true,
              isActiveFilter == true,
            ),
            _buildStatusChip(
              context,
              'Ngưng hoạt động',
              false,
              isActiveFilter == false,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusChip(
    BuildContext context,
    String label,
    bool? status,
    bool isSelected,
  ) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          onFiltersChanged(selectedType, searchQuery, status);
        }
      },
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }
}
