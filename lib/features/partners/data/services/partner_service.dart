import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/config/supabase_config.dart';
import '../../domain/entities/partner.dart';

class PartnerService {
  static final SupabaseClient? _supabase = SupabaseConfig.client;

  // Get all partners
  static Future<List<Partner>> getPartners({
    PartnerType? type,
    String? searchQuery,
    bool? isActive,
    int? limit,
    int? offset,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoPartners()
          .where((partner) {
            if (type != null && !partner.matchesType(type)) return false;
            if (searchQuery != null && !partner.matchesSearch(searchQuery))
              return false;
            if (isActive != null && partner.isActive != isActive) return false;
            return true;
          })
          .skip(offset ?? 0)
          .take(limit ?? 50)
          .toList();
    }

    try {
      print('🔥 Loading partners from Supabase...');
      print('🔥 Filters: type=$type, isActive=$isActive, search=$searchQuery');

      var queryBuilder = _supabase!.from('partners').select();

      if (type != null) {
        queryBuilder = queryBuilder.eq('type', type.name);
      }

      if (isActive != null) {
        queryBuilder = queryBuilder.eq('is_active', isActive);
      }

      var orderedQuery = queryBuilder.order('created_at', ascending: false);

      if (offset != null) {
        orderedQuery = orderedQuery.range(offset, (offset + (limit ?? 50)) - 1);
      } else if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      final response = await orderedQuery;

      List<Partner> partners = (response as List)
          .map((json) => Partner.fromJson(json))
          .toList();

      print('🔥 Loaded ${partners.length} partners from Supabase');

      // Apply search filter in memory if needed
      if (searchQuery != null && searchQuery.isNotEmpty) {
        partners = partners
            .where((partner) => partner.matchesSearch(searchQuery))
            .toList();
        print('🔥 After search filter: ${partners.length} partners');
      }

      return partners;
    } catch (e) {
      print('Error getting partners: $e');
      return [];
    }
  }

  // Get partner by ID
  static Future<Partner?> getPartnerById(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      try {
        return _getDemoPartners().firstWhere((partner) => partner.id == id);
      } catch (e) {
        return null;
      }
    }

    try {
      final response = await _supabase!
          .from('partners')
          .select()
          .eq('id', id)
          .single();

      return Partner.fromJson(response);
    } catch (e) {
      print('Error getting partner by ID: $e');
      return null;
    }
  }

  // Create new partner
  static Future<Partner> createPartner(Partner partner) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate creation
      await Future.delayed(const Duration(milliseconds: 500));
      return partner.copyWith(
        id: 'partner_${DateTime.now().millisecondsSinceEpoch}',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }

    try {
      print('🔥 Creating partner in Supabase...');
      print('🔥 Partner name: ${partner.name}');
      print('🔥 Partner type: ${partner.type.name}');

      final insertData = partner.toJson();
      insertData.remove('id'); // Let database generate ID

      print('🔥 Insert data: $insertData');

      final response = await _supabase!
          .from('partners')
          .insert(insertData)
          .select()
          .single();

      print('🔥 Partner created successfully: ${response['id']}');

      return Partner.fromJson(response);
    } catch (e) {
      print('Error creating partner: $e');
      throw Exception('Failed to create partner: $e');
    }
  }

  // Update partner
  static Future<Partner> updatePartner(String id, Partner partner) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate update
      await Future.delayed(const Duration(milliseconds: 500));
      return partner.copyWith(id: id, updatedAt: DateTime.now());
    }

    try {
      final updateData = partner.toJson();
      updateData.remove('id'); // Don't update ID
      updateData.remove('created_at'); // Don't update created_at

      final response = await _supabase!
          .from('partners')
          .update(updateData)
          .eq('id', id)
          .select()
          .single();

      return Partner.fromJson(response);
    } catch (e) {
      print('Error updating partner: $e');
      throw Exception('Failed to update partner: $e');
    }
  }

  // Delete partner
  static Future<void> deletePartner(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate deletion
      await Future.delayed(const Duration(milliseconds: 500));
      return;
    }

    try {
      await _supabase!.from('partners').delete().eq('id', id);
    } catch (e) {
      print('Error deleting partner: $e');
      throw Exception('Failed to delete partner: $e');
    }
  }

  // Get partner statistics
  static Future<Map<String, dynamic>> getPartnerStats() async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      final partners = await getPartners();
      final customers = partners.where(
        (p) => p.type == PartnerType.customer || p.type == PartnerType.both,
      );
      final suppliers = partners.where(
        (p) => p.type == PartnerType.supplier || p.type == PartnerType.both,
      );
      final activePartners = partners.where((p) => p.isActive);
      final partnersWithDebt = partners.where((p) => p.hasDebt);

      return {
        'totalPartners': partners.length,
        'totalCustomers': customers.length,
        'totalSuppliers': suppliers.length,
        'activePartners': activePartners.length,
        'partnersWithDebt': partnersWithDebt.length,
        'totalDebt': partners.fold<double>(
          0,
          (sum, p) => sum + p.currentBalance,
        ),
        'totalCreditLimit': partners.fold<double>(
          0,
          (sum, p) => sum + p.creditLimit,
        ),
      };
    }

    // Real implementation would go here
    return {};
  }

  // Demo data methods
  static List<Partner> _getDemoPartners() {
    // Demo mode is disabled, return empty list
    return [];
  }
}
