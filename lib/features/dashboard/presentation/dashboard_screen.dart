import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/routers/app_router.dart';
import '../../../core/services/dashboard_refresh_service.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/formatters.dart';
import '../../../core/utils/responsive.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../data/services/dashboard_service.dart';

// Dashboard data provider with refresh capability
final dashboardDataProvider = FutureProvider.family<Map<String, dynamic>, int>((
  ref,
  refreshKey,
) async {
  try {
    print(
      '🔥 Loading dashboard data using DashboardService... (refresh key: $refreshKey)',
    );

    // Use the dedicated dashboard service for clean and accurate data
    final dashboardData = await DashboardService.getDashboardData();

    print('🔥 Dashboard data loaded: $dashboardData');
    return dashboardData;
  } catch (e) {
    print('🔥 Critical error loading dashboard data: $e');
    return {
      'todayRevenue': 0.0,
      'todayOrders': 0,
      'lowStockCount': 0,
      'totalCustomers': 0,
    };
  }
});

// Use dashboard refresh provider from service

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen>
    with WidgetsBindingObserver {
  Timer? _autoRefreshTimer;
  bool _isFirstLoad = true;

  @override
  void initState() {
    super.initState();

    // Initialize dashboard refresh service
    DashboardRefreshService.initialize(ref);

    // Add observer for app lifecycle changes
    WidgetsBinding.instance.addObserver(this);

    // Auto refresh every 30 seconds
    _autoRefreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      print('🔥 Auto refresh triggered');
      ref.read(dashboardRefreshProvider.notifier).autoRefresh();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Refresh data when returning to dashboard (except first load)
    if (!_isFirstLoad) {
      _refreshDataWithNotification('Quay về màn hình Dashboard');
    }
    _isFirstLoad = false;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        // App resumed from background - refresh data
        _refreshDataWithNotification('Mở lại ứng dụng');
        break;
      case AppLifecycleState.paused:
        print('🔥 App paused');
        break;
      case AppLifecycleState.inactive:
        print('🔥 App inactive');
        break;
      case AppLifecycleState.detached:
        print('🔥 App detached');
        break;
      case AppLifecycleState.hidden:
        print('🔥 App hidden');
        break;
    }
  }

  @override
  void dispose() {
    _autoRefreshTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _onRefresh() async {
    print('🔥 Pull to refresh triggered');
    ref.read(dashboardRefreshProvider.notifier).refresh();
    // Wait a bit for the refresh to complete
    await Future.delayed(const Duration(milliseconds: 500));
  }

  void _showDataRefreshNotification(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.refresh, color: Colors.white, size: 16),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  void _refreshDataWithNotification(String reason) {
    print('🔥 Dashboard refresh: $reason');
    ref.read(dashboardRefreshProvider.notifier).refresh();
    _showDataRefreshNotification('📊 Dữ liệu đã được cập nhật');
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final responsive = Responsive(context);
    final refreshKey = ref.watch(dashboardRefreshProvider);
    final dashboardData = ref.watch(dashboardDataProvider(refreshKey));

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.dashboard),
        actions: [
          // Manual refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _refreshDataWithNotification('Làm mới thủ công');
            },
            tooltip: 'Làm mới dữ liệu',
          ),
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(l10n.notificationsComingSoon)),
              );
            },
          ),
        ],
      ),
      drawer: responsive.isMobile ? const AppDrawer() : null,
      body: Row(
        children: [
          // Sidebar for desktop and tablet
          if (!responsive.isMobile)
            Container(
              width: Responsive.responsiveSidebarWidth(context),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  right: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: const AppDrawer(),
            ),

          // Main content with RefreshIndicator
          Expanded(
            child: RefreshIndicator(
              onRefresh: _onRefresh,
              child: SingleChildScrollView(
                padding: Responsive.responsivePadding(context),
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Section
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(AppTheme.spacingL),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.waving_hand,
                              size: 32,
                              color: AppTheme.warningColor,
                            ),
                            const SizedBox(width: AppTheme.spacingM),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    l10n.welcome,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge
                                        ?.copyWith(
                                          fontSize:
                                              Responsive.responsiveHeadingText(
                                                context,
                                              ),
                                        ),
                                  ),
                                  const SizedBox(height: AppTheme.spacingS),
                                  Text(
                                    '${l10n.today} ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          color: AppTheme.textSecondaryColor,
                                          fontSize:
                                              Responsive.responsiveBodyText(
                                                context,
                                              ),
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingL),

                    // Quick Stats
                    Text(
                      l10n.quickStats,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontSize: Responsive.responsiveHeadingText(context),
                      ),
                    ),
                    SizedBox(height: Responsive.responsiveSpacing(context)),

                    // Stats with real data
                    dashboardData.when(
                      data: (data) => GridView.count(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        crossAxisCount: Responsive.responsiveGridCount(context),
                        crossAxisSpacing: Responsive.responsiveSpacing(context),
                        mainAxisSpacing: Responsive.responsiveSpacing(context),
                        childAspectRatio:
                            MediaQuery.of(context).size.width < 400
                            ? 1.3 // Very small screens - 2 columns
                            : responsive.isMobile
                            ? 1.4 // Mobile - 2 columns balanced
                            : 1.8, // Desktop - wider cards
                        children: [
                          _buildStatCard(
                            context,
                            l10n.todayRevenue,
                            Formatters.formatCurrency(
                              data['todayRevenue'] as double,
                            ),
                            Icons.trending_up,
                            AppTheme.successColor,
                          ),
                          _buildStatCard(
                            context,
                            l10n.todayOrders,
                            '${data['todayOrders']}',
                            Icons.shopping_cart,
                            AppTheme.primaryColor,
                          ),
                          _buildStatCard(
                            context,
                            l10n.lowStockProducts,
                            '${data['lowStockCount']}',
                            Icons.warning,
                            AppTheme.warningColor,
                          ),
                          _buildStatCard(
                            context,
                            l10n.totalCustomers,
                            '${data['totalCustomers']}',
                            Icons.people,
                            AppTheme.infoColor,
                          ),
                        ],
                      ),
                      loading: () =>
                          const Center(child: CircularProgressIndicator()),
                      error: (error, stack) => Center(
                        child: Column(
                          children: [
                            Icon(Icons.error, color: Colors.red, size: 48),
                            const SizedBox(height: 16),
                            Text('Lỗi tải dữ liệu: $error'),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () => _refreshDataWithNotification(
                                'Thử lại sau lỗi',
                              ),
                              child: const Text('Thử lại'),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: Responsive.responsiveSpacing(context) * 1.5,
                    ),

                    // Quick Actions
                    Text(
                      l10n.quickActions,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontSize: Responsive.responsiveHeadingText(context),
                      ),
                    ),
                    SizedBox(height: Responsive.responsiveSpacing(context)),
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: responsive.isMobile
                          ? 3
                          : 4, // Mobile: 3 items/hàng, Desktop: 4 items/hàng
                      crossAxisSpacing: Responsive.responsiveSpacing(context),
                      mainAxisSpacing: Responsive.responsiveSpacing(context),
                      childAspectRatio: 1.0, // Hình vuông cho tất cả
                      children: [
                        _buildActionCard(
                          context,
                          'Bán hàng (POS)',
                          Icons.point_of_sale,
                          () => context.go(AppRoutes.pos),
                        ),
                        _buildActionCard(
                          context,
                          l10n.addProduct,
                          Icons.add_box,
                          () => context.go(AppRoutes.addProduct),
                        ),
                        _buildActionCard(
                          context,
                          l10n.addStock,
                          Icons.inventory,
                          () => context.go(AppRoutes.stockTransactions),
                        ),
                        _buildActionCard(
                          context,
                          l10n.invoices,
                          Icons.receipt_long,
                          () => context.go(AppRoutes.invoices),
                        ),
                        _buildActionCard(
                          context,
                          l10n.reports,
                          Icons.analytics,
                          () => context.go(AppRoutes.reports),
                        ),
                        _buildActionCard(
                          context,
                          l10n.partners,
                          Icons.business,
                          () => context.go(AppRoutes.partners),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final responsive = Responsive(context);

    return Card(
      elevation: Responsive.responsiveElevation(context),
      child: Padding(
        padding: EdgeInsets.all(Responsive.responsiveSpacing(context)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: Responsive.responsiveIconSize(context) * 0.8,
              color: color,
            ),
            SizedBox(height: Responsive.responsiveSpacing(context) * 0.5),
            Flexible(
              child: Text(
                value,
                style: responsive.isMobile
                    ? Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                      )
                    : Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(height: Responsive.responsiveSpacing(context) * 0.25),
            Flexible(
              child: Text(
                title,
                style: responsive.isMobile
                    ? Theme.of(context).textTheme.labelSmall
                    : Theme.of(context).textTheme.labelMedium,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    final responsive = Responsive(context);

    return Card(
      elevation: Responsive.responsiveElevation(context),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(
          Responsive.responsiveBorderRadius(context),
        ),
        child: Container(
          padding: EdgeInsets.all(Responsive.responsiveSpacing(context)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: Responsive.responsiveIconSize(context),
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(height: Responsive.responsiveSpacing(context) * 0.5),
              Flexible(
                child: Text(
                  title,
                  style: responsive.isMobile
                      ? Theme.of(context).textTheme.labelSmall?.copyWith(
                          fontWeight: FontWeight.w500,
                        )
                      : Theme.of(context).textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.store, size: 48, color: Colors.white),
                const SizedBox(height: AppTheme.spacingM),
                Text(
                  AppConstants.appName,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'v${AppConstants.appVersion}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
                ),
              ],
            ),
          ),
          _buildDrawerItem(
            context,
            l10n.dashboard,
            Icons.dashboard,
            () => context.go(AppRoutes.dashboard),
          ),
          _buildDrawerItem(
            context,
            l10n.posMenuTitle,
            Icons.point_of_sale,
            () => context.go(AppRoutes.pos),
          ),
          _buildDrawerItem(
            context,
            l10n.notificationsMenuTitle,
            Icons.notifications,
            () => context.go(AppRoutes.notifications),
          ),
          _buildDrawerItem(
            context,
            l10n.inventory,
            Icons.inventory_2,
            () => context.go(AppRoutes.products),
          ),
          _buildDrawerItem(
            context,
            l10n.stockTransactionsMenuTitle,
            Icons.swap_horiz,
            () => context.go(AppRoutes.stockTransactions),
          ),
          _buildDrawerItem(
            context,
            l10n.invoices,
            Icons.receipt_long,
            () => context.go(AppRoutes.invoices),
          ),
          _buildDrawerItem(
            context,
            l10n.finance,
            Icons.account_balance_wallet,
            () => context.go(AppRoutes.finance),
          ),
          _buildDrawerItem(
            context,
            l10n.partners,
            Icons.business,
            () => context.go(AppRoutes.partners),
          ),
          _buildDrawerItem(
            context,
            l10n.reports,
            Icons.analytics,
            () => context.go(AppRoutes.reports),
          ),
          const Divider(),
          _buildDrawerItem(
            context,
            l10n.settings,
            Icons.settings,
            () => context.go(AppRoutes.settings),
          ),
          _buildDrawerItem(
            context,
            l10n.logout,
            Icons.logout,
            () => context.go(AppRoutes.login),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(leading: Icon(icon), title: Text(title), onTap: onTap);
  }
}
