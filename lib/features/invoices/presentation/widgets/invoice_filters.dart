import 'package:flutter/material.dart';

import '../../../../core/widgets/app_card.dart';

class InvoiceFilters extends StatelessWidget {
  final String selectedType;
  final String selectedStatus;
  final String searchQuery;
  final Function(String?, String?, String?) onFiltersChanged;

  const InvoiceFilters({
    super.key,
    required this.selectedType,
    required this.selectedStatus,
    required this.searchQuery,
    required this.onFiltersChanged,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return AppCard(
      child: Column(
        children: [
          // Use responsive layout based on screen width
          if (screenWidth < 768)
            _buildMobileLayout(context)
          else
            _buildDesktopLayout(context),
        ],
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        // Search field - full width on mobile
        TextField(
          decoration: const InputDecoration(
            hintText: '<PERSON><PERSON><PERSON> kiếm hóa đơn...',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            onFiltersChanged(null, null, value);
          },
        ),
        const SizedBox(height: 12),
        // Filters in row on mobile
        Row(
          children: [
            Expanded(child: _buildTypeFilter(context)),
            const SizedBox(width: 12),
            Expanded(child: _buildStatusFilter(context)),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: TextField(
            decoration: const InputDecoration(
              hintText: 'Tìm kiếm hóa đơn...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              onFiltersChanged(null, null, value);
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(child: _buildTypeFilter(context)),
        const SizedBox(width: 16),
        Expanded(child: _buildStatusFilter(context)),
      ],
    );
  }

  Widget _buildTypeFilter(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selectedType,
      decoration: const InputDecoration(
        labelText: 'Loại hóa đơn',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      style: TextStyle(
        fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
        color: Colors.black87,
      ),
      items: [
        DropdownMenuItem(
          value: 'all',
          child: Text(
            'Tất cả',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'sale',
          child: Text(
            'Bán hàng',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'purchase',
          child: Text(
            'Mua hàng',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'return',
          child: Text(
            'Trả hàng',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
      ],
      onChanged: (value) {
        onFiltersChanged(value, null, null);
      },
    );
  }

  Widget _buildStatusFilter(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selectedStatus,
      decoration: const InputDecoration(
        labelText: 'Trạng thái',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      style: TextStyle(
        fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
        color: Colors.black87,
      ),
      items: [
        DropdownMenuItem(
          value: 'all',
          child: Text(
            'Tất cả',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'draft',
          child: Text(
            'Nháp',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'sent',
          child: Text(
            'Đã gửi',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'paid',
          child: Text(
            'Đã thanh toán',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'overdue',
          child: Text(
            'Quá hạn',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'cancelled',
          child: Text(
            'Đã hủy',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
      ],
      onChanged: (value) {
        onFiltersChanged(null, value, null);
      },
    );
  }
}
