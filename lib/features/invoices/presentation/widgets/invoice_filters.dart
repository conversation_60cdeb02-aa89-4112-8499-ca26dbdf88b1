import 'package:flutter/material.dart';
import 'package:city_pos/generated/l10n/app_localizations.dart';

import '../../../../core/widgets/app_card.dart';

class InvoiceFilters extends StatelessWidget {
  final String selectedType;
  final String selectedStatus;
  final String searchQuery;
  final Function(String?, String?, String?) onFiltersChanged;

  const InvoiceFilters({
    super.key,
    required this.selectedType,
    required this.selectedStatus,
    required this.searchQuery,
    required this.onFiltersChanged,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final l10n = AppLocalizations.of(context);

    return AppCard(
      child: Column(
        children: [
          // Use responsive layout based on screen width
          if (screenWidth < 768)
            _buildMobileLayout(context)
          else
            _buildDesktopLayout(context),
        ],
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Column(
      children: [
        // Search field - full width on mobile
        TextField(
          decoration: InputDecoration(
            hintText: l10n.searchInvoice,
            prefixIcon: const Icon(Icons.search),
            border: const OutlineInputBorder(),
          ),
          onChanged: (value) {
            onFiltersChanged(null, null, value);
          },
        ),
        const SizedBox(height: 12),
        // Filters in row on mobile
        Row(
          children: [
            Expanded(child: _buildTypeFilter(context)),
            const SizedBox(width: 12),
            Expanded(child: _buildStatusFilter(context)),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: TextField(
            decoration: InputDecoration(
              hintText: l10n.searchInvoice,
              prefixIcon: const Icon(Icons.search),
              border: const OutlineInputBorder(),
            ),
            onChanged: (value) {
              onFiltersChanged(null, null, value);
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(child: _buildTypeFilter(context)),
        const SizedBox(width: 16),
        Expanded(child: _buildStatusFilter(context)),
      ],
    );
  }

  Widget _buildTypeFilter(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return DropdownButtonFormField<String>(
      value: selectedType,
      decoration: InputDecoration(
        labelText: l10n.invoiceType,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      style: TextStyle(
        fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
        color: Colors.black87,
      ),
      items: [
        DropdownMenuItem(
          value: 'all',
          child: Text(
            l10n.all,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'sale',
          child: Text(
            l10n.sale,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'purchase',
          child: Text(
            l10n.purchase,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'return',
          child: Text(
            l10n.return_,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
      ],
      onChanged: (value) {
        onFiltersChanged(value, null, null);
      },
    );
  }

  Widget _buildStatusFilter(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return DropdownButtonFormField<String>(
      value: selectedStatus,
      decoration: InputDecoration(
        labelText: l10n.status,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      style: TextStyle(
        fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
        color: Colors.black87,
      ),
      items: [
        DropdownMenuItem(
          value: 'all',
          child: Text(
            l10n.all,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'draft',
          child: Text(
            l10n.draft,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'sent',
          child: Text(
            l10n.sent,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'paid',
          child: Text(
            l10n.paid,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'overdue',
          child: Text(
            l10n.overdue,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'cancelled',
          child: Text(
            l10n.cancelled,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
      ],
      onChanged: (value) {
        onFiltersChanged(null, value, null);
      },
    );
  }
}
