import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/config/supabase_config.dart';
import '../../../invoices/domain/entities/invoice.dart';

class InvoiceService {
  static final SupabaseClient? _supabase = SupabaseConfig.client;

  // Get all invoices
  static Future<List<Invoice>> getInvoices({
    String? type,
    String? status,
    String? partnerId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoInvoices();
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      var queryBuilder = _supabase!.from('invoices').select('''
        id, invoice_number, order_id, partner_id, issue_date, due_date,
        status, subtotal, tax_amount, discount_amount, total_amount, paid_amount,
        notes, created_at, updated_at, created_by,
        partners(id, name, type, email, phone)
      ''');

      // Apply filters
      // Note: type filter removed as column doesn't exist yet

      if (status != null) {
        queryBuilder = queryBuilder.eq('status', status);
      }

      if (partnerId != null) {
        queryBuilder = queryBuilder.eq('partner_id', partnerId);
      }

      if (startDate != null) {
        queryBuilder = queryBuilder.gte(
          'issue_date',
          startDate.toIso8601String().split('T')[0],
        );
      }

      if (endDate != null) {
        queryBuilder = queryBuilder.lte(
          'issue_date',
          endDate.toIso8601String().split('T')[0],
        );
      }

      // Order first, then apply pagination
      var orderedQuery = queryBuilder.order('created_at', ascending: false);

      // Apply pagination
      if (offset != null) {
        orderedQuery = orderedQuery.range(offset, (offset + (limit ?? 50)) - 1);
      } else if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      final response = await orderedQuery;

      return response.map<Invoice>((data) {
        return Invoice(
          id: data['id'],
          invoiceNumber: data['invoice_number'],
          type: 'sale', // Default type since column doesn't exist yet
          orderId: data['order_id'],
          partnerId: data['partner_id'],
          partnerName: data['partners']?['name'],
          issueDate: DateTime.parse(data['issue_date']),
          dueDate: data['due_date'] != null
              ? DateTime.parse(data['due_date'])
              : null,
          status: data['status'],
          subtotal: (data['subtotal'] as num).toDouble(),
          taxAmount: (data['tax_amount'] as num).toDouble(),
          discountAmount: (data['discount_amount'] as num).toDouble(),
          totalAmount: (data['total_amount'] as num).toDouble(),
          paidAmount: (data['paid_amount'] as num).toDouble(),
          notes: data['notes'],
          items: [], // TODO: Load invoice items separately
          createdBy: data['created_by'],
          createdAt: DateTime.parse(data['created_at']),
          updatedAt: DateTime.parse(data['updated_at']),
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to load invoices: $e');
    }
  }

  // Get invoice by ID
  static Future<Invoice?> getInvoiceById(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      final invoices = _getDemoInvoices();
      return invoices.firstWhere(
        (inv) => inv.id == id,
        orElse: () => invoices.first,
      );
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase!
          .from('invoices')
          .select('''
            id, invoice_number, order_id, partner_id, issue_date, due_date,
            status, subtotal, tax_amount, discount_amount, total_amount, paid_amount,
            notes, created_at, updated_at, created_by,
            partners(id, name, type, email, phone)
          ''')
          .eq('id', id)
          .single();

      return Invoice(
        id: response['id'],
        invoiceNumber: response['invoice_number'],
        type: 'sale', // Default type since column doesn't exist
        orderId: response['order_id'], // Will be null
        partnerId: response['partner_id'], // Will be null
        partnerName: response['partners']?['name'] ?? 'Khách lẻ',
        issueDate: DateTime.parse(response['issue_date']),
        dueDate: response['due_date'] != null
            ? DateTime.parse(response['due_date'])
            : null,
        status: response['status'],
        subtotal: (response['subtotal'] as num).toDouble(),
        taxAmount: (response['tax_amount'] as num).toDouble(),
        discountAmount: (response['discount_amount'] as num).toDouble(),
        totalAmount: (response['total_amount'] as num).toDouble(),
        paidAmount: (response['paid_amount'] as num).toDouble(),
        notes: response['notes'],
        items: [], // TODO: Load invoice items separately
        createdBy: response['created_by'] ?? 'system',
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['updated_at']),
      );
    } catch (e) {
      print('Error loading invoice by ID: $e');
      return null;
    }
  }

  // Create invoice
  static Future<Invoice> createInvoice(Invoice invoice) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate creation in demo mode
      await Future.delayed(const Duration(milliseconds: 500));
      return invoice.copyWith(
        id: 'demo-${DateTime.now().millisecondsSinceEpoch}',
        invoiceNumber: 'INV-${DateTime.now().millisecondsSinceEpoch}',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase!
          .from('invoices')
          .insert({
            'invoice_number': invoice.invoiceNumber,
            // order_id: NULL (optional foreign key)
            // partner_id: NULL (optional foreign key)
            // created_by: NULL (optional foreign key)
            'issue_date': invoice.issueDate.toIso8601String().split('T')[0],
            'due_date': invoice.dueDate?.toIso8601String().split('T')[0],
            'status': invoice.status,
            'subtotal': invoice.subtotal,
            'tax_amount': invoice.taxAmount,
            'discount_amount': invoice.discountAmount,
            'total_amount': invoice.totalAmount,
            'paid_amount': invoice.paidAmount,
            'notes': invoice.notes,
          })
          .select()
          .single();

      return Invoice(
        id: response['id'],
        invoiceNumber: response['invoice_number'],
        type: 'sale', // Default type since column doesn't exist
        orderId: response['order_id'], // Will be null
        partnerId: response['partner_id'], // Will be null
        partnerName: 'Khách lẻ', // Default customer name
        issueDate: DateTime.parse(response['issue_date']),
        dueDate: response['due_date'] != null
            ? DateTime.parse(response['due_date'])
            : null,
        status: response['status'],
        subtotal: (response['subtotal'] as num).toDouble(),
        taxAmount: (response['tax_amount'] as num).toDouble(),
        discountAmount: (response['discount_amount'] as num).toDouble(),
        totalAmount: (response['total_amount'] as num).toDouble(),
        paidAmount: (response['paid_amount'] as num).toDouble(),
        notes: response['notes'],
        items: [], // Items will be created separately
        createdBy: response['created_by'] ?? 'system', // Handle null created_by
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['updated_at']),
      );
    } catch (e) {
      throw Exception('Failed to create invoice: $e');
    }
  }

  // Update invoice
  static Future<Invoice> updateInvoice(String id, Invoice invoice) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      throw Exception('Demo mode - cannot update invoices');
    }

    // Real implementation would go here
    throw Exception('Demo mode - cannot update invoices');
  }

  // Delete invoice
  static Future<void> deleteInvoice(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      throw Exception('Demo mode - cannot delete invoices');
    }

    // Real implementation would go here
    throw Exception('Demo mode - cannot delete invoices');
  }

  // Update invoice status
  static Future<Invoice> updateInvoiceStatus(String id, String status) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      throw Exception('Demo mode - cannot update invoice status');
    }

    // Real implementation would go here
    throw Exception('Demo mode - cannot update invoice status');
  }

  // Record payment
  static Future<Invoice> recordPayment(String id, double amount) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      throw Exception('Demo mode - cannot record payments');
    }

    // Real implementation would go here
    throw Exception('Demo mode - cannot record payments');
  }

  // Get invoice statistics
  static Future<Map<String, dynamic>> getInvoiceStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoInvoiceStatistics();
    }

    // Real implementation would go here
    return _getDemoInvoiceStatistics();
  }

  // Generate invoice number
  static Future<String> generateInvoiceNumber() async {
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');
    final timestamp = now.millisecondsSinceEpoch.toString().substring(8);

    return 'INV$year$month$day$timestamp';
  }

  // Demo data methods
  static List<Invoice> _getDemoInvoices() {
    final now = DateTime.now();
    return [
      Invoice(
        id: 'demo-inv-1',
        invoiceNumber: 'INV240001',
        type: 'sale',
        partnerId: 'demo-customer-1',
        partnerName: 'Nguyễn Văn A',
        issueDate: now.subtract(const Duration(days: 5)),
        dueDate: now.add(const Duration(days: 25)),
        status: 'sent',
        subtotal: 1500000,
        taxAmount: 150000,
        discountAmount: 50000,
        totalAmount: 1600000,
        paidAmount: 0,
        items: [
          InvoiceItem(
            id: 'item-1',
            productName: 'Sản phẩm A',
            productSku: 'SKU001',
            quantity: 2,
            unit: 'cái',
            unitPrice: 500000,
            discountAmount: 25000,
            taxAmount: 75000,
            totalAmount: 975000,
          ),
          InvoiceItem(
            id: 'item-2',
            productName: 'Sản phẩm B',
            productSku: 'SKU002',
            quantity: 1,
            unit: 'cái',
            unitPrice: 1000000,
            discountAmount: 25000,
            taxAmount: 75000,
            totalAmount: 1050000,
          ),
        ],
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
      Invoice(
        id: 'demo-inv-2',
        invoiceNumber: 'INV240002',
        type: 'sale',
        partnerId: 'demo-customer-2',
        partnerName: 'Trần Thị B',
        issueDate: now.subtract(const Duration(days: 3)),
        dueDate: now.add(const Duration(days: 27)),
        status: 'paid',
        subtotal: 2500000,
        taxAmount: 250000,
        discountAmount: 100000,
        totalAmount: 2650000,
        paidAmount: 2650000,
        items: [
          InvoiceItem(
            id: 'item-3',
            productName: 'Sản phẩm C',
            productSku: 'SKU003',
            quantity: 5,
            unit: 'cái',
            unitPrice: 500000,
            discountAmount: 100000,
            taxAmount: 250000,
            totalAmount: 2650000,
          ),
        ],
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
      Invoice(
        id: 'demo-inv-3',
        invoiceNumber: 'INV240003',
        type: 'purchase',
        partnerId: 'demo-supplier-1',
        partnerName: 'Công ty ABC',
        issueDate: now.subtract(const Duration(days: 10)),
        dueDate: now.subtract(const Duration(days: 1)),
        status: 'overdue',
        subtotal: 3000000,
        taxAmount: 300000,
        discountAmount: 0,
        totalAmount: 3300000,
        paidAmount: 1000000,
        items: [
          InvoiceItem(
            id: 'item-4',
            productName: 'Nguyên liệu D',
            productSku: 'SKU004',
            quantity: 10,
            unit: 'kg',
            unitPrice: 300000,
            discountAmount: 0,
            taxAmount: 300000,
            totalAmount: 3300000,
          ),
        ],
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
    ];
  }

  static Map<String, dynamic> _getDemoInvoiceStatistics() {
    return {
      'totalInvoices': 15,
      'totalAmount': 25000000.0,
      'paidAmount': 18000000.0,
      'pendingAmount': 7000000.0,
      'overdueAmount': 2000000.0,
      'draftInvoices': 2,
      'sentInvoices': 8,
      'paidInvoices': 4,
      'overdueInvoices': 1,
      'averageInvoiceValue': 1666666.67,
      'paymentRate': 72.0,
    };
  }
}
