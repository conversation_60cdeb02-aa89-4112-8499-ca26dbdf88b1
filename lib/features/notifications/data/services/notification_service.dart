import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/config/supabase_config.dart';
import '../../../notifications/domain/entities/notification.dart';

class NotificationService {
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Get notifications
  static Future<List<AppNotification>> getNotifications({
    bool? isRead,
    String? category,
    String? type,
    int? limit,
    int? offset,
  }) async {
    try {
      dynamic query = _supabase.from('notifications').select('*');

      // Apply filters
      if (isRead != null) {
        query = query.eq('is_read', isRead);
      }
      if (category != null && category.isNotEmpty && category != 'all') {
        query = query.eq('category', category);
      }
      if (type != null && type.isNotEmpty && type != 'all') {
        query = query.eq('type', type);
      }

      // Apply ordering and pagination
      query = query.order('created_at', ascending: false);
      if (limit != null) {
        query = query.limit(limit);
      }
      if (offset != null) {
        query = query.range(offset, offset + limit! - 1);
      }

      final response = await query;
      debugPrint('✅ Loaded ${response.length} notifications from Supabase');
      return response
          .map<AppNotification>((json) => AppNotification.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting notifications: $e');
      return [];
    }
  }

  // Get unread notifications count
  static Future<int> getUnreadCount() async {
    try {
      final response = await _supabase
          .from('notifications')
          .select('id')
          .eq('is_read', false);

      debugPrint('✅ Found ${response.length} unread notifications');
      return response.length;
    } catch (e) {
      debugPrint('❌ Error getting unread count: $e');
      return 0;
    }
  }

  // Mark notification as read
  static Future<void> markAsRead(String notificationId) async {
    try {
      await _supabase
          .from('notifications')
          .update({
            'is_read': true,
            'read_at': DateTime.now().toIso8601String(),
          })
          .eq('id', notificationId);
      debugPrint('✅ Marked notification $notificationId as read');
    } catch (e) {
      debugPrint('❌ Error marking notification as read: $e');
      throw Exception('Lỗi đánh dấu thông báo đã đọc: $e');
    }
  }

  // Mark all notifications as read
  static Future<void> markAllAsRead() async {
    try {
      await _supabase
          .from('notifications')
          .update({
            'is_read': true,
            'read_at': DateTime.now().toIso8601String(),
          })
          .eq('is_read', false);
      debugPrint('✅ Marked all notifications as read');
    } catch (e) {
      debugPrint('❌ Error marking all notifications as read: $e');
      throw Exception('Lỗi đánh dấu tất cả thông báo đã đọc: $e');
    }
  }

  // Delete notification
  static Future<void> deleteNotification(String notificationId) async {
    try {
      await _supabase.from('notifications').delete().eq('id', notificationId);
      debugPrint('✅ Deleted notification $notificationId');
    } catch (e) {
      debugPrint('❌ Error deleting notification: $e');
      throw Exception('Lỗi xóa thông báo: $e');
    }
  }

  // Delete all read notifications
  static Future<void> deleteAllRead() async {
    try {
      await _supabase.from('notifications').delete().eq('is_read', true);
      debugPrint('✅ Deleted all read notifications');
    } catch (e) {
      debugPrint('❌ Error deleting read notifications: $e');
      throw Exception('Lỗi xóa thông báo đã đọc: $e');
    }
  }

  // Create notification
  static Future<AppNotification> createNotification(
    AppNotification notification,
  ) async {
    try {
      final data = {
        'title': notification.title,
        'message': notification.message,
        'type': notification.type,
        'category': notification.category,
        'is_read': notification.isRead,
        'is_important': notification.isImportant,
        'action_url': notification.actionUrl,
        'action_label': notification.actionLabel,
        'data': notification.data,
        'expires_at': notification.expiresAt?.toIso8601String(),
        'user_id': null, // Global notification for now
      };

      final response = await _supabase
          .from('notifications')
          .insert(data)
          .select()
          .single();

      debugPrint('✅ Created notification: ${notification.title}');
      return AppNotification.fromJson(response);
    } catch (e) {
      debugPrint('❌ Error creating notification: $e');
      throw Exception('Lỗi tạo thông báo: $e');
    }
  }

  // Send push notification (for mobile)
  static Future<void> sendPushNotification({
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    // Simulate push notification for now
    await Future.delayed(const Duration(milliseconds: 500));
    debugPrint('📱 Push notification sent: $title - $message');
  }

  // Get notification settings
  static Future<Map<String, bool>> getNotificationSettings() async {
    // Return default settings for now
    return {
      'lowStock': true,
      'newOrders': true,
      'payments': true,
      'systemUpdates': true,
      'errors': true,
      'pushNotifications': false,
      'emailNotifications': true,
    };
  }

  // Update notification settings
  static Future<void> updateNotificationSettings(
    Map<String, bool> settings,
  ) async {
    // Simulate settings update
    await Future.delayed(const Duration(milliseconds: 500));
    debugPrint('⚙️ Notification settings updated: $settings');
  }

  // Helper methods for creating specific notification types
  static Future<void> notifyLowStock(
    String productName,
    int currentStock,
    int minStock,
  ) async {
    final notification = AppNotification.lowStock(
      productName: productName,
      currentStock: currentStock,
      minStock: minStock,
    );

    await createNotification(notification);

    // Send push notification if enabled
    final settings = await getNotificationSettings();
    if (settings['pushNotifications'] == true) {
      await sendPushNotification(
        title: notification.title,
        message: notification.message,
        data: notification.data,
      );
    }
  }

  static Future<void> notifyNewOrder(
    String orderNumber,
    double totalAmount,
    String customerName,
  ) async {
    final notification = AppNotification.newOrder(
      orderNumber: orderNumber,
      totalAmount: totalAmount,
      customerName: customerName,
    );

    await createNotification(notification);

    final settings = await getNotificationSettings();
    if (settings['pushNotifications'] == true) {
      await sendPushNotification(
        title: notification.title,
        message: notification.message,
        data: notification.data,
      );
    }
  }

  static Future<void> notifyPaymentReceived(
    String orderNumber,
    double amount,
    String paymentMethod,
  ) async {
    final notification = AppNotification.paymentReceived(
      orderNumber: orderNumber,
      amount: amount,
      paymentMethod: paymentMethod,
    );

    await createNotification(notification);
  }

  static Future<void> notifyError(
    String title,
    String message, {
    Map<String, dynamic>? data,
  }) async {
    final notification = AppNotification.error(
      title: title,
      message: message,
      data: data,
    );

    await createNotification(notification);

    final settings = await getNotificationSettings();
    if (settings['pushNotifications'] == true) {
      await sendPushNotification(
        title: notification.title,
        message: notification.message,
        data: notification.data,
      );
    }
  }
}
