class AppNotification {
  final String id;
  final String title;
  final String message;
  final String type; // info, warning, error, success
  final String category; // system, order, inventory, finance, user
  final bool isRead;
  final bool isImportant;
  final Map<String, dynamic>? data;
  final String? actionUrl;
  final String? actionLabel;
  final DateTime createdAt;
  final DateTime? readAt;
  final DateTime? expiresAt;

  const AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.category,
    this.isRead = false,
    this.isImportant = false,
    this.data,
    this.actionUrl,
    this.actionLabel,
    required this.createdAt,
    this.readAt,
    this.expiresAt,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: json['type'] as String,
      category: json['category'] as String,
      isRead: json['is_read'] as bool? ?? false,
      isImportant: json['is_important'] as bool? ?? false,
      data: json['data'] as Map<String, dynamic>?,
      actionUrl: json['action_url'] as String?,
      actionLabel: json['action_label'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      readAt: json['read_at'] != null ? DateTime.parse(json['read_at'] as String) : null,
      expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'category': category,
      'is_read': isRead,
      'is_important': isImportant,
      'data': data,
      'action_url': actionUrl,
      'action_label': actionLabel,
      'created_at': createdAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
    };
  }

  // Calculated properties
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);
  bool get hasAction => actionUrl != null && actionLabel != null;
  
  Duration get age => DateTime.now().difference(createdAt);
  
  String get ageText {
    final minutes = age.inMinutes;
    final hours = age.inHours;
    final days = age.inDays;
    
    if (days > 0) {
      return '$days ngày trước';
    } else if (hours > 0) {
      return '$hours giờ trước';
    } else if (minutes > 0) {
      return '$minutes phút trước';
    } else {
      return 'Vừa xong';
    }
  }

  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    String? category,
    bool? isRead,
    bool? isImportant,
    Map<String, dynamic>? data,
    String? actionUrl,
    String? actionLabel,
    DateTime? createdAt,
    DateTime? readAt,
    DateTime? expiresAt,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      category: category ?? this.category,
      isRead: isRead ?? this.isRead,
      isImportant: isImportant ?? this.isImportant,
      data: data ?? this.data,
      actionUrl: actionUrl ?? this.actionUrl,
      actionLabel: actionLabel ?? this.actionLabel,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  // Mark as read
  AppNotification markAsRead() {
    return copyWith(
      isRead: true,
      readAt: DateTime.now(),
    );
  }

  // Factory methods for different notification types
  factory AppNotification.lowStock({
    required String productName,
    required int currentStock,
    required int minStock,
  }) {
    return AppNotification(
      id: 'low_stock_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Sản phẩm sắp hết hàng',
      message: '$productName chỉ còn $currentStock (tối thiểu: $minStock)',
      type: 'warning',
      category: 'inventory',
      isImportant: true,
      data: {
        'productName': productName,
        'currentStock': currentStock,
        'minStock': minStock,
      },
      actionUrl: '/inventory',
      actionLabel: 'Xem kho hàng',
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(const Duration(days: 7)),
    );
  }

  factory AppNotification.newOrder({
    required String orderNumber,
    required double totalAmount,
    required String customerName,
  }) {
    return AppNotification(
      id: 'new_order_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Đơn hàng mới',
      message: 'Đơn hàng $orderNumber từ $customerName - ${totalAmount.toStringAsFixed(0)}₫',
      type: 'info',
      category: 'order',
      data: {
        'orderNumber': orderNumber,
        'totalAmount': totalAmount,
        'customerName': customerName,
      },
      actionUrl: '/sales',
      actionLabel: 'Xem đơn hàng',
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(const Duration(days: 30)),
    );
  }

  factory AppNotification.paymentReceived({
    required String orderNumber,
    required double amount,
    required String paymentMethod,
  }) {
    return AppNotification(
      id: 'payment_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Thanh toán thành công',
      message: 'Nhận thanh toán ${amount.toStringAsFixed(0)}₫ cho đơn $orderNumber',
      type: 'success',
      category: 'finance',
      data: {
        'orderNumber': orderNumber,
        'amount': amount,
        'paymentMethod': paymentMethod,
      },
      actionUrl: '/finance',
      actionLabel: 'Xem sổ quỹ',
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(const Duration(days: 30)),
    );
  }

  factory AppNotification.systemUpdate({
    required String version,
    required String description,
  }) {
    return AppNotification(
      id: 'system_update_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Cập nhật hệ thống',
      message: 'Phiên bản $version đã có sẵn: $description',
      type: 'info',
      category: 'system',
      data: {
        'version': version,
        'description': description,
      },
      actionUrl: '/settings',
      actionLabel: 'Cập nhật ngay',
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(const Duration(days: 7)),
    );
  }

  factory AppNotification.error({
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) {
    return AppNotification(
      id: 'error_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      message: message,
      type: 'error',
      category: 'system',
      isImportant: true,
      data: data,
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(const Duration(days: 3)),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AppNotification(id: $id, title: $title, type: $type, category: $category, isRead: $isRead)';
  }
}
