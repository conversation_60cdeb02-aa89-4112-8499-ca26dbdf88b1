import 'package:flutter/material.dart';

import '../../../../core/widgets/app_card.dart';

class NotificationFiltersWidget extends StatelessWidget {
  final String selectedCategory;
  final String selectedType;
  final bool showOnlyUnread;
  final Function(String?, String?, bool?) onFiltersChanged;

  const NotificationFiltersWidget({
    super.key,
    required this.selectedCategory,
    required this.selectedType,
    required this.showOnlyUnread,
    required this.onFiltersChanged,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return AppCard(
      child: Column(
        children: [
          // Use responsive layout based on screen width
          if (screenWidth < 768)
            _buildMobileLayout(context)
          else
            _buildDesktopLayout(context),
        ],
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        // First row: Category and Type filters
        Row(
          children: [
            Expanded(child: _buildCategoryFilter(context)),
            const SizedBox(width: 12),
            Expanded(child: _buildTypeFilter(context)),
          ],
        ),
        const SizedBox(height: 12),
        // Second row: Unread filter
        Align(
          alignment: Alignment.centerLeft,
          child: _buildUnreadFilter(context),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildCategoryFilter(context)),
        const SizedBox(width: 16),
        Expanded(child: _buildTypeFilter(context)),
        const SizedBox(width: 16),
        _buildUnreadFilter(context),
      ],
    );
  }

  Widget _buildCategoryFilter(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selectedCategory,
      decoration: const InputDecoration(
        labelText: 'Danh mục',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      style: TextStyle(
        fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
        color: Colors.black87,
      ),
      items: [
        DropdownMenuItem(
          value: 'all',
          child: Text(
            'Tất cả',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'system',
          child: Text(
            'Hệ thống',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'order',
          child: Text(
            'Đơn hàng',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'inventory',
          child: Text(
            'Kho hàng',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'finance',
          child: Text(
            'Tài chính',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'user',
          child: Text(
            'Người dùng',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
      ],
      onChanged: (value) {
        onFiltersChanged(value, null, null);
      },
    );
  }

  Widget _buildTypeFilter(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selectedType,
      decoration: const InputDecoration(
        labelText: 'Loại',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      style: TextStyle(
        fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
        color: Colors.black87,
      ),
      items: [
        DropdownMenuItem(
          value: 'all',
          child: Text(
            'Tất cả',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'info',
          child: Text(
            'Thông tin',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'warning',
          child: Text(
            'Cảnh báo',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'error',
          child: Text(
            'Lỗi',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'success',
          child: Text(
            'Thành công',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
      ],
      onChanged: (value) {
        onFiltersChanged(null, value, null);
      },
    );
  }

  Widget _buildUnreadFilter(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Checkbox(
          value: showOnlyUnread,
          onChanged: (value) {
            onFiltersChanged(null, null, value);
          },
        ),
        Text(
          'Chỉ chưa đọc',
          style: TextStyle(
            fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
          ),
        ),
      ],
    );
  }
}
