import 'package:flutter/material.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../generated/l10n/app_localizations.dart';

class NotificationFiltersWidget extends StatelessWidget {
  final String selectedCategory;
  final String selectedType;
  final bool showOnlyUnread;
  final Function(String?, String?, bool?) onFiltersChanged;

  const NotificationFiltersWidget({
    super.key,
    required this.selectedCategory,
    required this.selectedType,
    required this.showOnlyUnread,
    required this.onFiltersChanged,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final l10n = AppLocalizations.of(context);

    return AppCard(
      child: Column(
        children: [
          // Use responsive layout based on screen width
          if (screenWidth < 768)
            _buildMobileLayout(context)
          else
            _buildDesktopLayout(context),
        ],
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Column(
      children: [
        // First row: Category and Type filters
        Row(
          children: [
            Expanded(child: _buildCategoryFilter(context)),
            const SizedBox(width: 12),
            Expanded(child: _buildTypeFilter(context)),
          ],
        ),
        const SizedBox(height: 12),
        // Second row: Unread filter
        Align(
          alignment: Alignment.centerLeft,
          child: _buildUnreadFilter(context),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildCategoryFilter(context)),
        const SizedBox(width: 16),
        Expanded(child: _buildTypeFilter(context)),
        const SizedBox(width: 16),
        _buildUnreadFilter(context),
      ],
    );
  }

  Widget _buildCategoryFilter(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return DropdownButtonFormField<String>(
      value: selectedCategory,
      decoration: InputDecoration(
        labelText: l10n.notificationCategory,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      style: TextStyle(
        fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
        color: Colors.black87,
      ),
      items: [
        DropdownMenuItem(
          value: 'all',
          child: Text(
            l10n.notificationCategoryAll,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'system',
          child: Text(
            l10n.notificationCategorySystem,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'order',
          child: Text(
            l10n.notificationCategoryOrder,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'inventory',
          child: Text(
            l10n.notificationCategoryInventory,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'finance',
          child: Text(
            l10n.notificationCategoryFinance,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'user',
          child: Text(
            l10n.notificationCategoryUser,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
      ],
      onChanged: (value) {
        onFiltersChanged(value, null, null);
      },
    );
  }

  Widget _buildTypeFilter(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return DropdownButtonFormField<String>(
      value: selectedType,
      decoration: InputDecoration(
        labelText: l10n.notificationType,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      style: TextStyle(
        fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
        color: Colors.black87,
      ),
      items: [
        DropdownMenuItem(
          value: 'all',
          child: Text(
            l10n.notificationTypeAll,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'info',
          child: Text(
            l10n.notificationTypeInfo,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'warning',
          child: Text(
            l10n.notificationTypeWarning,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'error',
          child: Text(
            l10n.notificationTypeError,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
        DropdownMenuItem(
          value: 'success',
          child: Text(
            l10n.notificationTypeSuccess,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
            ),
          ),
        ),
      ],
      onChanged: (value) {
        onFiltersChanged(null, value, null);
      },
    );
  }

  Widget _buildUnreadFilter(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Checkbox(
          value: showOnlyUnread,
          onChanged: (value) {
            onFiltersChanged(null, null, value);
          },
        ),
        Text(
          l10n.notificationUnreadOnly,
          style: TextStyle(
            fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
          ),
        ),
      ],
    );
  }
}
