import 'package:flutter/material.dart';

import '../../domain/entities/notification.dart';

class NotificationItemWidget extends StatelessWidget {
  final AppNotification notification;
  final VoidCallback onTap;
  final VoidCallback onMarkAsRead;
  final VoidCallback onDelete;

  const NotificationItemWidget({
    super.key,
    required this.notification,
    required this.onTap,
    required this.onMarkAsRead,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: notification.isRead ? null : Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: _buildLeadingIcon(),
        title: Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: notification.isRead
                      ? FontWeight.normal
                      : FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (!notification.isRead)
              Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              notification.message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.withValues(alpha: 0.8),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 6,
              runSpacing: 4,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: MediaQuery.of(context).size.width < 600
                        ? 4.0
                        : 6.0,
                    vertical: MediaQuery.of(context).size.width < 600
                        ? 1.0
                        : 2.0,
                  ),
                  decoration: BoxDecoration(
                    color: _getCategoryColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(
                      MediaQuery.of(context).size.width < 600 ? 3.0 : 4.0,
                    ),
                  ),
                  child: Text(
                    _getCategoryLabel(),
                    style: TextStyle(
                      color: _getCategoryColor(),
                      fontSize: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 10.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 100),
                  child: Text(
                    notification.ageText,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.withValues(alpha: 0.6),
                      fontSize: MediaQuery.of(context).size.width < 600
                          ? 10.0
                          : 12.0,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (notification.isImportant)
                  Icon(
                    Icons.priority_high,
                    size: 16.0,
                    color: Colors.red.withValues(alpha: 0.7),
                  ),
                if (notification.hasAction) ...[
                  ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 80),
                    child: Text(
                      notification.actionLabel!,
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Colors.blue.withValues(alpha: 0.8),
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12.0,
                    color: Colors.blue.withValues(alpha: 0.8),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'mark_read':
                onMarkAsRead();
                break;
              case 'delete':
                onDelete();
                break;
            }
          },
          itemBuilder: (context) => [
            if (!notification.isRead)
              PopupMenuItem(
                value: 'mark_read',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.mark_email_read,
                      size: MediaQuery.of(context).size.width < 600
                          ? 14.0
                          : 16.0,
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        'Đánh dấu đã đọc',
                        style: TextStyle(
                          fontSize: MediaQuery.of(context).size.width < 600
                              ? 12.0
                              : 14.0,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.delete,
                    size: MediaQuery.of(context).size.width < 600 ? 14.0 : 16.0,
                    color: Colors.red,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      'Xóa',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: MediaQuery.of(context).size.width < 600
                            ? 12.0
                            : 14.0,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildLeadingIcon() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: _getTypeColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(_getTypeIcon(), color: _getTypeColor(), size: 20),
    );
  }

  Color _getTypeColor() {
    switch (notification.type) {
      case 'info':
        return Colors.blue;
      case 'warning':
        return Colors.orange;
      case 'error':
        return Colors.red;
      case 'success':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon() {
    switch (notification.type) {
      case 'info':
        return Icons.info;
      case 'warning':
        return Icons.warning;
      case 'error':
        return Icons.error;
      case 'success':
        return Icons.check_circle;
      default:
        return Icons.notifications;
    }
  }

  Color _getCategoryColor() {
    switch (notification.category) {
      case 'system':
        return Colors.purple;
      case 'order':
        return Colors.blue;
      case 'inventory':
        return Colors.orange;
      case 'finance':
        return Colors.green;
      case 'user':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  String _getCategoryLabel() {
    switch (notification.category) {
      case 'system':
        return 'Hệ thống';
      case 'order':
        return 'Đơn hàng';
      case 'inventory':
        return 'Kho hàng';
      case 'finance':
        return 'Tài chính';
      case 'user':
        return 'Người dùng';
      default:
        return notification.category;
    }
  }
}
