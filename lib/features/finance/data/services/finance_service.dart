import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/config/supabase_config.dart';
import '../../domain/entities/cashbook_entry.dart';

class FinanceService {
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Get all cashbook entries
  static Future<List<CashbookEntry>> getCashbookEntries({
    CashbookType? type,
    PaymentCategory? category,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    int? limit,
    int? offset,
  }) async {
    if (SupabaseConfig.isDemoMode) {
      return _getDemoCashbookEntries()
          .where((entry) {
            if (type != null && entry.type != type) return false;
            if (category != null && entry.category != category) return false;
            if (startDate != null && entry.entryDate.isBefore(startDate)) {
              return false;
            }
            if (endDate != null && entry.entryDate.isAfter(endDate)) {
              return false;
            }
            if (searchQuery != null && searchQuery.isNotEmpty) {
              final query = searchQuery.toLowerCase();
              return entry.description.toLowerCase().contains(query) ||
                  entry.entryNumber.toLowerCase().contains(query) ||
                  (entry.partnerName?.toLowerCase().contains(query) ?? false);
            }
            return true;
          })
          .skip(offset ?? 0)
          .take(limit ?? 50)
          .toList();
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      debugPrint('🔥 Loading finance entries from Supabase...');
      debugPrint(
        '🔥 Filters: type=$type, category=$category, search=$searchQuery',
      );

      var queryBuilder = _supabase.from('payments').select('''
        id, payment_number, type, category, amount, description,
        partner_id, payment_date, created_at, updated_at, created_by,
        partners(id, name, type)
      ''');

      // Apply filters
      if (type != null) {
        // Map type to database values
        final dbType = type == CashbookType.receipt ? 'income' : 'expense';
        queryBuilder = queryBuilder.eq('type', dbType);
      }

      if (category != null) {
        queryBuilder = queryBuilder.eq('category', category.name);
      }

      if (startDate != null) {
        queryBuilder = queryBuilder.gte(
          'payment_date',
          startDate.toIso8601String().split('T')[0],
        );
      }

      if (endDate != null) {
        queryBuilder = queryBuilder.lte(
          'payment_date',
          endDate.toIso8601String().split('T')[0],
        );
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryBuilder = queryBuilder.or(
          'description.ilike.%$searchQuery%,payment_number.ilike.%$searchQuery%',
        );
      }

      // Order and execute
      final response = await queryBuilder.order(
        'payment_date',
        ascending: false,
      );

      debugPrint('🔥 Loaded ${response.length} finance entries from Supabase');

      return response.map<CashbookEntry>((data) {
        return CashbookEntry(
          id: data['id'],
          entryNumber: data['payment_number'],
          type: data['type'] == 'income'
              ? CashbookType.receipt
              : CashbookType.payment,
          category: PaymentCategory.values.firstWhere(
            (c) => c.name == data['category'],
          ),
          amount: (data['amount'] as num).toDouble(),
          partnerId: data['partner_id'],
          partnerName: data['partners']?['name'],
          description: data['description'],
          entryDate: DateTime.parse(data['payment_date']),
          createdAt: DateTime.parse(data['created_at']),
          updatedAt: DateTime.parse(data['updated_at']),
          createdBy: data['created_by'],
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to load cashbook entries: $e');
    }
  }

  // Get cashbook entry by ID
  static Future<CashbookEntry?> getCashbookEntryById(String id) async {
    if (SupabaseConfig.isDemoMode) {
      try {
        return _getDemoCashbookEntries().firstWhere((entry) => entry.id == id);
      } catch (e) {
        return null;
      }
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase
          .from('payments')
          .select('''
          id, payment_number, type, category, amount, description,
          partner_id, payment_date, created_at, updated_at, created_by,
          partners(id, name, type)
        ''')
          .eq('id', id)
          .single();

      return CashbookEntry(
        id: response['id'],
        entryNumber: response['payment_number'],
        type: response['type'] == 'income'
            ? CashbookType.receipt
            : CashbookType.payment,
        category: PaymentCategory.values.firstWhere(
          (c) => c.name == response['category'],
        ),
        amount: (response['amount'] as num).toDouble(),
        partnerId: response['partner_id'],
        partnerName: response['partners']?['name'],
        description: response['description'],
        entryDate: DateTime.parse(response['payment_date']),
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['updated_at']),
        createdBy: response['created_by'],
      );
    } catch (e) {
      return null;
    }
  }

  // Create new cashbook entry
  static Future<CashbookEntry> createCashbookEntry(CashbookEntry entry) async {
    if (SupabaseConfig.isDemoMode) {
      // Simulate creation
      await Future.delayed(const Duration(milliseconds: 500));
      return entry.copyWith(
        id: 'entry_${DateTime.now().millisecondsSinceEpoch}',
        entryNumber: _generateEntryNumber(entry.type),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      // Debug: Print values being sent
      debugPrint('=== FINANCE DEBUG ===');
      debugPrint('Original Type: ${entry.type.value}');
      debugPrint('Category: ${entry.category.name}');
      debugPrint('Amount: ${entry.amount}');

      // Map type to database accepted values
      final dbType = entry.type == CashbookType.receipt ? 'income' : 'expense';
      debugPrint('Mapped DB Type: $dbType');

      final response = await _supabase
          .from('payments')
          .insert({
            'payment_number': _generateEntryNumber(entry.type),
            'type': dbType, // Use mapped type instead of entry.type.value
            'category': entry.category.name,
            'amount': entry.amount,
            'description': entry.description,
            'partner_id': entry.partnerId,
            'payment_date': entry.entryDate.toIso8601String().split('T')[0],
            'payment_method': 'cash', // Default payment method
          })
          .select('''
        id, payment_number, type, category, amount, description,
        partner_id, payment_date, created_at, updated_at, created_by,
        partners(id, name, type)
      ''')
          .single();

      return CashbookEntry(
        id: response['id'],
        entryNumber: response['payment_number'],
        type: response['type'] == 'income'
            ? CashbookType.receipt
            : CashbookType.payment,
        category: PaymentCategory.values.firstWhere(
          (c) => c.name == response['category'],
        ),
        amount: (response['amount'] as num).toDouble(),
        partnerId: response['partner_id'],
        partnerName: response['partners']?['name'],
        description: response['description'],
        entryDate: DateTime.parse(response['payment_date']),
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['updated_at']),
        createdBy: response['created_by'],
      );
    } catch (e) {
      throw Exception('Failed to create cashbook entry: $e');
    }
  }

  // Update cashbook entry
  static Future<CashbookEntry> updateCashbookEntry(
    String id,
    CashbookEntry entry,
  ) async {
    if (SupabaseConfig.isDemoMode) {
      // Simulate update
      await Future.delayed(const Duration(milliseconds: 500));
      return entry.copyWith(id: id, updatedAt: DateTime.now());
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      // Map type to database accepted values
      final dbType = entry.type == CashbookType.receipt ? 'income' : 'expense';

      final response = await _supabase
          .from('payments')
          .update({
            'type': dbType, // Use mapped type
            'category': entry.category.name,
            'amount': entry.amount,
            'description': entry.description,
            'partner_id': entry.partnerId,
            'payment_date': entry.entryDate.toIso8601String().split('T')[0],
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', id)
          .select('''
        id, payment_number, type, category, amount, description,
        partner_id, payment_date, created_at, updated_at, created_by,
        partners(id, name, type)
      ''')
          .single();

      return CashbookEntry(
        id: response['id'],
        entryNumber: response['payment_number'],
        type: response['type'] == 'income'
            ? CashbookType.receipt
            : CashbookType.payment,
        category: PaymentCategory.values.firstWhere(
          (c) => c.name == response['category'],
        ),
        amount: (response['amount'] as num).toDouble(),
        partnerId: response['partner_id'],
        partnerName: response['partners']?['name'],
        description: response['description'],
        entryDate: DateTime.parse(response['payment_date']),
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['updated_at']),
        createdBy: response['created_by'],
      );
    } catch (e) {
      throw Exception('Failed to update cashbook entry: $e');
    }
  }

  // Delete cashbook entry
  static Future<void> deleteCashbookEntry(String id) async {
    if (SupabaseConfig.isDemoMode) {
      // Simulate deletion
      await Future.delayed(const Duration(milliseconds: 500));
      return;
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      await _supabase.from('payments').delete().eq('id', id);
    } catch (e) {
      throw Exception('Failed to delete cashbook entry: $e');
    }
  }

  // Get cash balance
  static Future<CashBalance> getCashBalance({DateTime? asOfDate}) async {
    if (SupabaseConfig.isDemoMode) {
      final entries = await getCashbookEntries(
        endDate: asOfDate ?? DateTime.now(),
      );

      final receipts = entries.where((e) => e.isReceipt);
      final payments = entries.where((e) => e.isPayment);

      final totalReceipts = receipts.fold<double>(
        0,
        (sum, e) => sum + e.amount,
      );
      final totalPayments = payments.fold<double>(
        0,
        (sum, e) => sum + e.amount,
      );

      return CashBalance(
        openingBalance: 5000000, // Demo opening balance
        totalReceipts: totalReceipts,
        totalPayments: totalPayments,
        closingBalance: 5000000 + totalReceipts - totalPayments,
        asOfDate: asOfDate ?? DateTime.now(),
      );
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final entries = await getCashbookEntries(
        endDate: asOfDate ?? DateTime.now(),
      );

      final receipts = entries.where((e) => e.isReceipt);
      final payments = entries.where((e) => e.isPayment);

      final totalReceipts = receipts.fold<double>(
        0,
        (sum, e) => sum + e.amount,
      );
      final totalPayments = payments.fold<double>(
        0,
        (sum, e) => sum + e.amount,
      );

      return CashBalance(
        openingBalance: 0, // Could be configured or calculated
        totalReceipts: totalReceipts,
        totalPayments: totalPayments,
        closingBalance: totalReceipts - totalPayments,
        asOfDate: asOfDate ?? DateTime.now(),
      );
    } catch (e) {
      throw Exception('Failed to calculate cash balance: $e');
    }
  }

  // Get finance statistics
  static Future<Map<String, dynamic>> getFinanceStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (SupabaseConfig.isDemoMode) {
      final entries = await getCashbookEntries(
        startDate: startDate,
        endDate: endDate,
      );

      final receipts = entries.where((e) => e.isReceipt);
      final payments = entries.where((e) => e.isPayment);

      final totalReceipts = receipts.fold<double>(
        0,
        (sum, e) => sum + e.amount,
      );
      final totalPayments = payments.fold<double>(
        0,
        (sum, e) => sum + e.amount,
      );

      // Group by category
      final receiptsByCategory = <PaymentCategory, double>{};
      final paymentsByCategory = <PaymentCategory, double>{};

      for (final entry in receipts) {
        receiptsByCategory[entry.category] =
            (receiptsByCategory[entry.category] ?? 0) + entry.amount;
      }

      for (final entry in payments) {
        paymentsByCategory[entry.category] =
            (paymentsByCategory[entry.category] ?? 0) + entry.amount;
      }

      return {
        'totalEntries': entries.length,
        'totalReceipts': totalReceipts,
        'totalPayments': totalPayments,
        'netCashFlow': totalReceipts - totalPayments,
        'receiptsByCategory': receiptsByCategory,
        'paymentsByCategory': paymentsByCategory,
        'averageReceiptAmount': receipts.isNotEmpty
            ? totalReceipts / receipts.length
            : 0.0,
        'averagePaymentAmount': payments.isNotEmpty
            ? totalPayments / payments.length
            : 0.0,
      };
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final entries = await getCashbookEntries(
        startDate: startDate,
        endDate: endDate,
      );

      final receipts = entries.where((e) => e.isReceipt);
      final payments = entries.where((e) => e.isPayment);

      final totalReceipts = receipts.fold<double>(
        0,
        (sum, e) => sum + e.amount,
      );
      final totalPayments = payments.fold<double>(
        0,
        (sum, e) => sum + e.amount,
      );

      // Group by category
      final receiptsByCategory = <PaymentCategory, double>{};
      final paymentsByCategory = <PaymentCategory, double>{};

      for (final entry in receipts) {
        receiptsByCategory[entry.category] =
            (receiptsByCategory[entry.category] ?? 0) + entry.amount;
      }

      for (final entry in payments) {
        paymentsByCategory[entry.category] =
            (paymentsByCategory[entry.category] ?? 0) + entry.amount;
      }

      return {
        'totalEntries': entries.length,
        'totalReceipts': totalReceipts,
        'totalPayments': totalPayments,
        'netCashFlow': totalReceipts - totalPayments,
        'receiptsByCategory': receiptsByCategory,
        'paymentsByCategory': paymentsByCategory,
        'averageReceiptAmount': receipts.isNotEmpty
            ? totalReceipts / receipts.length
            : 0.0,
        'averagePaymentAmount': payments.isNotEmpty
            ? totalPayments / payments.length
            : 0.0,
      };
    } catch (e) {
      throw Exception('Failed to get finance stats: $e');
    }
  }

  // Demo data methods
  static List<CashbookEntry> _getDemoCashbookEntries() {
    final now = DateTime.now();

    return [
      CashbookEntry(
        id: 'entry_001',
        entryNumber: 'PT001',
        type: CashbookType.receipt,
        category: PaymentCategory.sales,
        amount: 500000,
        partnerId: 'customer_001',
        partnerName: 'Nguyễn Văn An',
        orderId: 'order_001',
        description: 'Thu tiền bán hàng - Đơn hàng HD001',
        note: 'Thanh toán tiền mặt',
        entryDate: now.subtract(const Duration(hours: 2)),
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        createdBy: 'staff',
      ),

      CashbookEntry(
        id: 'entry_002',
        entryNumber: 'PC001',
        type: CashbookType.payment,
        category: PaymentCategory.purchase,
        amount: 300000,
        partnerId: 'supplier_001',
        partnerName: 'Công ty ABC',
        description: 'Thanh toán tiền mua hàng',
        note: 'Chuyển khoản',
        entryDate: now.subtract(const Duration(hours: 4)),
        createdAt: now.subtract(const Duration(hours: 4)),
        updatedAt: now.subtract(const Duration(hours: 4)),
        createdBy: 'admin',
      ),

      CashbookEntry(
        id: 'entry_003',
        entryNumber: 'PC002',
        type: CashbookType.payment,
        category: PaymentCategory.rent,
        amount: 2000000,
        description: 'Tiền thuê mặt bằng tháng ${now.month}/${now.year}',
        note: 'Thanh toán hàng tháng',
        entryDate: now.subtract(const Duration(days: 1)),
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
        createdBy: 'admin',
      ),

      CashbookEntry(
        id: 'entry_004',
        entryNumber: 'PT002',
        type: CashbookType.receipt,
        category: PaymentCategory.sales,
        amount: 750000,
        partnerId: 'customer_002',
        partnerName: 'Trần Thị Bình',
        orderId: 'order_002',
        description: 'Thu tiền bán hàng - Đơn hàng HD002',
        note: 'Thanh toán thẻ',
        entryDate: now.subtract(const Duration(hours: 1)),
        createdAt: now.subtract(const Duration(hours: 1)),
        updatedAt: now.subtract(const Duration(hours: 1)),
        createdBy: 'staff',
      ),

      CashbookEntry(
        id: 'entry_005',
        entryNumber: 'PC003',
        type: CashbookType.payment,
        category: PaymentCategory.salary,
        amount: 8000000,
        description: 'Lương nhân viên tháng ${now.month}/${now.year}',
        note: 'Chuyển khoản lương',
        entryDate: now.subtract(const Duration(days: 2)),
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 2)),
        createdBy: 'admin',
      ),

      CashbookEntry(
        id: 'entry_006',
        entryNumber: 'PC004',
        type: CashbookType.payment,
        category: PaymentCategory.utilities,
        amount: 500000,
        description: 'Tiền điện nước tháng ${now.month}/${now.year}',
        note: 'Thanh toán hóa đơn',
        entryDate: now.subtract(const Duration(days: 3)),
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(days: 3)),
        createdBy: 'admin',
      ),

      CashbookEntry(
        id: 'entry_007',
        entryNumber: 'PT003',
        type: CashbookType.receipt,
        category: PaymentCategory.other,
        amount: 1000000,
        description: 'Thu khác - Hoàn trả tiền thừa',
        note: 'Tiền mặt',
        entryDate: now.subtract(const Duration(days: 5)),
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 5)),
        createdBy: 'admin',
      ),
    ];
  }

  static String _generateEntryNumber(CashbookType type) {
    final now = DateTime.now();
    final prefix = type == CashbookType.receipt ? 'PT' : 'PC';
    return '$prefix${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch.toString().substring(8)}';
  }
}
