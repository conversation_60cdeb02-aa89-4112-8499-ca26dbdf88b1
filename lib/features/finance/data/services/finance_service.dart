import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/material.dart';
import 'package:city_pos/generated/l10n/app_localizations.dart';

import '../../../../core/config/supabase_config.dart';
import '../../domain/entities/cashbook_entry.dart';

class FinanceService {
  static final SupabaseClient? _supabase = SupabaseConfig.client;

  // Get all cashbook entries
  static Future<List<CashbookEntry>> getCashbookEntries(
    BuildContext context, {
    CashbookType? type,
    PaymentCategory? category,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    int? offset,
    int? limit,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoCashbookEntries()
          .where((entry) {
            if (type != null && entry.type != type) return false;
            if (category != null && entry.category != category) return false;
            if (startDate != null && entry.entryDate.isBefore(startDate)) {
              return false;
            }
            if (endDate != null && entry.entryDate.isAfter(endDate)) {
              return false;
            }
            if (searchQuery != null && searchQuery.isNotEmpty) {
              final query = searchQuery.toLowerCase();
              return entry.description.toLowerCase().contains(query) ||
                  entry.entryNumber.toLowerCase().contains(query) ||
                  (entry.partnerName?.toLowerCase().contains(query) ?? false);
            }
            return true;
          })
          .skip(offset ?? 0)
          .take(limit ?? 50)
          .toList();
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      print('🔥 Loading finance entries from Supabase...');
      print('🔥 Filters: type=$type, category=$category, search=$searchQuery');

      var queryBuilder = _supabase!.from('payments').select('''
        id, payment_number, type, category, amount, description,
        partner_id, payment_date, created_at, updated_at, created_by,
        partners(id, name, type)
      ''');

      // Apply filters
      if (type != null) {
        // Map type to database values
        final dbType = type == CashbookType.receipt ? 'income' : 'expense';
        queryBuilder = queryBuilder.eq('type', dbType);
      }

      if (category != null) {
        queryBuilder = queryBuilder.eq('category', category.name);
      }

      if (startDate != null) {
        queryBuilder = queryBuilder.gte(
          'payment_date',
          startDate.toIso8601String().split('T')[0],
        );
      }

      if (endDate != null) {
        queryBuilder = queryBuilder.lte(
          'payment_date',
          endDate.toIso8601String().split('T')[0],
        );
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryBuilder = queryBuilder.or(
          'description.ilike.%$searchQuery%,payment_number.ilike.%$searchQuery%',
        );
      }

      // Order and execute
      final response = await queryBuilder.order(
        'payment_date',
        ascending: false,
      );

      print('🔥 Loaded ${response.length} finance entries from Supabase');

      return response.map<CashbookEntry>((data) {
        return CashbookEntry(
          id: data['id'],
          entryNumber: data['payment_number'],
          type: data['type'] == 'income'
              ? CashbookType.receipt
              : CashbookType.payment,
          category: PaymentCategory.values.firstWhere(
            (c) => c.name == data['category'],
          ),
          amount: (data['amount'] as num).toDouble(),
          partnerId: data['partner_id'],
          partnerName: data['partners']?['name'],
          description: data['description'],
          paymentMethod: PaymentMethod.cash,
          entryDate: DateTime.parse(data['payment_date']),
          createdAt: DateTime.parse(data['created_at']),
          updatedAt: DateTime.parse(data['updated_at']),
          createdBy: data['created_by'],
        );
      }).toList();
    } catch (e) {
      throw Exception(AppLocalizations.of(context).errorLoadingCashbookEntries(e.toString()));
    }
  }

  // Get cashbook entry by ID
  static Future<CashbookEntry?> getCashbookEntryById(BuildContext context, String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      try {
        return _getDemoCashbookEntries().firstWhere((entry) => entry.id == id);
      } catch (e) {
        throw Exception(AppLocalizations.of(context).errorLoadingCashbookEntry(e.toString()));
      }
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase!
          .from('payments')
          .select('''
          id, payment_number, type, category, amount, description,
          partner_id, payment_date, created_at, updated_at, created_by,
          partners(id, name, type)
        ''')
          .eq('id', id)
          .single();

      return CashbookEntry(
        id: response['id'],
        entryNumber: response['payment_number'],
        type: response['type'] == 'income'
            ? CashbookType.receipt
            : CashbookType.payment,
        category: PaymentCategory.values.firstWhere(
          (c) => c.name == response['category'],
        ),
        amount: (response['amount'] as num).toDouble(),
        partnerId: response['partner_id'],
        partnerName: response['partners']?['name'],
        description: response['description'],
        paymentMethod: PaymentMethod.cash,
        entryDate: DateTime.parse(response['payment_date']),
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['updated_at']),
        createdBy: response['created_by'],
      );
    } catch (e) {
      throw Exception(AppLocalizations.of(context).errorLoadingCashbookEntry(e.toString()));
    }
  }

  // Create new cashbook entry
  static Future<CashbookEntry> createCashbookEntry(BuildContext context, CashbookEntry entry) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate creation
      await Future.delayed(const Duration(milliseconds: 500));
      return entry.copyWith(
        id: 'entry_${DateTime.now().millisecondsSinceEpoch}',
        entryNumber: _generateEntryNumber(entry.type),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      // Debug: Print values being sent
      print('=== FINANCE DEBUG ===');
      print('Original Type: ${entry.type.value}');
      print('Category: ${entry.category.name}');
      print('Amount: ${entry.amount}');

      // Map type to database accepted values
      final dbType = entry.type == CashbookType.receipt ? 'income' : 'expense';
      print('Mapped DB Type: $dbType');

      final response = await _supabase!
          .from('payments')
          .insert({
            'payment_number': _generateEntryNumber(entry.type),
            'type': dbType, // Use mapped type instead of entry.type.value
            'category': entry.category.name,
            'amount': entry.amount,
            'description': entry.description,
            'partner_id': entry.partnerId,
            'payment_date': entry.entryDate.toIso8601String().split('T')[0],
            'payment_method': 'cash', // Default payment method
          })
          .select('''
        id, payment_number, type, category, amount, description,
        partner_id, payment_date, created_at, updated_at, created_by,
        partners(id, name, type)
      ''')
          .single();

      return CashbookEntry(
        id: response['id'],
        entryNumber: response['payment_number'],
        type: response['type'] == 'income'
            ? CashbookType.receipt
            : CashbookType.payment,
        category: PaymentCategory.values.firstWhere(
          (c) => c.name == response['category'],
        ),
        amount: (response['amount'] as num).toDouble(),
        partnerId: response['partner_id'],
        partnerName: response['partners']?['name'],
        description: response['description'],
        paymentMethod: PaymentMethod.cash,
        entryDate: DateTime.parse(response['payment_date']),
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['updated_at']),
        createdBy: response['created_by'],
      );
    } catch (e) {
      throw Exception(AppLocalizations.of(context).errorCreatingCashbookEntry(e.toString()));
    }
  }

  // Update cashbook entry
  static Future<CashbookEntry> updateCashbookEntry(BuildContext context, CashbookEntry entry) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate update
      await Future.delayed(const Duration(milliseconds: 500));
      return entry.copyWith(updatedAt: DateTime.now());
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      // Map type to database accepted values
      final dbType = entry.type == CashbookType.receipt ? 'income' : 'expense';

      final response = await _supabase!
          .from('payments')
          .update({
            'type': dbType, // Use mapped type
            'category': entry.category.name,
            'amount': entry.amount,
            'description': entry.description,
            'partner_id': entry.partnerId,
            'payment_date': entry.entryDate.toIso8601String().split('T')[0],
          })
          .eq('id', entry.id)
          .select('''
        id, payment_number, type, category, amount, description,
        partner_id, payment_date, created_at, updated_at, created_by,
        partners(id, name, type)
      ''')
          .single();

      return CashbookEntry(
        id: response['id'],
        entryNumber: response['payment_number'],
        type: response['type'] == 'income'
            ? CashbookType.receipt
            : CashbookType.payment,
        category: PaymentCategory.values.firstWhere(
          (c) => c.name == response['category'],
        ),
        amount: (response['amount'] as num).toDouble(),
        partnerId: response['partner_id'],
        partnerName: response['partners']?['name'],
        description: response['description'],
        paymentMethod: PaymentMethod.cash,
        entryDate: DateTime.parse(response['payment_date']),
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['updated_at']),
        createdBy: response['created_by'],
      );
    } catch (e) {
      throw Exception(AppLocalizations.of(context).errorUpdatingCashbookEntry(e.toString()));
    }
  }

  // Delete cashbook entry
  static Future<void> deleteCashbookEntry(BuildContext context, String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate deletion
      await Future.delayed(const Duration(milliseconds: 500));
      return;
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      await _supabase!.from('payments').delete().eq('id', id);
    } catch (e) {
      throw Exception(AppLocalizations.of(context).errorDeletingCashbookEntry(e.toString()));
    }
  }

  // Get cash balance
  static Future<CashBalance> getCashBalance(BuildContext context) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate balance calculation
      await Future.delayed(const Duration(milliseconds: 500));
      return _getDemoCashBalance();
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase!
          .from('payments')
          .select('type, amount')
          .order('payment_date', ascending: false);

      double totalReceipts = 0;
      double totalPayments = 0;

      for (final entry in response) {
        if (entry['type'] == 'income') {
          totalReceipts += (entry['amount'] as num).toDouble();
        } else {
          totalPayments += (entry['amount'] as num).toDouble();
        }
      }

      return CashBalance(
        openingBalance: 0, // TODO: Implement opening balance
        totalReceipts: totalReceipts,
        totalPayments: totalPayments,
        closingBalance: totalReceipts - totalPayments,
        asOfDate: DateTime.now(),
      );
    } catch (e) {
      throw Exception(AppLocalizations.of(context).errorLoadingCashBalance(e.toString()));
    }
  }

  // Get finance stats
  static Future<Map<String, dynamic>> getFinanceStats(BuildContext context) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate stats
      await Future.delayed(const Duration(milliseconds: 500));
      return _getDemoFinanceStats();
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase!
          .from('payments')
          .select('type, category, amount')
          .order('payment_date', ascending: false);

      final stats = <String, dynamic>{
        'totalReceipts': 0.0,
        'totalPayments': 0.0,
        'categoryStats': <String, double>{},
      };

      for (final entry in response) {
        final amount = (entry['amount'] as num).toDouble();
        final category = entry['category'] as String;

        if (entry['type'] == 'income') {
          stats['totalReceipts'] = (stats['totalReceipts'] as double) + amount;
        } else {
          stats['totalPayments'] = (stats['totalPayments'] as double) + amount;
        }

        // Update category stats
        if (!stats['categoryStats'].containsKey(category)) {
          stats['categoryStats'][category] = 0.0;
        }
        stats['categoryStats'][category] = (stats['categoryStats'][category] as double) + amount;
      }

      return stats;
    } catch (e) {
      throw Exception(AppLocalizations.of(context).errorLoadingFinanceStats(e.toString()));
    }
  }

  // Helper methods for demo mode
  static List<CashbookEntry> _getDemoCashbookEntries() {
    return [
      CashbookEntry(
        id: '1',
        entryNumber: 'R001',
        type: CashbookType.receipt,
        category: PaymentCategory.sales,
        amount: 1000000,
        description: 'Demo receipt 1',
        paymentMethod: PaymentMethod.cash,
        entryDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      CashbookEntry(
        id: '2',
        entryNumber: 'P001',
        type: CashbookType.payment,
        category: PaymentCategory.expense,
        amount: 500000,
        description: 'Demo payment 1',
        paymentMethod: PaymentMethod.cash,
        entryDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  static CashBalance _getDemoCashBalance() {
    return CashBalance(
      openingBalance: 0,
      totalReceipts: 1000000,
      totalPayments: 500000,
      closingBalance: 500000,
      asOfDate: DateTime.now(),
    );
  }

  static Map<String, dynamic> _getDemoFinanceStats() {
    return {
      'totalReceipts': 1000000.0,
      'totalPayments': 500000.0,
      'categoryStats': {
        'sales': 1000000.0,
        'expense': 500000.0,
      },
    };
  }

  static String _generateEntryNumber(CashbookType type) {
    final prefix = type == CashbookType.receipt ? 'R' : 'P';
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    return '$prefix${timestamp.substring(timestamp.length - 6)}';
  }
}
