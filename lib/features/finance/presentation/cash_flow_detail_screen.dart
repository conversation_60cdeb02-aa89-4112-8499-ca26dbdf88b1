import 'package:city_pos/generated/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../data/services/finance_service.dart';
import '../domain/entities/cashbook_entry.dart';

class CashFlowDetailScreen extends ConsumerStatefulWidget {
  final String cashFlowId;

  const CashFlowDetailScreen({super.key, required this.cashFlowId});

  @override
  ConsumerState<CashFlowDetailScreen> createState() =>
      _CashFlowDetailScreenState();
}

class _CashFlowDetailScreenState extends ConsumerState<CashFlowDetailScreen> {
  CashbookEntry? _cashFlow;
  bool _isLoading = true;
  bool _isEditing = false;
  bool _isSaving = false;
  String? _error;

  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCashFlow();
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _loadCashFlow() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final cashFlow = await FinanceService.getCashbookEntryById(
        context,
        widget.cashFlowId,
      );
      if (mounted) {
        setState(() {
          _cashFlow = cashFlow;
          _descriptionController.text = cashFlow?.description ?? '';
          _amountController.text = cashFlow?.amount.toString() ?? '0';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive(context);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _cashFlow?.type == CashbookType.receipt
              ? l10n.receiptDetails
              : l10n.paymentDetails,
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.finance),
        ),
        actions: [
          if (_cashFlow != null && !_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
              tooltip: l10n.edit,
            ),
          if (_isEditing)
            TextButton(
              onPressed: _isSaving ? null : _saveChanges,
              child: _isSaving
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(l10n.save),
            ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _showDeleteConfirmation();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    const Icon(Icons.delete, color: Colors.red),
                    const SizedBox(width: 8),
                    Text(l10n.deleteReceipt),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildBody(responsive),
    );
  }

  Widget _buildBody(Responsive responsive) {
    final l10n = AppLocalizations.of(context);

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppTheme.errorColor),
            const SizedBox(height: 16),
            Text(
              l10n.errorOccurred,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadCashFlow,
              child: Text(l10n.tryAgain),
            ),
          ],
        ),
      );
    }

    if (_cashFlow == null) {
      return Center(child: Text(l10n.receiptNotFound));
    }

    return responsive.isMobile ? _buildMobileLayout() : _buildDesktopLayout();
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildCashFlowInfo(),
          const SizedBox(height: 16),
          _buildPartnerInfo(),
          if (_isEditing) ...[
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(flex: 2, child: _buildCashFlowInfo()),
                  const SizedBox(width: 24),
                  Expanded(child: _buildPartnerInfo()),
                ],
              ),
              if (_isEditing) ...[
                const SizedBox(height: 32),
                Center(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: _buildActionButtons(),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCashFlowInfo() {
    final l10n = AppLocalizations.of(context);
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.receiptInfo,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              l10n.receiptType,
              _cashFlow!.type == CashbookType.receipt
                  ? l10n.receipt
                  : l10n.payment,
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              l10n.amount,
              currencyFormat.format(_cashFlow!.amount),
            ),
            const SizedBox(height: 8),
            _buildInfoRow(l10n.date, dateFormat.format(_cashFlow!.entryDate)),
            const SizedBox(height: 8),
            _buildInfoRow(l10n.description, _cashFlow!.description),
            const SizedBox(height: 8),
            _buildInfoRow(
              l10n.paymentMethod,
              _getPaymentMethodLabel(_cashFlow!.paymentMethod.toString()),
            ),
            const SizedBox(height: 8),
            _buildInfoRow(l10n.status, _getStatusLabel(_cashFlow!.status)),
          ],
        ),
      ),
    );
  }

  Widget _buildPartnerInfo() {
    final l10n = AppLocalizations.of(context);
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.partnerInfo,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_cashFlow!.partnerId != null) ...[
              _buildInfoRow(
                l10n.partnerName,
                _cashFlow!.partnerName ?? l10n.unknown,
              ),
              const SizedBox(height: 8),
              _buildInfoRow(
                l10n.phone,
                _cashFlow!.partnerPhone ?? l10n.unknown,
              ),
              const SizedBox(height: 8),
              _buildInfoRow(
                l10n.address,
                _cashFlow!.partnerAddress ?? l10n.unknown,
              ),
            ] else
              Text(
                l10n.noPartnerInfo,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final l10n = AppLocalizations.of(context);
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              setState(() {
                _isEditing = false;
                _descriptionController.text = _cashFlow?.description ?? '';
                _amountController.text = _cashFlow?.amount.toString() ?? '0';
              });
            },
            child: Text(l10n.cancel),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isSaving ? null : _saveChanges,
            child: _isSaving
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(l10n.save),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ),
        Expanded(
          child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
        ),
      ],
    );
  }

  String _getPaymentMethodLabel(String method) {
    final l10n = AppLocalizations.of(context);
    switch (method) {
      case 'cash':
        return l10n.cash;
      case 'bank_transfer':
        return l10n.bankTransfer;
      case 'credit_card':
        return l10n.creditCard;
      case 'debit_card':
        return l10n.debitCard;
      case 'e_wallet':
        return l10n.eWallet;
      default:
        return method;
    }
  }

  String _getStatusLabel(String status) {
    final l10n = AppLocalizations.of(context);
    switch (status) {
      case 'draft':
        return l10n.draft;
      case 'pending':
        return l10n.pending;
      case 'completed':
        return l10n.completed;
      case 'cancelled':
        return l10n.cancelled;
      default:
        return status;
    }
  }

  Future<void> _saveChanges() async {
    if (_cashFlow == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final updatedCashFlow = _cashFlow!.copyWith(
        description: _descriptionController.text,
        amount: double.tryParse(_amountController.text) ?? 0,
        updatedAt: DateTime.now(),
      );

      await FinanceService.updateCashbookEntry(context, updatedCashFlow);

      if (mounted) {
        setState(() {
          _cashFlow = updatedCashFlow;
          _isEditing = false;
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).receiptUpdated),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context).errorUpdatingReceipt}: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showDeleteConfirmation() async {
    final l10n = AppLocalizations.of(context);
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deleteReceipt),
        content: Text(l10n.deleteReceiptConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _deleteCashFlow();
    }
  }

  Future<void> _deleteCashFlow() async {
    if (_cashFlow == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await FinanceService.deleteCashbookEntry(context, _cashFlow!.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).receiptDeleted),
            backgroundColor: Colors.green,
          ),
        );
        context.go(AppRoutes.finance);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = e.toString();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context).errorDeletingReceipt}: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
