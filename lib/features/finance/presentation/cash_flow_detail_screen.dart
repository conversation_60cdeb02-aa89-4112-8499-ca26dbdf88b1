import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../data/services/finance_service.dart';
import '../domain/entities/cashbook_entry.dart';

class CashFlowDetailScreen extends ConsumerStatefulWidget {
  final String cashFlowId;

  const CashFlowDetailScreen({super.key, required this.cashFlowId});

  @override
  ConsumerState<CashFlowDetailScreen> createState() =>
      _CashFlowDetailScreenState();
}

class _CashFlowDetailScreenState extends ConsumerState<CashFlowDetailScreen> {
  CashbookEntry? _cashFlow;
  bool _isLoading = true;
  bool _isEditing = false;
  bool _isSaving = false;
  String? _error;

  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCashFlow();
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _loadCashFlow() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final cashFlow = await FinanceService.getCashbookEntryById(
        widget.cashFlowId,
      );
      if (mounted) {
        setState(() {
          _cashFlow = cashFlow;
          _descriptionController.text = cashFlow?.description ?? '';
          _amountController.text = cashFlow?.amount.toString() ?? '0';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _cashFlow?.type == CashbookType.receipt
              ? 'Chi tiết phiếu thu'
              : 'Chi tiết phiếu chi',
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.finance),
        ),
        actions: [
          if (_cashFlow != null && !_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
              tooltip: 'Chỉnh sửa',
            ),
          if (_isEditing)
            TextButton(
              onPressed: _isSaving ? null : _saveChanges,
              child: _isSaving
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Lưu'),
            ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _showDeleteConfirmation();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Xóa phiếu'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildBody(responsive),
    );
  }

  Widget _buildBody(Responsive responsive) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppTheme.errorColor),
            const SizedBox(height: 16),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadCashFlow,
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (_cashFlow == null) {
      return const Center(child: Text('Không tìm thấy phiếu'));
    }

    return responsive.isMobile ? _buildMobileLayout() : _buildDesktopLayout();
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildCashFlowInfo(),
          const SizedBox(height: 16),
          _buildPartnerInfo(),
          if (_isEditing) ...[
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(flex: 2, child: _buildCashFlowInfo()),
                  const SizedBox(width: 24),
                  Expanded(child: _buildPartnerInfo()),
                ],
              ),
              if (_isEditing) ...[
                const SizedBox(height: 32),
                Center(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: _buildActionButtons(),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCashFlowInfo() {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin phiếu',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              'Loại phiếu',
              _cashFlow!.type == CashbookType.receipt
                  ? 'Phiếu thu'
                  : 'Phiếu chi',
              valueColor: _cashFlow!.type == CashbookType.receipt
                  ? Colors.green
                  : Colors.red,
            ),
            _buildInfoRow('Ngày tạo', dateFormat.format(_cashFlow!.createdAt)),
            _buildInfoRow('Người tạo', _cashFlow!.createdBy ?? 'N/A'),
            const SizedBox(height: 16),
            Text(
              'Số tiền',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            if (_isEditing)
              TextField(
                controller: _amountController,
                decoration: const InputDecoration(
                  hintText: 'Nhập số tiền...',
                  border: OutlineInputBorder(),
                  suffixText: '₫',
                ),
                keyboardType: TextInputType.number,
              )
            else
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _cashFlow!.type == CashbookType.receipt
                      ? Colors.green[50]
                      : Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _cashFlow!.type == CashbookType.receipt
                        ? Colors.green[300]!
                        : Colors.red[300]!,
                  ),
                ),
                child: Text(
                  currencyFormat.format(_cashFlow!.amount),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _cashFlow!.type == CashbookType.receipt
                        ? Colors.green[700]
                        : Colors.red[700],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            const SizedBox(height: 16),
            Text(
              'Mô tả',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            if (_isEditing)
              TextField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  hintText: 'Nhập mô tả...',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              )
            else
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  _cashFlow!.description.isNotEmpty
                      ? _cashFlow!.description
                      : 'Không có mô tả',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: _cashFlow!.description.isNotEmpty
                        ? null
                        : Colors.grey[600],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPartnerInfo() {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin đối tác',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_cashFlow!.partnerName != null)
              _buildInfoRow('Tên đối tác', _cashFlow!.partnerName!)
            else
              const Text(
                'Không có đối tác',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
              ),
            ...[
              const SizedBox(height: 8),
              _buildInfoRow('Danh mục', _cashFlow!.category.displayName),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: valueColor,
                fontWeight: valueColor != null ? FontWeight.w500 : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isSaving
                ? null
                : () {
                    setState(() {
                      _isEditing = false;
                      _descriptionController.text = _cashFlow!.description;
                      _amountController.text = _cashFlow!.amount.toString();
                    });
                  },
            child: const Text('Hủy'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isSaving ? null : _saveChanges,
            child: _isSaving
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Lưu thay đổi'),
          ),
        ),
      ],
    );
  }

  Future<void> _saveChanges() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final amount = double.tryParse(_amountController.text) ?? 0;

      final updatedEntry = _cashFlow!.copyWith(
        description: _descriptionController.text.trim(),
        amount: amount,
      );

      await FinanceService.updateCashbookEntry(_cashFlow!.id, updatedEntry);

      if (mounted) {
        setState(() {
          _cashFlow = updatedEntry;
          _isEditing = false;
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).entryUpdatedSuccessfully,
            ),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context).errorUpdatingEntry}: $e',
            ),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text(
          'Bạn có chắc chắn muốn xóa phiếu ${_cashFlow!.type == CashbookType.receipt ? 'thu' : 'chi'} này?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteCashFlow();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteCashFlow() async {
    try {
      await FinanceService.deleteCashbookEntry(_cashFlow!.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã xóa phiếu thành công'),
            backgroundColor: AppTheme.successColor,
          ),
        );
        context.go(AppRoutes.finance);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi xóa phiếu: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }
}
