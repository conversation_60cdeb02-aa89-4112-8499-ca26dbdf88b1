import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/utils/responsive.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../domain/entities/cashbook_entry.dart';
import 'widgets/add_transaction_dialog.dart';
import 'widgets/cash_flow_chart.dart';
import 'widgets/finance_filters_widget.dart';
import 'widgets/financial_summary_card.dart';
import 'widgets/transaction_list_widget.dart';

final financeProvider = StateNotifierProvider<FinanceNotifier, FinanceState>((
  ref,
) {
  return FinanceNotifier();
});

class FinanceState {
  final bool isLoading;
  final String? error;
  final List<CashbookEntry> entries;
  final CashBalance? balance;
  final Map<String, dynamic> stats;
  final DateTime selectedDate;
  final TransactionType? selectedType;
  final DateTime? startDate;
  final DateTime? endDate;

  const FinanceState({
    this.isLoading = false,
    this.error,
    this.entries = const [],
    this.balance,
    this.stats = const {},
    required this.selectedDate,
    this.selectedType,
    this.startDate,
    this.endDate,
  });

  FinanceState copyWith({
    bool? isLoading,
    String? error,
    List<CashbookEntry>? entries,
    CashBalance? balance,
    Map<String, dynamic>? stats,
    DateTime? selectedDate,
    TransactionType? selectedType,
    DateTime? startDate,
    DateTime? endDate,
    bool clearSelectedType = false,
    bool clearStartDate = false,
    bool clearEndDate = false,
  }) {
    return FinanceState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      entries: entries ?? this.entries,
      balance: balance ?? this.balance,
      stats: stats ?? this.stats,
      selectedDate: selectedDate ?? this.selectedDate,
      selectedType: clearSelectedType
          ? null
          : (selectedType ?? this.selectedType),
      startDate: clearStartDate ? null : (startDate ?? this.startDate),
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
    );
  }
}

class FinanceNotifier extends StateNotifier<FinanceState> {
  FinanceNotifier() : super(FinanceState(selectedDate: DateTime.now())) {
    loadData();
  }

  Future<void> loadData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Demo data - replace with actual service calls
      final entries = <CashbookEntry>[];
      final stats = <String, dynamic>{
        'totalIncome': 5000000.0,
        'totalExpense': 3000000.0,
        'balance': 2000000.0,
      };

      state = state.copyWith(isLoading: false, entries: entries, stats: stats);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> addEntry(CashbookEntry entry) async {
    try {
      // Demo - add to local list
      final newEntries = [...state.entries, entry];
      state = state.copyWith(entries: newEntries);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  void setSelectedDate(DateTime date) {
    state = state.copyWith(selectedDate: date);
    loadData();
  }

  void setFilters({
    TransactionType? type,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    state = state.copyWith(
      selectedType: type,
      startDate: startDate,
      endDate: endDate,
    );
    loadData();
  }

  void clearFilters() {
    state = state.copyWith(
      clearSelectedType: true,
      clearStartDate: true,
      clearEndDate: true,
    );
    loadData();
  }
}

class FinanceScreen extends ConsumerWidget {
  const FinanceScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final state = ref.watch(financeProvider);
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/dashboard'),
        ),
        title: Text(l10n.financeTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddTransactionDialog(context, ref),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(financeProvider.notifier).loadData(),
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
          ? AppErrorWidget(
              message: state.error!,
              onRetry: () => ref.read(financeProvider.notifier).loadData(),
            )
          : _buildContent(context, ref, state, responsive, l10n),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    FinanceState state,
    Responsive responsive,
    AppLocalizations l10n,
  ) {
    if (responsive.isMobile) {
      return _buildMobileLayout(context, ref, state, l10n);
    } else {
      return _buildDesktopLayout(context, ref, state, responsive, l10n);
    }
  }

  Widget _buildMobileLayout(
    BuildContext context,
    WidgetRef ref,
    FinanceState state,
    AppLocalizations l10n,
  ) {
    final responsive = Responsive(context);

    return SingleChildScrollView(
      padding: EdgeInsets.all(responsive.isMobile ? 8.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Compact filter for mobile
          FinanceFiltersWidget(
            selectedType: state.selectedType,
            startDate: state.startDate,
            endDate: state.endDate,
            onFiltersChanged: (type, startDate, endDate) {
              ref
                  .read(financeProvider.notifier)
                  .setFilters(
                    type: type,
                    startDate: startDate,
                    endDate: endDate,
                  );
            },
            onClearFilters: () {
              ref.read(financeProvider.notifier).clearFilters();
            },
          ),
          SizedBox(height: responsive.isMobile ? 8.0 : 16.0),
          FinancialSummaryCard(summary: state.stats),
          SizedBox(height: responsive.isMobile ? 8.0 : 16.0),
          CashFlowChart(transactions: const []),
          SizedBox(height: responsive.isMobile ? 8.0 : 16.0),
          TransactionListWidget(transactions: const []),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    WidgetRef ref,
    FinanceState state,
    Responsive responsive,
    AppLocalizations l10n,
  ) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Desktop filter
          FinanceFiltersWidget(
            selectedType: state.selectedType,
            startDate: state.startDate,
            endDate: state.endDate,
            onFiltersChanged: (type, startDate, endDate) {
              ref
                  .read(financeProvider.notifier)
                  .setFilters(
                    type: type,
                    startDate: startDate,
                    endDate: endDate,
                  );
            },
            onClearFilters: () {
              ref.read(financeProvider.notifier).clearFilters();
            },
          ),
          const SizedBox(height: 24),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    FinancialSummaryCard(summary: state.stats),
                    const SizedBox(height: 16),
                    CashFlowChart(transactions: const []),
                  ],
                ),
              ),
              const SizedBox(width: 24),
              Expanded(
                flex: 3,
                child: TransactionListWidget(transactions: const []),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showAddTransactionDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AddTransactionDialog(
        onAdd: (transaction) {
          // Convert Transaction to CashbookEntry if needed
          ref.read(financeProvider.notifier).loadData();
        },
      ),
    );
  }
}
