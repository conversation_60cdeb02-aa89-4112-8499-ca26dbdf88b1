import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../generated/l10n/app_localizations.dart';
import '../../domain/entities/transaction.dart';

class AddTransactionDialog extends StatefulWidget {
  final Function(Transaction) onAdd;

  const AddTransactionDialog({super.key, required this.onAdd});

  @override
  State<AddTransactionDialog> createState() => _AddTransactionDialogState();
}

class _AddTransactionDialogState extends State<AddTransactionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedType = 'income';
  String _selectedCategory = '';
  String _selectedPaymentMethod = 'cash';
  DateTime _selectedDate = DateTime.now();

  List<String> _categories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() => _isLoading = true);

    try {
      // Demo categories
      final categories = _selectedType == 'income'
          ? ['Bán hàng', 'Dịch vụ', 'Lãi suất', 'Khác']
          : [
              'Nhập hàng',
              'Tiền lương',
              'Tiền thuê',
              'Điện nước',
              'Marketing',
              'Khác',
            ];

      setState(() {
        _categories = categories;
        _selectedCategory = categories.isNotEmpty ? categories.first : '';
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return AlertDialog(
      title: Text(l10n.addTransactionTitle),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTypeSelector(),
                const SizedBox(height: 16),
                _buildDescriptionField(),
                const SizedBox(height: 16),
                _buildAmountField(),
                const SizedBox(height: 16),
                _buildCategoryDropdown(),
                const SizedBox(height: 16),
                _buildPaymentMethodDropdown(),
                const SizedBox(height: 16),
                _buildDateSelector(),
                const SizedBox(height: 16),
                _buildNotesField(),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Hủy'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleSubmit,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Thêm'),
        ),
      ],
    );
  }

  Widget _buildTypeSelector() {
    return Row(
      children: [
        Expanded(
          child: RadioListTile<String>(
            title: const Text('Thu'),
            value: 'income',
            groupValue: _selectedType,
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
              });
              _loadCategories();
            },
          ),
        ),
        Expanded(
          child: RadioListTile<String>(
            title: const Text('Chi'),
            value: 'expense',
            groupValue: _selectedType,
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
              });
              _loadCategories();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: 'Mô tả',
        border: OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Vui lòng nhập mô tả';
        }
        return null;
      },
    );
  }

  Widget _buildAmountField() {
    return TextFormField(
      controller: _amountController,
      decoration: const InputDecoration(
        labelText: 'Số tiền',
        border: OutlineInputBorder(),
        suffixText: '₫',
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Vui lòng nhập số tiền';
        }
        final amount = double.tryParse(value);
        if (amount == null || amount <= 0) {
          return 'Vui lòng nhập số tiền hợp lệ';
        }
        return null;
      },
    );
  }

  Widget _buildCategoryDropdown() {
    if (_isLoading) {
      return const CircularProgressIndicator();
    }

    return DropdownButtonFormField<String>(
      value: _selectedCategory.isNotEmpty ? _selectedCategory : null,
      decoration: const InputDecoration(
        labelText: 'Danh mục',
        border: OutlineInputBorder(),
      ),
      items: _categories.map((category) {
        return DropdownMenuItem(value: category, child: Text(category));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCategory = value!;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Vui lòng chọn danh mục';
        }
        return null;
      },
    );
  }

  Widget _buildPaymentMethodDropdown() {
    final paymentMethods = [
      {'value': 'cash', 'label': 'Tiền mặt'},
      {'value': 'card', 'label': 'Thẻ'},
      {'value': 'bank_transfer', 'label': 'Chuyển khoản'},
    ];

    return DropdownButtonFormField<String>(
      value: _selectedPaymentMethod,
      decoration: const InputDecoration(
        labelText: 'Phương thức thanh toán',
        border: OutlineInputBorder(),
      ),
      items: paymentMethods.map((method) {
        return DropdownMenuItem(
          value: method['value'],
          child: Text(method['label']!),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedPaymentMethod = value!;
        });
      },
    );
  }

  Widget _buildDateSelector() {
    return InkWell(
      onTap: _selectDate,
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'Ngày',
          border: OutlineInputBorder(),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
            ),
            const Icon(Icons.calendar_today),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: const InputDecoration(
        labelText: 'Ghi chú (tùy chọn)',
        border: OutlineInputBorder(),
      ),
      maxLines: 3,
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  void _handleSubmit() {
    if (_formKey.currentState!.validate()) {
      final transaction = Transaction(
        id: '', // Will be generated by service
        type: _selectedType,
        category: _selectedCategory,
        amount: double.parse(_amountController.text),
        description: _descriptionController.text,
        paymentMethod: _selectedPaymentMethod,
        date: _selectedDate,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      widget.onAdd(transaction);
      Navigator.of(context).pop();
    }
  }
}
