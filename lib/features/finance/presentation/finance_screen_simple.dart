import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:city_pos/generated/l10n/app_localizations.dart';
import 'package:city_pos/core/themes/app_theme.dart';
import 'package:city_pos/features/finance/domain/entities/cashbook_entry.dart';
import 'package:city_pos/features/finance/data/services/finance_service.dart';
import 'add_finance_transaction_screen.dart';
import 'cash_flow_detail_screen.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';

// Simple Finance State
class FinanceSimpleState {
  final List<CashbookEntry> entries;
  final CashBalance? balance;
  final bool isLoading;
  final String? error;
  final List<PaymentCategory> categories;
  final List<PaymentMethod> paymentMethods;

  const FinanceSimpleState({
    this.entries = const [],
    this.balance,
    this.isLoading = false,
    this.error,
    this.categories = const [],
    this.paymentMethods = const [],
  });

  FinanceSimpleState copyWith({
    List<CashbookEntry>? entries,
    CashBalance? balance,
    bool? isLoading,
    String? error,
    List<PaymentCategory>? categories,
    List<PaymentMethod>? paymentMethods,
  }) {
    return FinanceSimpleState(
      entries: entries ?? this.entries,
      balance: balance ?? this.balance,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      categories: categories ?? this.categories,
      paymentMethods: paymentMethods ?? this.paymentMethods,
    );
  }
}

// Finance Notifier
class FinanceSimpleNotifier extends StateNotifier<FinanceSimpleState> {
  FinanceSimpleNotifier() : super(const FinanceSimpleState());

  Future<void> loadData(BuildContext context) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final entries = await FinanceService.getCashbookEntries(context);
      final balance = await FinanceService.getCashBalance(context);
      final categories = PaymentCategory.values;
      final paymentMethods = PaymentMethod.values;

      state = state.copyWith(
        entries: entries,
        balance: balance,
        categories: categories,
        paymentMethods: paymentMethods,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: AppLocalizations.of(context).errorLoadingData.toString(),
      );
    }
  }

  Future<void> createEntry(BuildContext context, CashbookEntry entry) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final newEntry = await FinanceService.createCashbookEntry(context, entry);
      state = state.copyWith(
        entries: [newEntry, ...state.entries],
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: AppLocalizations.of(context).errorLoadingData.toString(),
      );
    }
  }

  Future<void> updateEntry(BuildContext context, CashbookEntry entry) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final updatedEntry = await FinanceService.updateCashbookEntry(context, entry);
      state = state.copyWith(
        entries: state.entries.map((e) => e.id == entry.id ? updatedEntry : e).toList(),
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: AppLocalizations.of(context).errorLoadingData.toString(),
      );
    }
  }

  Future<void> deleteEntry(BuildContext context, String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await FinanceService.deleteCashbookEntry(context, id);
      state = state.copyWith(
        entries: state.entries.where((e) => e.id != id).toList(),
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: AppLocalizations.of(context).errorLoadingData.toString(),
      );
    }
  }
}

// Provider
final financeSimpleProvider =
    StateNotifierProvider<FinanceSimpleNotifier, FinanceSimpleState>((ref) {
  return FinanceSimpleNotifier();
});

class FinanceScreenSimple extends ConsumerWidget {
  const FinanceScreenSimple({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(financeSimpleProvider);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: Text(l10n.cashBook),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddEntryDialog(context, ref),
            tooltip: l10n.addReceipt,
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
              ? AppErrorWidget(
                  message: state.error!,
                  onRetry: () => ref.read(financeSimpleProvider.notifier).loadData(context),
                )
              : RefreshIndicator(
                  onRefresh: () => ref
                      .read(financeSimpleProvider.notifier)
                      .loadData(context),
                  child: ListView(
                    padding: const EdgeInsets.all(AppTheme.spacingM),
                    children: [
                      // Cash Balance Card
                      AppCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              l10n.cashBalance,
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: AppTheme.spacingS),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  l10n.balance,
                                  style: Theme.of(context).textTheme.bodyLarge,
                                ),
                                Text(
                                  '\$${state.balance?.balance.toStringAsFixed(2) ?? '0.00'}',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                        color: state.balance?.balance ?? 0 >= 0
                                            ? Colors.green
                                            : Colors.red,
                                      ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingM),

                      // Entries List
                      if (state.entries.isEmpty)
                        Center(
                          child: Text(
                            l10n.noReceipts,
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        )
                      else
                        Expanded(
                          child: ListView.builder(
                            itemCount: state.entries.length,
                            itemBuilder: (context, index) {
                              final entry = state.entries[index];
                              return ListTile(
                                title: Text(entry.description),
                                subtitle: Text(
                                  '${entry.getDisplayName(context)} - ${entry.getCategoryDisplayName(context)}',
                                ),
                                trailing: Text(
                                  '\$${entry.amount.toStringAsFixed(2)}',
                                  style: TextStyle(
                                    color: entry.type == CashbookType.receipt
                                        ? Colors.green
                                        : Colors.red,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                onTap: () => _showEntryDetails(context, ref, entry),
                              );
                            },
                          ),
                        ),
                    ],
                  ),
                ),
    );
  }

  void _showAddEntryDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => const AddFinanceTransactionScreen(),
    );
  }

  void _showEntryDetails(BuildContext context, WidgetRef ref, CashbookEntry entry) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CashFlowDetailScreen(
          cashFlowId: entry.id,
        ),
      ),
    );
  }
}
