import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../data/services/finance_service.dart';
import '../domain/entities/cashbook_entry.dart';

// Simple Finance State
class FinanceSimpleState {
  final bool isLoading;
  final String? error;
  final List<CashbookEntry> entries;
  final CashBalance? balance;
  final Map<String, dynamic> stats;
  final CashbookType? filterType;
  final String searchQuery;

  const FinanceSimpleState({
    this.isLoading = false,
    this.error,
    this.entries = const [],
    this.balance,
    this.stats = const {},
    this.filterType,
    this.searchQuery = '',
  });

  FinanceSimpleState copyWith({
    bool? isLoading,
    String? error,
    List<CashbookEntry>? entries,
    CashBalance? balance,
    Map<String, dynamic>? stats,
    CashbookType? filterType,
    String? searchQuery,
  }) {
    return FinanceSimpleState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      entries: entries ?? this.entries,
      balance: balance ?? this.balance,
      stats: stats ?? this.stats,
      filterType: filterType ?? this.filterType,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

// Finance Notifier
class FinanceSimpleNotifier extends StateNotifier<FinanceSimpleState> {
  FinanceSimpleNotifier() : super(const FinanceSimpleState()) {
    loadData();
  }

  Future<void> loadData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final entries = await FinanceService.getCashbookEntries(
        type: state.filterType,
        searchQuery: state.searchQuery.isNotEmpty ? state.searchQuery : null,
      );
      final balance = await FinanceService.getCashBalance();
      final stats = await FinanceService.getFinanceStats();

      state = state.copyWith(
        isLoading: false,
        entries: entries,
        balance: balance,
        stats: stats,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void setFilter(CashbookType? type) {
    state = state.copyWith(filterType: type);
    loadData();
  }

  void searchEntries(String query) {
    state = state.copyWith(searchQuery: query);
    loadData();
  }

  Future<void> createEntry(CashbookEntry entry) async {
    try {
      await FinanceService.createCashbookEntry(entry);
      await loadData();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> updateEntry(String id, CashbookEntry entry) async {
    try {
      await FinanceService.updateCashbookEntry(id, entry);
      await loadData();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deleteEntry(String id) async {
    try {
      await FinanceService.deleteCashbookEntry(id);
      await loadData();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }
}

// Provider
final financeSimpleProvider =
    StateNotifierProvider<FinanceSimpleNotifier, FinanceSimpleState>(
      (ref) => FinanceSimpleNotifier(),
    );

class FinanceScreenSimple extends ConsumerWidget {
  const FinanceScreenSimple({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(financeSimpleProvider);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: const Text('Sổ quỹ'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.go(AppRoutes.addTransaction),
            tooltip: 'Thêm phiếu thu/chi',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () =>
                ref.read(financeSimpleProvider.notifier).loadData(),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
          ? AppErrorWidget(
              message: state.error!,
              onRetry: () =>
                  ref.read(financeSimpleProvider.notifier).loadData(),
            )
          : _buildContent(context, ref, state),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    FinanceSimpleState state,
  ) {
    return Column(
      children: [
        // Cash Balance Summary
        _buildCashBalanceSummary(context, state),

        // Filters and Search
        _buildFiltersAndSearch(context, ref, state),

        // Entries List
        Expanded(child: _buildEntriesList(context, ref, state)),
      ],
    );
  }

  Widget _buildCashBalanceSummary(
    BuildContext context,
    FinanceSimpleState state,
  ) {
    final balance = state.balance;

    if (balance == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // If mobile screen, show hamburger menu
          if (MediaQuery.of(context).size.width < 768) {
            return _buildMobileBalanceSummary(context, balance);
          } else {
            return _buildDesktopBalanceSummary(context, balance);
          }
        },
      ),
    );
  }

  Widget _buildMobileBalanceSummary(BuildContext context, CashBalance balance) {
    return Row(
      children: [
        // Main balance card (most important)
        Expanded(
          flex: 3,
          child: _buildBalanceCard(
            context,
            'Tồn quỹ',
            balance.formattedClosingBalance,
            Icons.account_balance_wallet,
            balance.closingBalance >= 0 ? Colors.blue : Colors.red,
          ),
        ),
        const SizedBox(width: 12),
        // Hamburger menu for other balances
        PopupMenuButton<String>(
          icon: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(Icons.more_vert, color: Theme.of(context).primaryColor),
          ),
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'receipts',
              child: Row(
                children: [
                  Icon(Icons.trending_up, color: Colors.green, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Tổng thu',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          balance.formattedTotalReceipts,
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'payments',
              child: Row(
                children: [
                  Icon(Icons.trending_down, color: Colors.red, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Tổng chi',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          balance.formattedTotalPayments,
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'cashflow',
              child: Row(
                children: [
                  Icon(
                    balance.netCashFlow >= 0
                        ? Icons.arrow_upward
                        : Icons.arrow_downward,
                    color: balance.netCashFlow >= 0 ? Colors.green : Colors.red,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Dòng tiền',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          balance.formattedNetCashFlow,
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                color: balance.netCashFlow >= 0
                                    ? Colors.green
                                    : Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
          onSelected: (value) {
            // Optional: Show detailed dialog for each item
            // _showBalanceDetails(context, value, balance);
          },
        ),
      ],
    );
  }

  Widget _buildDesktopBalanceSummary(
    BuildContext context,
    CashBalance balance,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildBalanceCard(
            context,
            'Tổng thu',
            balance.formattedTotalReceipts,
            Icons.trending_up,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildBalanceCard(
            context,
            'Tổng chi',
            balance.formattedTotalPayments,
            Icons.trending_down,
            Colors.red,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildBalanceCard(
            context,
            'Tồn quỹ',
            balance.formattedClosingBalance,
            Icons.account_balance_wallet,
            balance.closingBalance >= 0 ? Colors.blue : Colors.red,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildBalanceCard(
            context,
            'Dòng tiền',
            balance.formattedNetCashFlow,
            balance.netCashFlow >= 0
                ? Icons.arrow_upward
                : Icons.arrow_downward,
            balance.netCashFlow >= 0 ? Colors.green : Colors.red,
          ),
        ),
      ],
    );
  }

  Widget _buildBalanceCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersAndSearch(
    BuildContext context,
    WidgetRef ref,
    FinanceSimpleState state,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Search Bar
          TextField(
            decoration: const InputDecoration(
              labelText: 'Tìm kiếm phiếu thu/chi',
              hintText: 'Nhập mô tả, số phiếu, tên đối tác...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              ref.read(financeSimpleProvider.notifier).searchEntries(value);
            },
          ),

          const SizedBox(height: 12),

          // Filter Chips
          Row(
            children: [
              Text(
                'Loại phiếu:',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Wrap(
                  spacing: 8,
                  children: [
                    _buildFilterChip(
                      context,
                      ref,
                      'Tất cả',
                      null,
                      state.filterType == null,
                    ),
                    _buildFilterChip(
                      context,
                      ref,
                      'Phiếu thu',
                      CashbookType.receipt,
                      state.filterType == CashbookType.receipt,
                    ),
                    _buildFilterChip(
                      context,
                      ref,
                      'Phiếu chi',
                      CashbookType.payment,
                      state.filterType == CashbookType.payment,
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    BuildContext context,
    WidgetRef ref,
    String label,
    CashbookType? type,
    bool isSelected,
  ) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          ref.read(financeSimpleProvider.notifier).setFilter(type);
        }
      },
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  Widget _buildEntriesList(
    BuildContext context,
    WidgetRef ref,
    FinanceSimpleState state,
  ) {
    final filteredEntries = _getFilteredEntries(
      state.entries,
      state.searchQuery,
    );

    if (filteredEntries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              state.searchQuery.isNotEmpty
                  ? 'Không tìm thấy phiếu nào'
                  : 'Chưa có phiếu thu/chi nào',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              state.searchQuery.isNotEmpty
                  ? 'Thử tìm kiếm với từ khóa khác'
                  : 'Thêm phiếu thu/chi đầu tiên để bắt đầu',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showAddEntryDialog(context, ref),
              icon: const Icon(Icons.add),
              label: const Text('Thêm phiếu'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(financeSimpleProvider.notifier).loadData(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredEntries.length,
        itemBuilder: (context, index) {
          final entry = filteredEntries[index];
          return _buildEntryCard(context, ref, entry);
        },
      ),
    );
  }

  List<CashbookEntry> _getFilteredEntries(
    List<CashbookEntry> entries,
    String query,
  ) {
    if (query.isEmpty) return entries;

    final lowerQuery = query.toLowerCase();
    return entries.where((entry) {
      return entry.description.toLowerCase().contains(lowerQuery) ||
          entry.entryNumber.toLowerCase().contains(lowerQuery) ||
          (entry.partnerName?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  Widget _buildEntryCard(
    BuildContext context,
    WidgetRef ref,
    CashbookEntry entry,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return AppCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => context.go('/cash-flow/${entry.id}'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  // Type Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: entry.isReceipt
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      entry.isReceipt ? Icons.trending_up : Icons.trending_down,
                      color: entry.isReceipt ? Colors.green : Colors.red,
                      size: 20,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Entry Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        LayoutBuilder(
                          builder: (context, constraints) {
                            // If too narrow, use column layout
                            if (constraints.maxWidth < 150) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    entry.entryNumber,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontSize:
                                              MediaQuery.of(
                                                    context,
                                                  ).size.width <
                                                  600
                                              ? 12.0
                                              : 14.0,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 4.0
                                          : 6.0,
                                      vertical:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 1.0
                                          : 2.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: entry.isReceipt
                                          ? Colors.green.withValues(alpha: 0.1)
                                          : Colors.red.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(
                                        MediaQuery.of(context).size.width < 600
                                            ? 8.0
                                            : 12.0,
                                      ),
                                    ),
                                    child: Text(
                                      entry.type.displayName,
                                      style: TextStyle(
                                        color: entry.isReceipt
                                            ? Colors.green
                                            : Colors.red,
                                        fontSize:
                                            MediaQuery.of(context).size.width <
                                                600
                                            ? 10.0
                                            : 12.0,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            } else {
                              return Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      entry.entryNumber,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            fontSize:
                                                MediaQuery.of(
                                                      context,
                                                    ).size.width <
                                                    600
                                                ? 12.0
                                                : 14.0,
                                          ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 4.0
                                          : 6.0,
                                      vertical:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 1.0
                                          : 2.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: entry.isReceipt
                                          ? Colors.green.withValues(alpha: 0.1)
                                          : Colors.red.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(
                                        MediaQuery.of(context).size.width < 600
                                            ? 8.0
                                            : 12.0,
                                      ),
                                    ),
                                    child: Text(
                                      entry.type.displayName,
                                      style: TextStyle(
                                        color: entry.isReceipt
                                            ? Colors.green
                                            : Colors.red,
                                        fontSize:
                                            MediaQuery.of(context).size.width <
                                                600
                                            ? 10.0
                                            : 12.0,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            }
                          },
                        ),
                        const SizedBox(height: 4),
                        Text(
                          entry.category.displayName,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: Colors.grey[600],
                                fontSize:
                                    MediaQuery.of(context).size.width < 600
                                    ? 10.0
                                    : 12.0,
                              ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // Amount
                  SizedBox(
                    width: MediaQuery.of(context).size.width < 600 ? 80 : 120,
                    child: Text(
                      '${entry.isReceipt ? '+' : '-'}${currencyFormat.format(entry.amount)}',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: entry.isReceipt ? Colors.green : Colors.red,
                        fontSize: MediaQuery.of(context).size.width < 600
                            ? 12.0
                            : 16.0,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Description
              Text(
                entry.description,
                style: Theme.of(context).textTheme.bodyMedium,
              ),

              // Partner Info
              if (entry.partnerName != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.person, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Text(
                      entry.partnerName!,
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[700]),
                    ),
                  ],
                ),
              ],

              // Note
              if (entry.note != null && entry.note!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.note, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          entry.note!,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(fontStyle: FontStyle.italic),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Date
              const SizedBox(height: 8),
              Text(
                'Ngày: ${dateFormat.format(entry.entryDate)}',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddEntryDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thêm phiếu thu/chi'),
        content: const Text(
          'Tính năng thêm phiếu thu/chi đang được phát triển',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }
}
