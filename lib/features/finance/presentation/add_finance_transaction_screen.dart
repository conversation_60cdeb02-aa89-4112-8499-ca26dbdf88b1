import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../domain/entities/cashbook_entry.dart';
import 'finance_screen_simple.dart';

class AddFinanceTransactionScreen extends ConsumerStatefulWidget {
  const AddFinanceTransactionScreen({super.key});

  @override
  ConsumerState<AddFinanceTransactionScreen> createState() =>
      _AddFinanceTransactionScreenState();
}

class _AddFinanceTransactionScreenState
    extends ConsumerState<AddFinanceTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _referenceController = TextEditingController();
  final _noteController = TextEditingController();

  String _transactionType = 'income'; // 'income' or 'expense'
  String _selectedCategory = 'general';
  String _paymentMethod = 'cash';
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  final List<String> _transactionTypes = ['income', 'expense'];
  final List<String> _incomeCategories = [
    'general',
    'sales',
    'service',
    'investment',
    'loan',
    'other',
  ];
  final List<String> _expenseCategories = [
    'general',
    'purchase',
    'salary',
    'rent',
    'utilities',
    'marketing',
    'transport',
    'maintenance',
    'other',
  ];
  final List<String> _paymentMethods = [
    'cash',
    'bank_transfer',
    'credit_card',
    'debit_card',
    'e_wallet',
  ];

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    _referenceController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.addFinanceTransaction),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.finance),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveTransaction,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(l10n.save),
          ),
        ],
      ),
      body: responsive.isMobile ? _buildMobileLayout() : _buildDesktopLayout(),
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTransactionTypeSection(),
            const SizedBox(height: 24),
            _buildAmountSection(),
            const SizedBox(height: 24),
            _buildCategorySection(),
            const SizedBox(height: 24),
            _buildPaymentSection(),
            const SizedBox(height: 24),
            _buildAdditionalInfoSection(),
            const SizedBox(height: 32),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1000),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // First row: Transaction Type and Amount
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(flex: 2, child: _buildTransactionTypeSection()),
                    const SizedBox(width: 24),
                    Expanded(flex: 2, child: _buildAmountSection()),
                  ],
                ),
                const SizedBox(height: 24),
                // Second row: Category and Payment
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(flex: 2, child: _buildCategorySection()),
                    const SizedBox(width: 24),
                    Expanded(flex: 2, child: _buildPaymentSection()),
                  ],
                ),
                const SizedBox(height: 24),
                // Full width: Additional Info
                _buildAdditionalInfoSection(),
                const SizedBox(height: 32),
                // Action buttons
                Center(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: _buildActionButtons(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionTypeSection() {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Loại giao dịch',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _transactionType,
              isExpanded: true,
              decoration: const InputDecoration(
                labelText: 'Loại giao dịch *',
                border: OutlineInputBorder(),
              ),
              items: _transactionTypes.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(
                    _getTransactionTypeLabel(type),
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _transactionType = value;
                    _selectedCategory = 'general'; // Reset category
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectDate(context),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Ngày giao dịch',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSection() {
    final l10n = AppLocalizations.of(context);
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Số tiền',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Số tiền *',
                border: OutlineInputBorder(),
                suffixText: '₫',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Vui lòng nhập số tiền';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'Số tiền không hợp lệ';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: InputDecoration(
                labelText: '${l10n.transactionDescription} *',
                border: const OutlineInputBorder(),
                hintText: l10n.transactionDescriptionHint,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return l10n.pleaseEnterDescription;
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection() {
    final categories = _transactionType == 'income'
        ? _incomeCategories
        : _expenseCategories;

    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Danh mục',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: categories.contains(_selectedCategory)
                  ? _selectedCategory
                  : 'general',
              isExpanded: true,
              decoration: const InputDecoration(
                labelText: 'Danh mục',
                border: OutlineInputBorder(),
              ),
              items: categories.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(
                    _getCategoryLabel(category),
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCategory = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSection() {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Phương thức thanh toán',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _paymentMethod,
              isExpanded: true,
              decoration: const InputDecoration(
                labelText: 'Phương thức',
                border: OutlineInputBorder(),
              ),
              items: _paymentMethods.map((method) {
                return DropdownMenuItem(
                  value: method,
                  child: Text(
                    _getPaymentMethodLabel(method),
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _paymentMethod = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin bổ sung',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _referenceController,
              decoration: const InputDecoration(
                labelText: 'Số tham chiếu',
                border: OutlineInputBorder(),
                hintText: 'VD: INV001, PO001',
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _noteController,
              decoration: const InputDecoration(
                labelText: 'Ghi chú',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => context.go(AppRoutes.finance),
            child: const Text('Hủy'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveTransaction,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Lưu phiếu'),
          ),
        ),
      ],
    );
  }

  String _getTransactionTypeLabel(String type) {
    switch (type) {
      case 'income':
        return 'Thu';
      case 'expense':
        return 'Chi';
      default:
        return type;
    }
  }

  String _getCategoryLabel(String category) {
    switch (category) {
      case 'general':
        return 'Tổng quát';
      case 'sales':
        return 'Bán hàng';
      case 'service':
        return 'Dịch vụ';
      case 'investment':
        return 'Đầu tư';
      case 'loan':
        return 'Vay mượn';
      case 'purchase':
        return 'Mua hàng';
      case 'salary':
        return 'Lương';
      case 'rent':
        return 'Thuê mặt bằng';
      case 'utilities':
        return 'Tiện ích';
      case 'marketing':
        return 'Marketing';
      case 'transport':
        return 'Vận chuyển';
      case 'maintenance':
        return 'Bảo trì';
      case 'other':
        return 'Khác';
      default:
        return category;
    }
  }

  String _getPaymentMethodLabel(String method) {
    switch (method) {
      case 'cash':
        return 'Tiền mặt';
      case 'bank_transfer':
        return 'Chuyển khoản';
      case 'credit_card':
        return 'Thẻ tín dụng';
      case 'debit_card':
        return 'Thẻ ghi nợ';
      case 'e_wallet':
        return 'Ví điện tử';
      default:
        return method;
    }
  }

  PaymentCategory _getCategoryFromString(String category) {
    switch (category) {
      case 'sales':
        return PaymentCategory.sales;
      case 'purchase':
        return PaymentCategory.purchase;
      case 'salary':
        return PaymentCategory.salary;
      case 'rent':
        return PaymentCategory.rent;
      case 'utilities':
        return PaymentCategory.utilities;
      case 'marketing':
        return PaymentCategory.marketing;
      case 'loan':
        return PaymentCategory.loan;
      case 'investment':
        return PaymentCategory.investment;
      case 'general':
      case 'other':
      default:
        return PaymentCategory.other;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create CashbookEntry object
      final entry = CashbookEntry(
        id: '', // Will be generated by backend
        entryNumber: '', // Will be generated by backend
        type: _transactionType == 'income'
            ? CashbookType.receipt
            : CashbookType.payment,
        category: _getCategoryFromString(_selectedCategory),
        amount: double.parse(_amountController.text),
        description: _descriptionController.text,
        partnerId: null, // No partner selection in this form
        entryDate: _selectedDate,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Call FinanceProvider to create entry
      final financeNotifier = ref.read(financeSimpleProvider.notifier);
      await financeNotifier.createEntry(entry);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Phiếu thu chi đã được tạo thành công'),
            backgroundColor: Colors.green,
          ),
        );
        context.go(AppRoutes.finance);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tạo phiếu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
