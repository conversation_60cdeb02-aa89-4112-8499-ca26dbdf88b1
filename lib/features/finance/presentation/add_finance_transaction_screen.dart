import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../domain/entities/cashbook_entry.dart';
import 'finance_screen_simple.dart';

class AddFinanceTransactionScreen extends ConsumerStatefulWidget {
  const AddFinanceTransactionScreen({super.key});

  @override
  ConsumerState<AddFinanceTransactionScreen> createState() =>
      _AddFinanceTransactionScreenState();
}

class _AddFinanceTransactionScreenState
    extends ConsumerState<AddFinanceTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _referenceController = TextEditingController();
  final _noteController = TextEditingController();

  String _transactionType = 'income'; // 'income' or 'expense'
  String _selectedCategory = 'general';
  String _paymentMethod = 'cash';
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  final List<String> _transactionTypes = ['income', 'expense'];
  final List<String> _incomeCategories = [
    'general',
    'sales',
    'service',
    'investment',
    'loan',
    'other',
  ];
  final List<String> _expenseCategories = [
    'general',
    'purchase',
    'salary',
    'rent',
    'utilities',
    'marketing',
    'transport',
    'maintenance',
    'other',
  ];
  final List<String> _paymentMethods = [
    'cash',
    'bank_transfer',
    'credit_card',
    'debit_card',
    'e_wallet',
  ];

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    _referenceController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.addFinanceTransaction),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.finance),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveTransaction,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(l10n.save),
          ),
        ],
      ),
      body: responsive.isMobile ? _buildMobileLayout() : _buildDesktopLayout(),
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTransactionTypeSection(),
            const SizedBox(height: 24),
            _buildAmountSection(),
            const SizedBox(height: 24),
            _buildCategorySection(),
            const SizedBox(height: 24),
            _buildPaymentSection(),
            const SizedBox(height: 24),
            _buildAdditionalInfoSection(),
            const SizedBox(height: 32),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1000),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // First row: Transaction Type and Amount
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(flex: 2, child: _buildTransactionTypeSection()),
                    const SizedBox(width: 24),
                    Expanded(flex: 2, child: _buildAmountSection()),
                  ],
                ),
                const SizedBox(height: 24),
                // Second row: Category and Payment
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(flex: 2, child: _buildCategorySection()),
                    const SizedBox(width: 24),
                    Expanded(flex: 2, child: _buildPaymentSection()),
                  ],
                ),
                const SizedBox(height: 24),
                // Full width: Additional Info
                _buildAdditionalInfoSection(),
                const SizedBox(height: 32),
                // Action buttons
                Center(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: _buildActionButtons(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionTypeSection() {
    final l10n = AppLocalizations.of(context);
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.transactionType,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _transactionType,
              isExpanded: true,
              decoration: InputDecoration(
                labelText: l10n.transactionTypeRequired,
                border: const OutlineInputBorder(),
              ),
              items: _transactionTypes.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(
                    _getTransactionTypeLabel(type),
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _transactionType = value;
                    _selectedCategory = 'general'; // Reset category
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectDate(context),
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: l10n.transactionDate,
                  border: const OutlineInputBorder(),
                  suffixIcon: const Icon(Icons.calendar_today),
                ),
                child: Text(
                  '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSection() {
    final l10n = AppLocalizations.of(context);
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.amount,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _amountController,
              decoration: InputDecoration(
                labelText: l10n.amountRequired,
                border: const OutlineInputBorder(),
                prefixText: '₫ ',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return l10n.amountRequired;
                }
                if (double.tryParse(value) == null) {
                  return l10n.invalidAmount;
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection() {
    final l10n = AppLocalizations.of(context);
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.category,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              isExpanded: true,
              decoration: InputDecoration(
                labelText: l10n.categoryRequired,
                border: const OutlineInputBorder(),
              ),
              items: (_transactionType == 'income'
                      ? _incomeCategories
                      : _expenseCategories)
                  .map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(
                    _getCategoryLabel(category),
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCategory = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSection() {
    final l10n = AppLocalizations.of(context);
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.paymentMethod,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _paymentMethod,
              isExpanded: true,
              decoration: InputDecoration(
                labelText: l10n.paymentMethodRequired,
                border: const OutlineInputBorder(),
              ),
              items: _paymentMethods.map((method) {
                return DropdownMenuItem(
                  value: method,
                  child: Text(
                    _getPaymentMethodLabel(method),
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _paymentMethod = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    final l10n = AppLocalizations.of(context);
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.additionalInfo,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: InputDecoration(
                labelText: l10n.description,
                border: const OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _referenceController,
              decoration: InputDecoration(
                labelText: l10n.reference,
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _noteController,
              decoration: InputDecoration(
                labelText: l10n.notes,
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final l10n = AppLocalizations.of(context);
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => context.go(AppRoutes.finance),
            child: Text(l10n.cancel),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveTransaction,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(l10n.save),
          ),
        ),
      ],
    );
  }

  String _getTransactionTypeLabel(String type) {
    final l10n = AppLocalizations.of(context);
    switch (type) {
      case 'income':
        return l10n.income;
      case 'expense':
        return l10n.expense;
      default:
        return type;
    }
  }

  String _getCategoryLabel(String category) {
    final l10n = AppLocalizations.of(context);
    switch (category) {
      case 'general':
        return l10n.general;
      case 'sales':
        return l10n.sales;
      case 'service':
        return l10n.service;
      case 'investment':
        return l10n.investment;
      case 'loan':
        return l10n.loan;
      case 'purchase':
        return l10n.purchase;
      case 'salary':
        return l10n.salary;
      case 'rent':
        return l10n.rent;
      case 'utilities':
        return l10n.utilities;
      case 'marketing':
        return l10n.marketing;
      case 'transport':
        return l10n.transport;
      case 'maintenance':
        return l10n.maintenance;
      case 'other':
        return l10n.other;
      default:
        return category;
    }
  }

  String _getPaymentMethodLabel(String method) {
    final l10n = AppLocalizations.of(context);
    switch (method) {
      case 'cash':
        return l10n.cash;
      case 'bank_transfer':
        return l10n.bankTransfer;
      case 'credit_card':
        return l10n.creditCard;
      case 'debit_card':
        return l10n.debitCard;
      case 'e_wallet':
        return l10n.eWallet;
      default:
        return method;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final entry = CashbookEntry(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        entryNumber: 'ENT-${DateTime.now().millisecondsSinceEpoch}',
        type: _transactionType == 'income'
            ? CashbookType.receipt
            : CashbookType.payment,
        category: PaymentCategory.values.firstWhere(
          (c) => c.name == _selectedCategory,
          orElse: () => PaymentCategory.other,
        ),
        amount: double.parse(_amountController.text),
        description: _descriptionController.text,
        paymentMethod: PaymentMethod.cash,
        entryDate: _selectedDate,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await ref.read(financeSimpleProvider.notifier).createEntry(context, entry);
      if (mounted) {
        context.go(AppRoutes.finance);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e.toString()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
