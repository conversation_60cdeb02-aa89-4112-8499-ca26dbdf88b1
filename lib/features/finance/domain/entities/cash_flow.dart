class CashFlow {
  final String id;
  final DateTime date;
  final double openingBalance;
  final double totalIncome;
  final double totalExpense;
  final double closingBalance;
  final Map<String, double> incomeByCategory;
  final Map<String, double> expenseByCategory;
  final Map<String, double> paymentMethods;
  final List<String> transactionIds;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CashFlow({
    required this.id,
    required this.date,
    required this.openingBalance,
    required this.totalIncome,
    required this.totalExpense,
    required this.closingBalance,
    required this.incomeByCategory,
    required this.expenseByCategory,
    required this.paymentMethods,
    required this.transactionIds,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CashFlow.fromJson(Map<String, dynamic> json) {
    return CashFlow(
      id: json['id'] as String,
      date: DateTime.parse(json['date'] as String),
      openingBalance: (json['opening_balance'] as num).toDouble(),
      totalIncome: (json['total_income'] as num).toDouble(),
      totalExpense: (json['total_expense'] as num).toDouble(),
      closingBalance: (json['closing_balance'] as num).toDouble(),
      incomeByCategory: Map<String, double>.from(
        (json['income_by_category'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
        ),
      ),
      expenseByCategory: Map<String, double>.from(
        (json['expense_by_category'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
        ),
      ),
      paymentMethods: Map<String, double>.from(
        (json['payment_methods'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
        ),
      ),
      transactionIds: List<String>.from(json['transaction_ids'] as List),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'opening_balance': openingBalance,
      'total_income': totalIncome,
      'total_expense': totalExpense,
      'closing_balance': closingBalance,
      'income_by_category': incomeByCategory,
      'expense_by_category': expenseByCategory,
      'payment_methods': paymentMethods,
      'transaction_ids': transactionIds,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  double get netFlow => totalIncome - totalExpense;
  
  double get profitMargin => totalIncome > 0 ? (netFlow / totalIncome) * 100 : 0;

  CashFlow copyWith({
    String? id,
    DateTime? date,
    double? openingBalance,
    double? totalIncome,
    double? totalExpense,
    double? closingBalance,
    Map<String, double>? incomeByCategory,
    Map<String, double>? expenseByCategory,
    Map<String, double>? paymentMethods,
    List<String>? transactionIds,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CashFlow(
      id: id ?? this.id,
      date: date ?? this.date,
      openingBalance: openingBalance ?? this.openingBalance,
      totalIncome: totalIncome ?? this.totalIncome,
      totalExpense: totalExpense ?? this.totalExpense,
      closingBalance: closingBalance ?? this.closingBalance,
      incomeByCategory: incomeByCategory ?? this.incomeByCategory,
      expenseByCategory: expenseByCategory ?? this.expenseByCategory,
      paymentMethods: paymentMethods ?? this.paymentMethods,
      transactionIds: transactionIds ?? this.transactionIds,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
