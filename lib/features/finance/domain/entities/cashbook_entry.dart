enum CashbookType { receipt, payment }

enum PaymentCategory {
  sales,           // <PERSON><PERSON> hàng
  purchase,        // <PERSON>a hàng
  expense,         // Chi phí
  salary,          // Lương
  rent,            // Thuê mặt bằng
  utilities,       // Tiện ích
  marketing,       // Marketing
  loan,            // Vay/Trả nợ
  investment,      // Đầu tư
  other,           // Khác
}

extension CashbookTypeExtension on CashbookType {
  String get displayName {
    switch (this) {
      case CashbookType.receipt:
        return 'Phiếu thu';
      case CashbookType.payment:
        return 'Phiếu chi';
    }
  }
  
  String get value {
    switch (this) {
      case CashbookType.receipt:
        return 'receipt';
      case CashbookType.payment:
        return 'payment';
    }
  }
}

extension PaymentCategoryExtension on PaymentCategory {
  String get displayName {
    switch (this) {
      case PaymentCategory.sales:
        return 'Bán hàng';
      case PaymentCategory.purchase:
        return 'Mua hàng';
      case PaymentCategory.expense:
        return 'Chi phí';
      case PaymentCategory.salary:
        return 'Lương';
      case PaymentCategory.rent:
        return 'Thuê mặt bằng';
      case PaymentCategory.utilities:
        return 'Tiện ích';
      case PaymentCategory.marketing:
        return 'Marketing';
      case PaymentCategory.loan:
        return 'Vay/Trả nợ';
      case PaymentCategory.investment:
        return 'Đầu tư';
      case PaymentCategory.other:
        return 'Khác';
    }
  }
}

class CashbookEntry {
  final String id;
  final String entryNumber;
  final CashbookType type;
  final PaymentCategory category;
  final double amount;
  final String? partnerId;
  final String? partnerName;
  final String? orderId;
  final String? transactionId;
  final String description;
  final String? note;
  final DateTime entryDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;

  const CashbookEntry({
    required this.id,
    required this.entryNumber,
    required this.type,
    required this.category,
    required this.amount,
    this.partnerId,
    this.partnerName,
    this.orderId,
    this.transactionId,
    required this.description,
    this.note,
    required this.entryDate,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
  });

  factory CashbookEntry.fromJson(Map<String, dynamic> json) {
    return CashbookEntry(
      id: json['id'] as String,
      entryNumber: json['entry_number'] as String,
      type: CashbookType.values.firstWhere(
        (e) => e.value == json['type'],
        orElse: () => CashbookType.receipt,
      ),
      category: PaymentCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => PaymentCategory.other,
      ),
      amount: (json['amount'] as num).toDouble(),
      partnerId: json['partner_id'] as String?,
      partnerName: json['partner_name'] as String?,
      orderId: json['order_id'] as String?,
      transactionId: json['transaction_id'] as String?,
      description: json['description'] as String,
      note: json['note'] as String?,
      entryDate: DateTime.parse(json['entry_date'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      createdBy: json['created_by'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'entry_number': entryNumber,
      'type': type.value,
      'category': category.name,
      'amount': amount,
      'partner_id': partnerId,
      'partner_name': partnerName,
      'order_id': orderId,
      'transaction_id': transactionId,
      'description': description,
      'note': note,
      'entry_date': entryDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'created_by': createdBy,
    };
  }

  bool get isReceipt => type == CashbookType.receipt;
  bool get isPayment => type == CashbookType.payment;
  
  String get displayEntryNumber => entryNumber;
  String get formattedAmount => '${amount.toStringAsFixed(0)}₫';
  String get formattedDate => '${entryDate.day.toString().padLeft(2, '0')}/${entryDate.month.toString().padLeft(2, '0')}/${entryDate.year}';

  CashbookEntry copyWith({
    String? id,
    String? entryNumber,
    CashbookType? type,
    PaymentCategory? category,
    double? amount,
    String? partnerId,
    String? partnerName,
    String? orderId,
    String? transactionId,
    String? description,
    String? note,
    DateTime? entryDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return CashbookEntry(
      id: id ?? this.id,
      entryNumber: entryNumber ?? this.entryNumber,
      type: type ?? this.type,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      partnerId: partnerId ?? this.partnerId,
      partnerName: partnerName ?? this.partnerName,
      orderId: orderId ?? this.orderId,
      transactionId: transactionId ?? this.transactionId,
      description: description ?? this.description,
      note: note ?? this.note,
      entryDate: entryDate ?? this.entryDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  @override
  String toString() {
    return 'CashbookEntry(id: $id, entryNumber: $entryNumber, type: $type, amount: $amount)';
  }
}

// Cash Balance Model
class CashBalance {
  final double openingBalance;
  final double totalReceipts;
  final double totalPayments;
  final double closingBalance;
  final DateTime asOfDate;

  const CashBalance({
    required this.openingBalance,
    required this.totalReceipts,
    required this.totalPayments,
    required this.closingBalance,
    required this.asOfDate,
  });

  factory CashBalance.fromJson(Map<String, dynamic> json) {
    return CashBalance(
      openingBalance: (json['opening_balance'] as num).toDouble(),
      totalReceipts: (json['total_receipts'] as num).toDouble(),
      totalPayments: (json['total_payments'] as num).toDouble(),
      closingBalance: (json['closing_balance'] as num).toDouble(),
      asOfDate: DateTime.parse(json['as_of_date'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'opening_balance': openingBalance,
      'total_receipts': totalReceipts,
      'total_payments': totalPayments,
      'closing_balance': closingBalance,
      'as_of_date': asOfDate.toIso8601String(),
    };
  }

  double get netCashFlow => totalReceipts - totalPayments;
  
  String get formattedOpeningBalance => '${openingBalance.toStringAsFixed(0)}₫';
  String get formattedTotalReceipts => '${totalReceipts.toStringAsFixed(0)}₫';
  String get formattedTotalPayments => '${totalPayments.toStringAsFixed(0)}₫';
  String get formattedClosingBalance => '${closingBalance.toStringAsFixed(0)}₫';
  String get formattedNetCashFlow => '${netCashFlow.toStringAsFixed(0)}₫';
}
