import 'package:city_pos/generated/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

enum CashbookType { receipt, payment }

enum PaymentCategory {
  sales,
  purchase,
  expense,
  salary,
  rent,
  utilities,
  marketing,
  loan,
  investment,
  other,
}

enum PaymentMethod { cash, card, transfer, other }

extension CashbookTypeExtension on CashbookType {
  String get value {
    switch (this) {
      case CashbookType.receipt:
        return 'receipt';
      case CashbookType.payment:
        return 'payment';
    }
  }
}

class CashbookEntry {
  final String id;
  final String entryNumber;
  final CashbookType type;
  final PaymentCategory category;
  final double amount;
  final String? partnerId;
  final String? partnerName;
  final String? partnerPhone;
  final String? partnerAddress;
  final String? orderId;
  final String? transactionId;
  final String description;
  final String? note;
  final DateTime entryDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;
  final PaymentMethod paymentMethod;
  final String status;

  const CashbookEntry({
    required this.id,
    required this.entryNumber,
    required this.type,
    required this.category,
    required this.amount,
    this.partnerId,
    this.partnerName,
    this.partnerPhone,
    this.partnerAddress,
    this.orderId,
    this.transactionId,
    required this.description,
    this.note,
    required this.entryDate,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
    required this.paymentMethod,
    this.status = 'active',
  });

  factory CashbookEntry.fromJson(Map<String, dynamic> json) {
    return CashbookEntry(
      id: json['id'] as String,
      entryNumber: json['entry_number'] as String,
      type: CashbookType.values.firstWhere((e) => e.name == json['type']),
      category: PaymentCategory.values.firstWhere(
        (e) => e.name == json['category'],
      ),
      amount: (json['amount'] as num).toDouble(),
      partnerId: json['partner_id'] as String?,
      partnerName: json['partner_name'] as String?,
      partnerPhone: json['partner_phone'] as String?,
      partnerAddress: json['partner_address'] as String?,
      orderId: json['order_id'] as String?,
      transactionId: json['transaction_id'] as String?,
      description: json['description'] as String,
      note: json['note'] as String?,
      entryDate: DateTime.parse(json['entry_date'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      createdBy: json['created_by'] as String?,
      paymentMethod: PaymentMethod.values.firstWhere(
        (e) => e.name == json['payment_method'],
      ),
      status: json['status'] as String? ?? 'active',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'entry_number': entryNumber,
      'type': type.name,
      'category': category.name,
      'amount': amount,
      'partner_id': partnerId,
      'partner_name': partnerName,
      'partner_phone': partnerPhone,
      'partner_address': partnerAddress,
      'order_id': orderId,
      'transaction_id': transactionId,
      'description': description,
      'note': note,
      'entry_date': entryDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'created_by': createdBy,
      'payment_method': paymentMethod.name,
      'status': status,
    };
  }

  bool get isReceipt => type == CashbookType.receipt;
  bool get isPayment => type == CashbookType.payment;

  String get displayEntryNumber => entryNumber;
  String get formattedAmount => '${amount.toStringAsFixed(0)}₫';
  String get formattedDate =>
      '${entryDate.day.toString().padLeft(2, '0')}/${entryDate.month.toString().padLeft(2, '0')}/${entryDate.year}';

  CashbookEntry copyWith({
    String? id,
    String? entryNumber,
    CashbookType? type,
    PaymentCategory? category,
    double? amount,
    String? partnerId,
    String? partnerName,
    String? partnerPhone,
    String? partnerAddress,
    String? orderId,
    String? transactionId,
    String? description,
    String? note,
    DateTime? entryDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    PaymentMethod? paymentMethod,
    String? status,
  }) {
    return CashbookEntry(
      id: id ?? this.id,
      entryNumber: entryNumber ?? this.entryNumber,
      type: type ?? this.type,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      partnerId: partnerId ?? this.partnerId,
      partnerName: partnerName ?? this.partnerName,
      partnerPhone: partnerPhone ?? this.partnerPhone,
      partnerAddress: partnerAddress ?? this.partnerAddress,
      orderId: orderId ?? this.orderId,
      transactionId: transactionId ?? this.transactionId,
      description: description ?? this.description,
      note: note ?? this.note,
      entryDate: entryDate ?? this.entryDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'CashbookEntry(id: $id, entryNumber: $entryNumber, type: $type, amount: $amount)';
  }

  String getDisplayName(BuildContext context) {
    switch (type) {
      case CashbookType.receipt:
        return AppLocalizations.of(context).receipt;
      case CashbookType.payment:
        return AppLocalizations.of(context).payment;
    }
  }

  String getCategoryDisplayName(BuildContext context) {
    switch (category) {
      case PaymentCategory.sales:
        return AppLocalizations.of(context).sales;
      case PaymentCategory.purchase:
        return AppLocalizations.of(context).purchase;
      case PaymentCategory.expense:
        return AppLocalizations.of(context).expense;
      case PaymentCategory.salary:
        return AppLocalizations.of(context).salary;
      case PaymentCategory.rent:
        return AppLocalizations.of(context).rent;
      case PaymentCategory.utilities:
        return AppLocalizations.of(context).utilities;
      case PaymentCategory.marketing:
        return AppLocalizations.of(context).marketing;
      case PaymentCategory.loan:
        return AppLocalizations.of(context).loan;
      case PaymentCategory.investment:
        return AppLocalizations.of(context).investment;
      case PaymentCategory.other:
        return AppLocalizations.of(context).other;
    }
  }

  String get typeDisplay {
    switch (type) {
      case CashbookType.receipt:
        return 'Receipt';
      case CashbookType.payment:
        return 'Payment';
    }
  }

  String get categoryDisplay {
    switch (category) {
      case PaymentCategory.sales:
        return 'Sales';
      case PaymentCategory.purchase:
        return 'Purchase';
      case PaymentCategory.expense:
        return 'Expense';
      case PaymentCategory.salary:
        return 'Salary';
      case PaymentCategory.rent:
        return 'Rent';
      case PaymentCategory.utilities:
        return 'Utilities';
      case PaymentCategory.marketing:
        return 'Marketing';
      case PaymentCategory.loan:
        return 'Loan';
      case PaymentCategory.investment:
        return 'Investment';
      case PaymentCategory.other:
        return 'Other';
    }
  }

  String get paymentMethodDisplay {
    switch (paymentMethod) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.card:
        return 'Card';
      case PaymentMethod.transfer:
        return 'Transfer';
      case PaymentMethod.other:
        return 'Other';
    }
  }
}

// Cash Balance Model
class CashBalance {
  final double openingBalance;
  final double totalReceipts;
  final double totalPayments;
  final double closingBalance;
  final double balance;
  final DateTime asOfDate;

  const CashBalance({
    required this.openingBalance,
    required this.totalReceipts,
    required this.totalPayments,
    required this.closingBalance,
    required this.balance,
    required this.asOfDate,
  });

  factory CashBalance.fromJson(Map<String, dynamic> json) {
    return CashBalance(
      openingBalance: (json['opening_balance'] as num).toDouble(),
      totalReceipts: (json['total_receipts'] as num).toDouble(),
      totalPayments: (json['total_payments'] as num).toDouble(),
      closingBalance: (json['closing_balance'] as num).toDouble(),
      balance: (json['balance'] as num).toDouble(),
      asOfDate: DateTime.parse(json['as_of_date'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'opening_balance': openingBalance,
      'total_receipts': totalReceipts,
      'total_payments': totalPayments,
      'closing_balance': closingBalance,
      'balance': balance,
      'as_of_date': asOfDate.toIso8601String(),
    };
  }

  double get netCashFlow => totalReceipts - totalPayments;

  String get formattedOpeningBalance => '${openingBalance.toStringAsFixed(0)}₫';
  String get formattedTotalReceipts => '${totalReceipts.toStringAsFixed(0)}₫';
  String get formattedTotalPayments => '${totalPayments.toStringAsFixed(0)}₫';
  String get formattedClosingBalance => '${closingBalance.toStringAsFixed(0)}₫';
  String get formattedNetCashFlow => '${netCashFlow.toStringAsFixed(0)}₫';

  CashBalance copyWith({
    double? openingBalance,
    double? totalReceipts,
    double? totalPayments,
    double? closingBalance,
    double? balance,
    DateTime? asOfDate,
  }) {
    return CashBalance(
      openingBalance: openingBalance ?? this.openingBalance,
      totalReceipts: totalReceipts ?? this.totalReceipts,
      totalPayments: totalPayments ?? this.totalPayments,
      closingBalance: closingBalance ?? this.closingBalance,
      balance: balance ?? this.balance,
      asOfDate: asOfDate ?? this.asOfDate,
    );
  }
}
