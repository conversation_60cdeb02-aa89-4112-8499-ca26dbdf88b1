enum StockTransactionType { import, export }

class StockTransaction {
  final String id;
  final StockTransactionType type;
  final String? partnerId;
  final String? partnerName;
  final String createdBy;
  final double totalAmount;
  final String? note;
  final List<StockTransactionItem> items;
  final DateTime createdAt;
  final DateTime updatedAt;

  const StockTransaction({
    required this.id,
    required this.type,
    this.partnerId,
    this.partnerName,
    required this.createdBy,
    required this.totalAmount,
    this.note,
    required this.items,
    required this.createdAt,
    required this.updatedAt,
  });

  factory StockTransaction.fromJson(Map<String, dynamic> json) {
    return StockTransaction(
      id: json['id'] as String,
      type: StockTransactionType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      partnerId: json['partner_id'] as String?,
      partnerName: json['partner_name'] as String?,
      createdBy: json['created_by'] as String,
      totalAmount: (json['total_amount'] as num).toDouble(),
      note: json['note'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => StockTransactionItem.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'partner_id': partnerId,
      'partner_name': partnerName,
      'created_by': createdBy,
      'total_amount': totalAmount,
      'note': note,
      'items': items.map((item) => item.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get typeLabel {
    switch (type) {
      case StockTransactionType.import:
        return 'Nhập kho';
      case StockTransactionType.export:
        return 'Xuất kho';
    }
  }

  String get transactionNumber {
    final prefix = type == StockTransactionType.import ? 'NK' : 'XK';
    final date = createdAt;
    return '$prefix${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}-${id.substring(0, 6).toUpperCase()}';
  }

  StockTransaction copyWith({
    String? id,
    StockTransactionType? type,
    String? partnerId,
    String? partnerName,
    String? createdBy,
    double? totalAmount,
    String? note,
    List<StockTransactionItem>? items,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return StockTransaction(
      id: id ?? this.id,
      type: type ?? this.type,
      partnerId: partnerId ?? this.partnerId,
      partnerName: partnerName ?? this.partnerName,
      createdBy: createdBy ?? this.createdBy,
      totalAmount: totalAmount ?? this.totalAmount,
      note: note ?? this.note,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'StockTransaction(id: $id, type: $type, totalAmount: $totalAmount)';
  }
}

class StockTransactionItem {
  final String id;
  final String transactionId;
  final String productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final String? note;

  const StockTransactionItem({
    required this.id,
    required this.transactionId,
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.note,
  });

  factory StockTransactionItem.fromJson(Map<String, dynamic> json) {
    return StockTransactionItem(
      id: json['id'] as String,
      transactionId: json['transaction_id'] as String,
      productId: json['product_id'] as String,
      productName: json['product_name'] as String,
      quantity: json['quantity'] as int,
      unitPrice: (json['unit_price'] as num).toDouble(),
      totalPrice: (json['total_price'] as num).toDouble(),
      note: json['note'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'transaction_id': transactionId,
      'product_id': productId,
      'product_name': productName,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'note': note,
    };
  }

  StockTransactionItem copyWith({
    String? id,
    String? transactionId,
    String? productId,
    String? productName,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    String? note,
  }) {
    return StockTransactionItem(
      id: id ?? this.id,
      transactionId: transactionId ?? this.transactionId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      note: note ?? this.note,
    );
  }

  @override
  String toString() {
    return 'StockTransactionItem(productName: $productName, quantity: $quantity, totalPrice: $totalPrice)';
  }
}
