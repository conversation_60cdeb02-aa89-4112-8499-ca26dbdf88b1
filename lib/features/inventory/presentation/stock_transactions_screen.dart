import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../data/services/stock_transaction_service.dart';
import '../domain/entities/stock_transaction.dart';
import 'widgets/stock_transaction_filters_widget.dart';
import 'widgets/stock_transaction_item_widget.dart';

final stockTransactionsProvider =
    StateNotifierProvider<StockTransactionsNotifier, StockTransactionsState>((
      ref,
    ) {
      return StockTransactionsNotifier();
    });

class StockTransactionsState {
  final bool isLoading;
  final String? error;
  final List<StockTransaction> transactions;
  final StockTransactionType? selectedType;
  final DateTime? startDate;
  final DateTime? endDate;
  final Map<String, dynamic> stats;

  const StockTransactionsState({
    this.isLoading = false,
    this.error,
    this.transactions = const [],
    this.selectedType,
    this.startDate,
    this.endDate,
    this.stats = const {},
  });

  StockTransactionsState copyWith({
    bool? isLoading,
    String? error,
    List<StockTransaction>? transactions,
    StockTransactionType? selectedType,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? stats,
    bool clearSelectedType = false,
    bool clearStartDate = false,
    bool clearEndDate = false,
  }) {
    return StockTransactionsState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      transactions: transactions ?? this.transactions,
      selectedType: clearSelectedType
          ? null
          : (selectedType ?? this.selectedType),
      startDate: clearStartDate ? null : (startDate ?? this.startDate),
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
      stats: stats ?? this.stats,
    );
  }
}

class StockTransactionsNotifier extends StateNotifier<StockTransactionsState> {
  StockTransactionsNotifier() : super(const StockTransactionsState()) {
    loadTransactions();
    loadStats();
  }

  Future<void> loadTransactions() async {
    print('🔥 StockTransactionsNotifier: Loading transactions...');
    print(
      '🔥 Current filters: type=${state.selectedType}, startDate=${state.startDate}, endDate=${state.endDate}',
    );
    state = state.copyWith(isLoading: true, error: null);

    try {
      final transactions = await StockTransactionService.getStockTransactions(
        type: state.selectedType,
        startDate: state.startDate,
        endDate: state.endDate,
      );

      print(
        '🔥 StockTransactionsNotifier: Loaded ${transactions.length} transactions',
      );

      // Debug: Print transaction types
      for (final t in transactions) {
        print(
          '🔥 Transaction: ${t.id} - Type: ${t.type} - Amount: ${t.totalAmount}',
        );
      }

      state = state.copyWith(isLoading: false, transactions: transactions);
    } catch (e) {
      print('🔥 StockTransactionsNotifier: Error loading transactions: $e');
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> loadStats() async {
    try {
      final stats = await StockTransactionService.getStockTransactionStats(
        startDate: state.startDate,
        endDate: state.endDate,
      );

      state = state.copyWith(stats: stats);
    } catch (e) {
      // Stats loading error doesn't affect main UI
    }
  }

  void setFilters({
    StockTransactionType? type,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    state = state.copyWith(
      selectedType: type,
      startDate: startDate,
      endDate: endDate,
    );
    loadTransactions();
    loadStats();
  }

  void clearFilters() {
    state = state.copyWith(
      clearSelectedType: true,
      clearStartDate: true,
      clearEndDate: true,
    );
    loadTransactions();
    loadStats();
  }

  Future<void> createTransaction({
    required StockTransactionType type,
    String? partnerId,
    String? partnerName,
    required List<StockTransactionItem> items,
    String? note,
  }) async {
    try {
      await StockTransactionService.createStockTransaction(
        type: type,
        partnerId: partnerId,
        partnerName: partnerName,
        createdBy: 'current_user', // TODO: Get from auth
        items: items,
        note: note,
      );

      await loadTransactions();
      await loadStats();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deleteTransaction(String id) async {
    try {
      await StockTransactionService.deleteStockTransaction(id);
      await loadTransactions();
      await loadStats();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }
}

class StockTransactionsScreen extends ConsumerWidget {
  const StockTransactionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final state = ref.watch(stockTransactionsProvider);
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: Text(l10n.stockTransactionsScreen),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.go(AppRoutes.addStockTransaction),
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: () {
              ref.read(stockTransactionsProvider.notifier).clearFilters();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Đã xóa tất cả bộ lọc'),
                  backgroundColor: Colors.green,
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () =>
                ref.read(stockTransactionsProvider.notifier).loadTransactions(),
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
          ? AppErrorWidget(
              message: state.error!,
              onRetry: () => ref
                  .read(stockTransactionsProvider.notifier)
                  .loadTransactions(),
            )
          : SafeArea(child: _buildContent(context, ref, state, responsive)),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    StockTransactionsState state,
    Responsive responsive,
  ) {
    // Force mobile layout for screens < 600px to ensure CustomScrollView is used
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return _buildMobileLayout(context, ref, state);
    } else {
      return _buildDesktopLayout(context, ref, state);
    }
  }

  Widget _buildMobileLayout(
    BuildContext context,
    WidgetRef ref,
    StockTransactionsState state,
  ) {
    return CustomScrollView(
      slivers: [
        // Stats Cards
        SliverToBoxAdapter(child: _buildStatsCards(context, state)),

        // Filters
        SliverToBoxAdapter(
          child: StockTransactionFiltersWidget(
            selectedType: state.selectedType,
            startDate: state.startDate,
            endDate: state.endDate,
            onFiltersChanged: (type, startDate, endDate) {
              ref
                  .read(stockTransactionsProvider.notifier)
                  .setFilters(
                    type: type,
                    startDate: startDate,
                    endDate: endDate,
                  );
            },
            onClearFilters: () {
              ref.read(stockTransactionsProvider.notifier).clearFilters();
            },
          ),
        ),

        // Transactions List
        _buildTransactionsSliverList(context, ref, state),
      ],
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    WidgetRef ref,
    StockTransactionsState state,
  ) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Stats Cards
          _buildStatsCards(context, state),

          const SizedBox(height: 24),

          // Filters
          StockTransactionFiltersWidget(
            selectedType: state.selectedType,
            startDate: state.startDate,
            endDate: state.endDate,
            onFiltersChanged: (type, startDate, endDate) {
              ref
                  .read(stockTransactionsProvider.notifier)
                  .setFilters(
                    type: type,
                    startDate: startDate,
                    endDate: endDate,
                  );
            },
            onClearFilters: () {
              ref.read(stockTransactionsProvider.notifier).clearFilters();
            },
          ),

          const SizedBox(height: 24),

          // Transactions List
          Expanded(child: _buildTransactionsList(context, ref, state)),
        ],
      ),
    );
  }

  Widget _buildStatsCards(BuildContext context, StockTransactionsState state) {
    final l10n = AppLocalizations.of(context);
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Container(
      padding: const EdgeInsets.all(16),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // If mobile screen, show hamburger menu
          if (MediaQuery.of(context).size.width < 768) {
            return _buildMobileStatsCards(context, state, currencyFormat, l10n);
          } else {
            return _buildDesktopStatsCards(
              context,
              state,
              currencyFormat,
              l10n,
            );
          }
        },
      ),
    );
  }

  Widget _buildMobileStatsCards(
    BuildContext context,
    StockTransactionsState state,
    NumberFormat currencyFormat,
    AppLocalizations l10n,
  ) {
    return Row(
      children: [
        // Main stat card (most important)
        Expanded(
          flex: 3,
          child: _buildStatCard(
            context,
            l10n.totalTransactions,
            '${state.stats['totalTransactions'] ?? 0}',
            Icons.receipt_long,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        // Hamburger menu for other stats
        PopupMenuButton<String>(
          icon: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(Icons.more_vert, color: Theme.of(context).primaryColor),
          ),
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'import',
              child: Row(
                children: [
                  Icon(Icons.arrow_downward, color: Colors.green, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          l10n.stockInType,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          currencyFormat.format(
                            state.stats['totalImportValue'] ?? 0,
                          ),
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.arrow_upward, color: Colors.orange, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          l10n.stockOutType,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          currencyFormat.format(
                            state.stats['totalExportValue'] ?? 0,
                          ),
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                color: Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
          onSelected: (value) {
            // Optional: Show detailed dialog for each stat
            // _showStatDetails(context, value, state);
          },
        ),
      ],
    );
  }

  Widget _buildDesktopStatsCards(
    BuildContext context,
    StockTransactionsState state,
    NumberFormat currencyFormat,
    AppLocalizations l10n,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            l10n.totalTransactions,
            '${state.stats['totalTransactions'] ?? 0}',
            Icons.receipt_long,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            l10n.stockInType,
            currencyFormat.format(state.stats['totalImportValue'] ?? 0),
            Icons.arrow_downward,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            l10n.stockOutType,
            currencyFormat.format(state.stats['totalExportValue'] ?? 0),
            Icons.arrow_upward,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return AppCard(
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(
    BuildContext context,
    WidgetRef ref,
    StockTransactionsState state,
  ) {
    final l10n = AppLocalizations.of(context);
    if (state.transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(l10n.noStockTransactionsYet),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: state.transactions.length,
      itemBuilder: (context, index) {
        final transaction = state.transactions[index];
        return StockTransactionItemWidget(
          transaction: transaction,
          onTap: () => context.go('/stock-transaction/${transaction.id}'),
        );
      },
    );
  }

  Widget _buildTransactionsSliverList(
    BuildContext context,
    WidgetRef ref,
    StockTransactionsState state,
  ) {
    if (state.transactions.isEmpty) {
      return SliverFillRemaining(
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('Chưa có phiếu xuất nhập kho'),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final transaction = state.transactions[index];
        return Padding(
          padding: EdgeInsets.only(
            left: 16,
            right: 16,
            top: index == 0 ? 16 : 0,
            bottom: index == state.transactions.length - 1 ? 16 : 0,
          ),
          child: StockTransactionItemWidget(
            transaction: transaction,
            onTap: () => context.go('/stock-transaction/${transaction.id}'),
          ),
        );
      }, childCount: state.transactions.length),
    );
  }
}
