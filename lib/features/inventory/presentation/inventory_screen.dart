import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/product_provider.dart';
import '../../../core/routers/app_router.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/responsive.dart';
import '../../../data/models/product.dart';
import '../../../generated/l10n/app_localizations.dart';
import 'widgets/add_product_dialog.dart';
import 'widgets/category_filter.dart';
import 'widgets/product_card.dart';
import 'widgets/product_search.dart';

class InventoryScreen extends ConsumerStatefulWidget {
  const InventoryScreen({super.key});

  @override
  ConsumerState<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends ConsumerState<InventoryScreen> {
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final productState = ref.watch(productProvider);
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: Text(l10n.inventory),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.go(AppRoutes.addProduct),
            tooltip: l10n.addProduct,
          ),
          IconButton(
            icon: const Icon(Icons.category),
            onPressed: () => context.go(AppRoutes.categories),
            tooltip: l10n.categories,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: EdgeInsets.all(
              responsive.isMobile ? 12.0 : AppTheme.spacingM,
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Search Bar
                ProductSearch(
                  controller: _searchController,
                  onChanged: (query) {
                    ref.read(productProvider.notifier).searchProducts(query);
                  },
                ),
                SizedBox(height: responsive.isMobile ? 8.0 : AppTheme.spacingM),

                // Category Filter
                CategoryFilter(
                  categories: productState.categories,
                  selectedCategoryId: productState.selectedCategoryId,
                  onCategorySelected: (categoryId) {
                    ref
                        .read(productProvider.notifier)
                        .filterByCategory(categoryId);
                  },
                ),
              ],
            ),
          ),

          // Products List
          Expanded(
            child: _buildProductsList(context, productState, responsive),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRoutes.addProduct),
        tooltip: l10n.addProduct,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildProductsList(
    BuildContext context,
    ProductState state,
    Responsive responsive,
  ) {
    final l10n = AppLocalizations.of(context);

    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppTheme.errorColor),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              state.error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingM),
            ElevatedButton(
              onPressed: () {
                ref.read(productProvider.notifier).loadProducts();
              },
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (state.products.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: AppTheme.textSecondaryColor,
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              l10n.inventoryNoProductsYet,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              l10n.inventoryAddFirstProduct,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),
            ElevatedButton.icon(
              onPressed: () => context.go(AppRoutes.addProduct),
              icon: const Icon(Icons.add),
              label: Text(l10n.addProduct),
            ),
          ],
        ),
      );
    }

    // Grid layout for products
    final crossAxisCount = responsive.isMobile
        ? 2
        : responsive.isTablet
        ? 3
        : 4;

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(productProvider.notifier).loadProducts();
      },
      child: GridView.builder(
        padding: EdgeInsets.all(responsive.isMobile ? 8.0 : AppTheme.spacingM),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: responsive.isMobile ? 8.0 : AppTheme.spacingM,
          mainAxisSpacing: responsive.isMobile ? 8.0 : AppTheme.spacingM,
          childAspectRatio: 0.75,
        ),
        itemCount: state.products.length,
        itemBuilder: (context, index) {
          final product = state.products[index];
          return ProductCard(
            product: product,
            onTap: () => context.go('/products/${product.id}'),
          );
        },
      ),
    );
  }

  void _showEditProductDialog(BuildContext context, Product product) {
    showDialog(
      context: context,
      builder: (context) => AddProductDialog(
        product: product,
        categories: ref.read(productProvider).categories,
        onProductAdded: (updatedProduct) {
          ref
              .read(productProvider.notifier)
              .updateProduct(product.id!, updatedProduct);
        },
      ),
    );
  }

  void _showProductDetails(BuildContext context, Product product) {
    // Navigate to product details screen
    // context.push('/inventory/product/${product.id}');

    // For now, show a simple dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(product.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('SKU: ${product.sku ?? 'N/A'}'),
            Text('Giá: ${product.formattedPrice}'),
            Text('Tồn kho: ${product.stockQuantity} ${product.unit}'),
            Text('Danh mục: ${product.categoryName}'),
            if (product.description != null) ...[
              const SizedBox(height: AppTheme.spacingS),
              Text('Mô tả: ${product.description}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showEditProductDialog(context, product);
            },
            child: const Text('Chỉnh sửa'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Product product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xóa sản phẩm'),
        content: Text('Bạn có chắc chắn muốn xóa sản phẩm "${product.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();
              final success = await ref
                  .read(productProvider.notifier)
                  .deleteProduct(product.id!);
              if (success && mounted) {
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: const Text('Đã xóa sản phẩm thành công'),
                    backgroundColor: AppTheme.successColor,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  void _showCategoriesDialog(BuildContext context) {
    // Show categories management dialog
    // For now, just show a simple list
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Danh mục'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: ref.read(productProvider).categories.length,
            itemBuilder: (context, index) {
              final category = ref.read(productProvider).categories[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: Color(
                    int.parse(category.color.replaceFirst('#', '0xFF')),
                  ),
                  child: Icon(Icons.category, color: Colors.white, size: 16),
                ),
                title: Text(category.name),
                subtitle: category.description != null
                    ? Text(category.description!)
                    : null,
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }
}
