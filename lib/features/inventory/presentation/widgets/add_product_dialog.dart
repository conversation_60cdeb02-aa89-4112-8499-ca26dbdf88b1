import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/themes/app_theme.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../../data/models/category.dart';
import '../../../../data/models/product.dart';

class AddProductDialog extends StatefulWidget {
  final Product? product;
  final List<Category> categories;
  final Function(Product) onProductAdded;

  const AddProductDialog({
    super.key,
    this.product,
    required this.categories,
    required this.onProductAdded,
  });

  @override
  State<AddProductDialog> createState() => _AddProductDialogState();
}

class _AddProductDialogState extends State<AddProductDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _skuController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _priceController = TextEditingController();
  final _costController = TextEditingController();
  final _stockController = TextEditingController();
  final _minStockController = TextEditingController();
  final _maxStockController = TextEditingController();
  final _unitController = TextEditingController();

  String? _selectedCategoryId;
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
    if (widget.product != null) {
      _populateFields(widget.product!);
    } else {
      _unitController.text = 'pcs';
    }
  }

  void _populateFields(Product product) {
    _nameController.text = product.name;
    _descriptionController.text = product.description ?? '';
    _skuController.text = product.sku ?? '';
    _barcodeController.text = product.barcode ?? '';
    _priceController.text = product.price.toString();
    _costController.text = product.cost.toString();
    _stockController.text = product.stockQuantity.toString();
    _minStockController.text = product.minStockLevel.toString();
    _maxStockController.text = product.maxStockLevel?.toString() ?? '';
    _unitController.text = product.unit;
    _selectedCategoryId = product.categoryId;
    _isActive = product.isActive;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _skuController.dispose();
    _barcodeController.dispose();
    _priceController.dispose();
    _costController.dispose();
    _stockController.dispose();
    _minStockController.dispose();
    _maxStockController.dispose();
    _unitController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final isEditing = widget.product != null;

    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Text(
                  isEditing ? l10n.editProduct : l10n.addProduct,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const Divider(),

            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Product Name
                      TextFormField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          labelText: l10n.productName,
                          hintText: l10n.enterProductName,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return l10n.pleaseEnterProductName;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: AppTheme.spacingM),

                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: InputDecoration(
                          labelText: l10n.description,
                          hintText: l10n.enterDescription,
                        ),
                        maxLines: 3,
                      ),
                      const SizedBox(height: AppTheme.spacingM),

                      // SKU and Barcode
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _skuController,
                              decoration: InputDecoration(
                                labelText: l10n.sku,
                                hintText: l10n.enterSku,
                              ),
                            ),
                          ),
                          const SizedBox(width: AppTheme.spacingM),
                          Expanded(
                            child: TextFormField(
                              controller: _barcodeController,
                              decoration: InputDecoration(
                                labelText: l10n.barcode,
                                hintText: l10n.enterBarcode,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppTheme.spacingM),

                      // Category
                      DropdownButtonFormField<String>(
                        value: _selectedCategoryId,
                        decoration: InputDecoration(labelText: l10n.category),
                        items: widget.categories.map((category) {
                          return DropdownMenuItem(
                            value: category.id,
                            child: Row(
                              children: [
                                CircleAvatar(
                                  backgroundColor: Color(category.colorValue),
                                  radius: 8,
                                ),
                                const SizedBox(width: AppTheme.spacingS),
                                Text(category.name),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategoryId = value;
                          });
                        },
                      ),
                      const SizedBox(height: AppTheme.spacingM),

                      // Price and Cost
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _priceController,
                              decoration: InputDecoration(
                                labelText: l10n.price,
                                hintText: '0',
                                suffixText: 'đ',
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'^\d+\.?\d{0,2}'),
                                ),
                              ],
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return l10n.pleaseEnterPrice;
                                }
                                final price = double.tryParse(value);
                                if (price == null || price < 0) {
                                  return l10n.pleaseEnterValidPrice;
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: AppTheme.spacingM),
                          Expanded(
                            child: TextFormField(
                              controller: _costController,
                              decoration: InputDecoration(
                                labelText: l10n.cost,
                                hintText: '0',
                                suffixText: 'đ',
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'^\d+\.?\d{0,2}'),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppTheme.spacingM),

                      // Stock and Unit
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _stockController,
                              decoration: InputDecoration(
                                labelText: l10n.stockQuantity,
                                hintText: '0',
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return l10n.pleaseEnterStock;
                                }
                                final stock = int.tryParse(value);
                                if (stock == null || stock < 0) {
                                  return l10n.pleaseEnterValidStock;
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: AppTheme.spacingM),
                          Expanded(
                            child: TextFormField(
                              controller: _unitController,
                              decoration: InputDecoration(
                                labelText: l10n.unit,
                                hintText: 'pcs',
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppTheme.spacingM),

                      // Min and Max Stock
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _minStockController,
                              decoration: InputDecoration(
                                labelText: l10n.minStock,
                                hintText: '0',
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                            ),
                          ),
                          const SizedBox(width: AppTheme.spacingM),
                          Expanded(
                            child: TextFormField(
                              controller: _maxStockController,
                              decoration: InputDecoration(
                                labelText: l10n.maxStock,
                                hintText: l10n.optional,
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppTheme.spacingM),

                      // Active Switch
                      SwitchListTile(
                        title: Text(l10n.active),
                        subtitle: Text(l10n.productActiveDescription),
                        value: _isActive,
                        onChanged: (value) {
                          setState(() {
                            _isActive = value;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(l10n.cancel),
                ),
                const SizedBox(width: AppTheme.spacingM),
                ElevatedButton(
                  onPressed: _saveProduct,
                  child: Text(isEditing ? l10n.update : l10n.add),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _saveProduct() {
    if (!_formKey.currentState!.validate()) return;

    final product = Product(
      id: widget.product?.id,
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      sku: _skuController.text.trim().isEmpty
          ? null
          : _skuController.text.trim(),
      barcode: _barcodeController.text.trim().isEmpty
          ? null
          : _barcodeController.text.trim(),
      categoryId: _selectedCategoryId,
      price: double.tryParse(_priceController.text) ?? 0.0,
      cost: double.tryParse(_costController.text) ?? 0.0,
      stockQuantity: int.tryParse(_stockController.text) ?? 0,
      minStockLevel: int.tryParse(_minStockController.text) ?? 0,
      maxStockLevel: _maxStockController.text.trim().isEmpty
          ? null
          : int.tryParse(_maxStockController.text),
      unit: _unitController.text.trim().isEmpty
          ? 'pcs'
          : _unitController.text.trim(),
      isActive: _isActive,
    );

    widget.onProductAdded(product);
    Navigator.of(context).pop();
  }
}
