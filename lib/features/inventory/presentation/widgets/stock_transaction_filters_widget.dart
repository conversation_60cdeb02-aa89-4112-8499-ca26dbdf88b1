import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../core/utils/responsive.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../domain/entities/stock_transaction.dart';

class StockTransactionFiltersWidget extends StatelessWidget {
  final StockTransactionType? selectedType;
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(StockTransactionType?, DateTime?, DateTime?) onFiltersChanged;
  final VoidCallback? onClearFilters;

  const StockTransactionFiltersWidget({
    super.key,
    this.selectedType,
    this.startDate,
    this.endDate,
    required this.onFiltersChanged,
    this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final responsive = Responsive(context);

    // Mobile: Compact dropdown filter
    if (responsive.isMobile) {
      return _buildMobileCompactFilter(context, l10n);
    }

    // Desktop: Full filter layout
    return _buildDesktopFilter(context, l10n);
  }

  Widget _buildMobileCompactFilter(
    BuildContext context,
    AppLocalizations l10n,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Row(
        children: [
          // Filter dropdown button
          Expanded(child: _buildFilterDropdown(context, l10n)),
          const SizedBox(width: 8),
          // Clear filters button (compact)
          if (_hasActiveFilters())
            IconButton(
              onPressed:
                  onClearFilters ?? () => onFiltersChanged(null, null, null),
              icon: const Icon(Icons.clear),
              tooltip: 'Xóa bộ lọc',
              style: IconButton.styleFrom(
                backgroundColor: Colors.red.withValues(alpha: 0.1),
                foregroundColor: Colors.red,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(BuildContext context, AppLocalizations l10n) {
    return PopupMenuButton<String>(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
          color: _hasActiveFilters()
              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
              : null,
        ),
        child: Row(
          children: [
            Icon(
              Icons.filter_list,
              size: 20,
              color: _hasActiveFilters()
                  ? Theme.of(context).primaryColor
                  : Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _getFilterSummary(l10n),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: _hasActiveFilters()
                      ? Theme.of(context).primaryColor
                      : Colors.grey[600],
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: _hasActiveFilters()
                  ? Theme.of(context).primaryColor
                  : Colors.grey[600],
            ),
          ],
        ),
      ),
      itemBuilder: (context) => [
        // Transaction Type Section
        PopupMenuItem<String>(
          enabled: false,
          child: Text(
            'Loại giao dịch',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
        PopupMenuItem<String>(
          value: 'type_all',
          child: _buildFilterOption(
            context,
            'Tất cả',
            selectedType == null,
            Icons.all_inclusive,
          ),
        ),
        PopupMenuItem<String>(
          value: 'type_import',
          child: _buildFilterOption(
            context,
            l10n.stockInType,
            selectedType == StockTransactionType.import,
            Icons.arrow_downward,
            Colors.green,
          ),
        ),
        PopupMenuItem<String>(
          value: 'type_export',
          child: _buildFilterOption(
            context,
            l10n.stockOutType,
            selectedType == StockTransactionType.export,
            Icons.arrow_upward,
            Colors.orange,
          ),
        ),
        const PopupMenuDivider(),

        // Quick Date Filters Section
        PopupMenuItem<String>(
          enabled: false,
          child: Text(
            'Thời gian',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
        PopupMenuItem<String>(
          value: 'date_today',
          child: _buildFilterOption(
            context,
            'Hôm nay',
            _isDateRangeSelected(_getTodayRange()),
            Icons.today,
          ),
        ),
        PopupMenuItem<String>(
          value: 'date_week',
          child: _buildFilterOption(
            context,
            'Tuần này',
            _isDateRangeSelected(_getThisWeekRange()),
            Icons.date_range,
          ),
        ),
        PopupMenuItem<String>(
          value: 'date_month',
          child: _buildFilterOption(
            context,
            'Tháng này',
            _isDateRangeSelected(_getThisMonthRange()),
            Icons.calendar_month,
          ),
        ),
        PopupMenuItem<String>(
          value: 'date_30days',
          child: _buildFilterOption(
            context,
            '30 ngày qua',
            _isDateRangeSelected(_getLast30DaysRange()),
            Icons.history,
          ),
        ),
        const PopupMenuDivider(),

        // Custom Date Range
        PopupMenuItem<String>(
          value: 'date_custom',
          child: _buildFilterOption(
            context,
            'Chọn khoảng thời gian...',
            false,
            Icons.edit_calendar,
            Colors.blue,
          ),
        ),
      ],
      onSelected: (value) => _handleFilterSelection(context, value, l10n),
    );
  }

  Widget _buildFilterOption(
    BuildContext context,
    String label,
    bool isSelected,
    IconData icon, [
    Color? iconColor,
  ]) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: isSelected
              ? Theme.of(context).primaryColor
              : (iconColor ?? Colors.grey[600]),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? Theme.of(context).primaryColor : null,
            ),
          ),
        ),
        if (isSelected)
          Icon(Icons.check, size: 16, color: Theme.of(context).primaryColor),
      ],
    );
  }

  Widget _buildDesktopFilter(BuildContext context, AppLocalizations l10n) {
    return AppCard(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              l10n.stockFilters,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Transaction Type Filter
            _buildTypeFilter(context, l10n),

            const SizedBox(height: 16),

            // Date Range Filter
            _buildDateRangeFilter(context, l10n),

            const SizedBox(height: 16),

            // Quick Date Filters
            _buildQuickDateFilters(context, l10n),

            const SizedBox(height: 16),

            // Clear Filters Button
            Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 200),
                child: OutlinedButton(
                  onPressed:
                      onClearFilters ??
                      () => onFiltersChanged(null, null, null),
                  child: const Text('Xóa bộ lọc'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeFilter(BuildContext context, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.transactionTypeFilter,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildTypeChip(
                context,
                l10n.allTransactionTypes,
                null,
                selectedType == null,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTypeChip(
                context,
                l10n.stockInType,
                StockTransactionType.import,
                selectedType == StockTransactionType.import,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTypeChip(
                context,
                l10n.stockOutType,
                StockTransactionType.export,
                selectedType == StockTransactionType.export,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTypeChip(
    BuildContext context,
    String label,
    StockTransactionType? type,
    bool isSelected,
  ) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          onFiltersChanged(type, startDate, endDate);
        }
      },
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  Widget _buildDateRangeFilter(BuildContext context, AppLocalizations l10n) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Khoảng thời gian',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectStartDate(context),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.calendar_today, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        startDate != null
                            ? dateFormat.format(startDate!)
                            : 'Từ ngày',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: InkWell(
                onTap: () => _selectEndDate(context),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.calendar_today, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        endDate != null
                            ? dateFormat.format(endDate!)
                            : 'Đến ngày',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickDateFilters(BuildContext context, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Lọc nhanh',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildQuickDateChip(context, 'Hôm nay', _getTodayRange()),
            _buildQuickDateChip(context, 'Tuần này', _getThisWeekRange()),
            _buildQuickDateChip(context, 'Tháng này', _getThisMonthRange()),
            _buildQuickDateChip(context, '30 ngày', _getLast30DaysRange()),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickDateChip(
    BuildContext context,
    String label,
    DateTimeRange range,
  ) {
    final isSelected =
        startDate != null &&
        endDate != null &&
        _isSameDay(startDate!, range.start) &&
        _isSameDay(endDate!, range.end);

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          onFiltersChanged(selectedType, range.start, range.end);
        }
      },
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  DateTimeRange _getTodayRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);
    return DateTimeRange(start: today, end: endOfDay);
  }

  DateTimeRange _getThisWeekRange() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return DateTimeRange(
      start: DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
      end: DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day, 23, 59, 59),
    );
  }

  DateTimeRange _getThisMonthRange() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    return DateTimeRange(
      start: startOfMonth,
      end: DateTime(
        endOfMonth.year,
        endOfMonth.month,
        endOfMonth.day,
        23,
        59,
        59,
      ),
    );
  }

  DateTimeRange _getLast30DaysRange() {
    final now = DateTime.now();
    final start = now.subtract(const Duration(days: 30));
    return DateTimeRange(
      start: DateTime(start.year, start.month, start.day),
      end: DateTime(now.year, now.month, now.day, 23, 59, 59),
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      onFiltersChanged(selectedType, date, endDate);
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: endDate ?? DateTime.now(),
      firstDate: startDate ?? DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);
      onFiltersChanged(selectedType, startDate, endOfDay);
    }
  }

  // Helper methods for mobile dropdown filter
  bool _hasActiveFilters() {
    return selectedType != null || startDate != null || endDate != null;
  }

  String _getFilterSummary(AppLocalizations l10n) {
    if (!_hasActiveFilters()) {
      return 'Tất cả giao dịch';
    }

    List<String> parts = [];

    if (selectedType != null) {
      switch (selectedType!) {
        case StockTransactionType.import:
          parts.add(l10n.stockInType);
          break;
        case StockTransactionType.export:
          parts.add(l10n.stockOutType);
          break;
      }
    }

    if (startDate != null && endDate != null) {
      final dateFormat = DateFormat('dd/MM');
      if (_isDateRangeSelected(_getTodayRange())) {
        parts.add('Hôm nay');
      } else if (_isDateRangeSelected(_getThisWeekRange())) {
        parts.add('Tuần này');
      } else if (_isDateRangeSelected(_getThisMonthRange())) {
        parts.add('Tháng này');
      } else if (_isDateRangeSelected(_getLast30DaysRange())) {
        parts.add('30 ngày qua');
      } else {
        parts.add(
          '${dateFormat.format(startDate!)} - ${dateFormat.format(endDate!)}',
        );
      }
    } else if (startDate != null) {
      parts.add('Từ ${DateFormat('dd/MM').format(startDate!)}');
    } else if (endDate != null) {
      parts.add('Đến ${DateFormat('dd/MM').format(endDate!)}');
    }

    return parts.isEmpty ? 'Tất cả giao dịch' : parts.join(' • ');
  }

  bool _isDateRangeSelected(DateTimeRange range) {
    return startDate != null &&
        endDate != null &&
        _isSameDay(startDate!, range.start) &&
        _isSameDay(endDate!, range.end);
  }

  void _handleFilterSelection(
    BuildContext context,
    String value,
    AppLocalizations l10n,
  ) {
    switch (value) {
      case 'type_all':
        onFiltersChanged(null, startDate, endDate);
        break;
      case 'type_import':
        onFiltersChanged(StockTransactionType.import, startDate, endDate);
        break;
      case 'type_export':
        onFiltersChanged(StockTransactionType.export, startDate, endDate);
        break;
      case 'date_today':
        final range = _getTodayRange();
        onFiltersChanged(selectedType, range.start, range.end);
        break;
      case 'date_week':
        final range = _getThisWeekRange();
        onFiltersChanged(selectedType, range.start, range.end);
        break;
      case 'date_month':
        final range = _getThisMonthRange();
        onFiltersChanged(selectedType, range.start, range.end);
        break;
      case 'date_30days':
        final range = _getLast30DaysRange();
        onFiltersChanged(selectedType, range.start, range.end);
        break;
      case 'date_custom':
        _showCustomDateRangePicker(context);
        break;
    }
  }

  Future<void> _showCustomDateRangePicker(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: startDate != null && endDate != null
          ? DateTimeRange(start: startDate!, end: endDate!)
          : null,
    );

    if (picked != null) {
      final endOfDay = DateTime(
        picked.end.year,
        picked.end.month,
        picked.end.day,
        23,
        59,
        59,
      );
      onFiltersChanged(selectedType, picked.start, endOfDay);
    }
  }
}
