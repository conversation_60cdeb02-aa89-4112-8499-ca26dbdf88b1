import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../core/widgets/app_card.dart';
import '../../domain/entities/stock_transaction.dart';

class StockTransactionItemWidget extends StatelessWidget {
  final StockTransaction transaction;
  final VoidCallback? onTap;

  const StockTransactionItemWidget({
    super.key,
    required this.transaction,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return AppCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  // Transaction Type Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getTypeColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getTypeIcon(),
                      color: _getTypeColor(),
                      size: 20,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Transaction Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        LayoutBuilder(
                          builder: (context, constraints) {
                            // If too narrow, use column layout
                            if (constraints.maxWidth < 200) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    transaction.transactionNumber,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontSize:
                                              MediaQuery.of(
                                                    context,
                                                  ).size.width <
                                                  600
                                              ? 13.0
                                              : 15.0,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 6.0
                                          : 8.0,
                                      vertical:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 1.0
                                          : 2.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _getTypeColor().withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(
                                        MediaQuery.of(context).size.width < 600
                                            ? 8.0
                                            : 12.0,
                                      ),
                                    ),
                                    child: Text(
                                      transaction.typeLabel,
                                      style: TextStyle(
                                        color: _getTypeColor(),
                                        fontSize:
                                            MediaQuery.of(context).size.width <
                                                600
                                            ? 10.0
                                            : 12.0,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            } else {
                              return Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      transaction.transactionNumber,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            fontSize:
                                                MediaQuery.of(
                                                      context,
                                                    ).size.width <
                                                    600
                                                ? 13.0
                                                : 15.0,
                                          ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 6.0
                                          : 8.0,
                                      vertical:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 1.0
                                          : 2.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _getTypeColor().withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(
                                        MediaQuery.of(context).size.width < 600
                                            ? 8.0
                                            : 12.0,
                                      ),
                                    ),
                                    child: Text(
                                      transaction.typeLabel,
                                      style: TextStyle(
                                        color: _getTypeColor(),
                                        fontSize:
                                            MediaQuery.of(context).size.width <
                                                600
                                            ? 10.0
                                            : 12.0,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            }
                          },
                        ),
                        const SizedBox(height: 4),
                        Text(
                          dateFormat.format(transaction.createdAt),
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: Colors.grey[600],
                                fontSize:
                                    MediaQuery.of(context).size.width < 600
                                    ? 11.0
                                    : 13.0,
                              ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Partner Info
              if (transaction.partnerName != null) ...[
                Row(
                  children: [
                    Icon(Icons.business, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        transaction.partnerName!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: MediaQuery.of(context).size.width < 600
                              ? 12.0
                              : 14.0,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Items Summary
              Row(
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${transaction.items.length} sản phẩm',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: MediaQuery.of(context).size.width < 600
                            ? 12.0
                            : 14.0,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(
                    width: MediaQuery.of(context).size.width < 600 ? 80 : 120,
                    child: Text(
                      currencyFormat.format(transaction.totalAmount),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _getTypeColor(),
                        fontSize: MediaQuery.of(context).size.width < 600
                            ? 12.0
                            : 16.0,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),

              // Note
              if (transaction.note != null && transaction.note!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.note_outlined,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          transaction.note!,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(fontStyle: FontStyle.italic),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Items Preview
              if (transaction.items.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 8),
                Text(
                  'Sản phẩm:',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                ...transaction.items
                    .take(3)
                    .map(
                      (item) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                item.productName,
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ),
                            Text(
                              'x${item.quantity}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              currencyFormat.format(item.totalPrice),
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                    ),
                if (transaction.items.length > 3)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      '... và ${transaction.items.length - 3} sản phẩm khác',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getTypeColor() {
    switch (transaction.type) {
      case StockTransactionType.import:
        return Colors.green;
      case StockTransactionType.export:
        return Colors.orange;
    }
  }

  IconData _getTypeIcon() {
    switch (transaction.type) {
      case StockTransactionType.import:
        return Icons.arrow_downward;
      case StockTransactionType.export:
        return Icons.arrow_upward;
    }
  }
}
