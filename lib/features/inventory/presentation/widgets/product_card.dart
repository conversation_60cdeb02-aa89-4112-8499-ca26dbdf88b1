import 'package:flutter/material.dart';

import '../../../../core/themes/app_theme.dart';
import '../../../../core/utils/responsive.dart';
import '../../../../core/widgets/product_image_widget.dart';
import '../../../../data/models/product.dart';

class ProductCard extends StatelessWidget {
  final Product product;
  final VoidCallback? onTap;

  const ProductCard({super.key, required this.product, this.onTap});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive(context);

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppTheme.surfaceColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppTheme.radiusM),
                    topRight: Radius.circular(AppTheme.radiusM),
                  ),
                ),
                child: product.id != null
                    ? ProductImageWidget(
                        productId: product.id!,
                        initialImageUrl: product.imageUrl,
                        isEditable: false,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(AppTheme.radiusM),
                          topRight: Radius.circular(AppTheme.radiusM),
                        ),
                      )
                    : _buildPlaceholderImage(),
              ),
            ),

            // Product Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.all(
                  responsive.isMobile ? 6.0 : AppTheme.spacingS,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Product Name
                    Flexible(
                      child: Text(
                        product.name,
                        style: responsive.isMobile
                            ? Theme.of(context).textTheme.labelLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              )
                            : Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        maxLines: responsive.isMobile ? 1 : 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(height: responsive.isMobile ? 2.0 : 4.0),

                    // SKU - Always show
                    if (product.sku != null)
                      Flexible(
                        child: Text(
                          'SKU: ${product.sku}',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: AppTheme.textSecondaryColor),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                    if (product.sku != null)
                      SizedBox(height: responsive.isMobile ? 2.0 : 4.0),

                    // Spacer - Use Flexible instead of Spacer for better control
                    const Flexible(child: SizedBox(height: 4)),

                    // Price and Stock
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            product.formattedPrice,
                            style: responsive.isMobile
                                ? Theme.of(
                                    context,
                                  ).textTheme.labelMedium?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  )
                                : Theme.of(
                                    context,
                                  ).textTheme.titleMedium?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildStockBadge(context),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppTheme.radiusM),
          topRight: Radius.circular(AppTheme.radiusM),
        ),
      ),
      child: Icon(
        Icons.inventory_2_outlined,
        size: 48,
        color: AppTheme.textSecondaryColor,
      ),
    );
  }

  Widget _buildStockBadge(BuildContext context) {
    final responsive = Responsive(context);
    Color badgeColor;
    String stockText;

    if (product.isOutOfStock) {
      badgeColor = AppTheme.errorColor;
      stockText = '0';
    } else if (product.isLowStock) {
      badgeColor = Colors.orange;
      stockText = '${product.stockQuantity}';
    } else {
      badgeColor = Colors.green;
      stockText = '${product.stockQuantity}';
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: responsive.isMobile ? 4.0 : AppTheme.spacingXS,
        vertical: responsive.isMobile ? 1.0 : 2.0,
      ),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(
          responsive.isMobile ? 3.0 : AppTheme.radiusS,
        ),
        border: Border.all(color: badgeColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        stockText,
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          color: badgeColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
