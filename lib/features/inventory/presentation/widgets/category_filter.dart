import 'package:flutter/material.dart';

import '../../../../core/themes/app_theme.dart';
import '../../../../data/models/category.dart';
import '../../../../generated/l10n/app_localizations.dart';

class CategoryFilter extends StatelessWidget {
  final List<Category> categories;
  final String? selectedCategoryId;
  final ValueChanged<String?> onCategorySelected;

  const CategoryFilter({
    super.key,
    required this.categories,
    this.selectedCategoryId,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final isAllSelected = selectedCategoryId == null;

    return SizedBox(
      height: 40,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          // All categories chip
          Padding(
            padding: const EdgeInsets.only(right: AppTheme.spacingS),
            child: FilterChip(
              label: Text(
                l10n.allCategories,
                style: TextStyle(
                  color: isAllSelected
                      ? Theme.of(context).colorScheme.primary
                      : null,
                ),
              ),
              selected: isAllSelected,
              onSelected: (selected) {
                // Always trigger selection when clicking "All Categories"
                onCategorySelected(null);
              },
              backgroundColor: Theme.of(context).cardColor,
              selectedColor: Theme.of(
                context,
              ).colorScheme.primary.withOpacity(0.1),
              checkmarkColor: Theme.of(context).colorScheme.primary,
              showCheckmark: true,
              side: BorderSide(
                color: isAllSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).dividerColor,
              ),
            ),
          ),

          // Category chips
          ...categories.map((category) {
            final isSelected = selectedCategoryId == category.id;

            return Padding(
              padding: const EdgeInsets.only(right: AppTheme.spacingS),
              child: FilterChip(
                label: Text(
                  category.name,
                  style: TextStyle(
                    color: isSelected ? Color(category.colorValue) : null,
                  ),
                ),
                selected: isSelected,
                onSelected: (selected) {
                  onCategorySelected(isSelected ? null : category.id);
                },
                backgroundColor: Theme.of(context).cardColor,
                selectedColor: Color(category.colorValue).withOpacity(0.1),
                checkmarkColor: Color(category.colorValue),
                showCheckmark: true,
                avatar: !isSelected
                    ? CircleAvatar(
                        backgroundColor: Color(category.colorValue),
                        radius: 8,
                      )
                    : null,
                side: BorderSide(
                  color: isSelected
                      ? Color(category.colorValue)
                      : Theme.of(context).dividerColor,
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
