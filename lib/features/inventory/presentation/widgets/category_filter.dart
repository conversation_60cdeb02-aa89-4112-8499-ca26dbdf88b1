import 'package:flutter/material.dart';

import '../../../../core/themes/app_theme.dart';
import '../../../../data/models/category.dart';
import '../../../../generated/l10n/app_localizations.dart';

class CategoryFilter extends StatelessWidget {
  final List<Category> categories;
  final String? selectedCategoryId;
  final ValueChanged<String?> onCategorySelected;

  const CategoryFilter({
    super.key,
    required this.categories,
    this.selectedCategoryId,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return SizedBox(
      height: 40,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          // All categories chip
          Padding(
            padding: const EdgeInsets.only(right: AppTheme.spacingS),
            child: FilterChip(
              label: Text(l10n.allCategories),
              selected: selectedCategoryId == null,
              onSelected: (selected) {
                // Always call onCategorySelected(null) when tapped
                // regardless of current selection state
                onCategorySelected(null);
              },
              backgroundColor: Theme.of(context).cardColor,
              selectedColor: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.2),
              checkmarkColor: Theme.of(context).colorScheme.primary,
            ),
          ),

          // Category chips
          ...categories.map((category) {
            final isSelected = selectedCategoryId == category.id;

            return Padding(
              padding: const EdgeInsets.only(right: AppTheme.spacingS),
              child: FilterChip(
                label: Text(category.name),
                selected: isSelected,
                onSelected: (selected) {
                  onCategorySelected(selected ? category.id : null);
                },
                backgroundColor: Theme.of(context).cardColor,
                selectedColor: Color(
                  category.colorValue,
                ).withValues(alpha: 0.2),
                checkmarkColor: Color(category.colorValue),
                avatar: !isSelected
                    ? CircleAvatar(
                        backgroundColor: Color(category.colorValue),
                        radius: 8,
                      )
                    : null,
              ),
            );
          }),
        ],
      ),
    );
  }
}
