import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/config/supabase_config.dart';
import '../../../notifications/data/services/notification_service.dart';
import '../../../notifications/domain/entities/notification.dart';
import '../../domain/entities/stock_transaction.dart';

class StockTransactionService {
  static final SupabaseClient? _supabase = SupabaseConfig.client;

  // Get all stock transactions
  static Future<List<StockTransaction>> getStockTransactions({
    StockTransactionType? type,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoStockTransactions()
          .where((transaction) {
            if (type != null && transaction.type != type) return false;
            if (startDate != null && transaction.createdAt.isBefore(startDate))
              return false;
            if (endDate != null && transaction.createdAt.isAfter(endDate))
              return false;
            return true;
          })
          .skip(offset ?? 0)
          .take(limit ?? 50)
          .toList();
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      print(
        '🔥 Query params: type=$type, startDate=$startDate, endDate=$endDate',
      );

      var queryBuilder = _supabase!.from('stock_transactions').select('''
        id, product_id, type, quantity, unit_cost, reference_type,
        reference_id, notes, created_by, created_at,
        products(name, sku, unit)
      ''');

      if (type != null) {
        final typeValue = type == StockTransactionType.import ? 'in' : 'out';
        print('🔥 Filtering by type: $typeValue');
        queryBuilder = queryBuilder.eq('type', typeValue);
      } else {
        print('🔥 No type filter - loading all transactions');
      }

      if (startDate != null) {
        print('🔥 Filtering by start date: ${startDate.toIso8601String()}');
        queryBuilder = queryBuilder.gte(
          'created_at',
          startDate.toIso8601String(),
        );
      }

      if (endDate != null) {
        print('🔥 Filtering by end date: ${endDate.toIso8601String()}');
        queryBuilder = queryBuilder.lte(
          'created_at',
          endDate.toIso8601String(),
        );
      }

      var finalQuery = queryBuilder.order('created_at', ascending: false);

      if (limit != null) {
        finalQuery = finalQuery.limit(limit);
      }

      if (offset != null) {
        finalQuery = finalQuery.range(offset, offset + (limit ?? 50) - 1);
      }

      final response = await finalQuery;

      print('🔥 Raw Supabase response: $response');

      // Convert each individual transaction to a StockTransaction object
      final List<StockTransaction> transactions = [];

      for (final data in response) {
        print('🔥 Processing transaction data: $data');

        final product = data['products'];
        final item = StockTransactionItem(
          id: data['id'],
          transactionId: data['id'],
          productId: data['product_id'],
          productName: product?['name'] ?? 'Unknown Product',
          quantity: data['quantity'] as int,
          unitPrice: (data['unit_cost'] as num?)?.toDouble() ?? 0.0,
          totalPrice:
              (data['quantity'] as int) *
              ((data['unit_cost'] as num?)?.toDouble() ?? 0.0),
        );

        transactions.add(
          StockTransaction(
            id: data['id'],
            type: data['type'] == 'in'
                ? StockTransactionType.import
                : StockTransactionType.export,
            partnerId: null,
            partnerName: null,
            createdBy: data['created_by'] ?? 'unknown',
            totalAmount: item.totalPrice,
            note: data['notes'],
            items: [item], // Single item per transaction
            createdAt: DateTime.parse(data['created_at']),
            updatedAt: DateTime.parse(data['created_at']),
          ),
        );
      }

      print('🔥 Converted ${transactions.length} transactions');
      return transactions;
    } catch (e) {
      print('Error getting stock transactions: $e');
      return [];
    }
  }

  // Create new stock transaction
  static Future<StockTransaction> createStockTransaction({
    required StockTransactionType type,
    String? partnerId,
    String? partnerName,
    required String createdBy,
    required List<StockTransactionItem> items,
    String? note,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _createDemoStockTransaction(
        type: type,
        partnerId: partnerId,
        partnerName: partnerName,
        createdBy: createdBy,
        items: items,
        note: note,
      );
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      print('🔥 Creating stock transaction with ${items.length} items...');

      // Calculate total amount
      final totalAmount = items.fold<double>(
        0,
        (sum, item) => sum + item.totalPrice,
      );

      // Create individual stock transactions for each item
      // (Following FULL_SETUP.sql schema - one record per product)
      final List<Map<String, dynamic>> createdTransactions = [];

      for (final item in items) {
        // Validate product exists first
        final productCheck = await _supabase!
            .from('products')
            .select('id, name')
            .eq('id', item.productId)
            .maybeSingle();

        if (productCheck == null) {
          throw Exception('Sản phẩm với ID ${item.productId} không tồn tại');
        }

        final transactionData = {
          'product_id': item.productId,
          'type': type == StockTransactionType.import ? 'in' : 'out',
          'quantity': item.quantity,
          'unit_cost': item.unitPrice,
          'reference_type':
              'adjustment', // Use 'adjustment' instead of 'manual'
          'reference_id': null,
          'notes':
              note ??
              'Phiếu ${type == StockTransactionType.import ? "nhập" : "xuất"} kho',
          // Skip created_by for now to avoid UUID constraint issues
          // 'created_by': createdBy,
          'created_at': DateTime.now().toIso8601String(),
        };

        print(
          '📦 Creating transaction for product ${item.productId}: ${item.quantity} items',
        );

        final response = await _supabase!
            .from('stock_transactions')
            .insert(transactionData)
            .select()
            .single();

        createdTransactions.add(response);

        // Update product stock quantity
        await _updateProductStock(item.productId, item.quantity, type);
      }

      print(
        '✅ Created ${createdTransactions.length} stock transactions successfully',
      );

      // Create notification for stock transaction
      try {
        final transactionTypeText = type == StockTransactionType.import
            ? 'nhập'
            : 'xuất';
        final totalQuantity = items.fold<int>(
          0,
          (sum, item) => sum + item.quantity,
        );

        await NotificationService.createNotification(
          AppNotification(
            id: 'stock_${DateTime.now().millisecondsSinceEpoch}',
            title: 'Phiếu $transactionTypeText kho thành công',
            message:
                'Đã tạo phiếu $transactionTypeText kho với $totalQuantity sản phẩm',
            type: 'success',
            category: 'inventory',
            actionUrl: '/stock-transactions',
            actionLabel: 'Xem phiếu',
            data: {
              'transaction_type': type.toString(),
              'total_quantity': totalQuantity,
              'total_amount': totalAmount,
            },
            createdAt: DateTime.now(),
          ),
        );
      } catch (e) {
        print('Error creating notification: $e');
        // Don't fail the transaction if notification fails
      }

      // Return a consolidated StockTransaction object
      final now = DateTime.now();
      return StockTransaction(
        id: createdTransactions.first['id'],
        type: type,
        partnerId: partnerId,
        partnerName: partnerName,
        createdBy: createdBy,
        totalAmount: totalAmount,
        note: note,
        items: items,
        createdAt: now,
        updatedAt: now,
      );
    } catch (e) {
      print('❌ Error creating stock transaction: $e');
      throw Exception('Lỗi tạo phiếu xuất nhập kho: $e');
    }
  }

  // Helper method to update product stock
  static Future<void> _updateProductStock(
    String productId,
    int quantity,
    StockTransactionType type,
  ) async {
    try {
      // Get current stock
      final productResponse = await _supabase!
          .from('products')
          .select('stock_quantity')
          .eq('id', productId)
          .single();

      final currentStock = productResponse['stock_quantity'] as int? ?? 0;
      final newStock = type == StockTransactionType.import
          ? currentStock + quantity
          : currentStock - quantity;

      // Validate stock for export
      if (type == StockTransactionType.export && newStock < 0) {
        throw Exception(
          'Không đủ tồn kho cho sản phẩm. Tồn kho hiện tại: $currentStock',
        );
      }

      // Update stock
      await _supabase!
          .from('products')
          .update({
            'stock_quantity': newStock,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', productId);

      print(
        '📊 Updated stock for product $productId: $currentStock → $newStock',
      );
    } catch (e) {
      print('❌ Error updating product stock: $e');
      throw Exception('Lỗi cập nhật tồn kho: $e');
    }
  }

  // Get stock transaction by ID
  static Future<StockTransaction?> getStockTransactionById(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoStockTransactions().firstWhere(
        (transaction) => transaction.id == id,
        orElse: () => throw Exception('Transaction not found'),
      );
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final response = await _supabase!
          .from('stock_transactions')
          .select('*')
          .eq('id', id)
          .single();

      // Since we store individual transactions, we need to group them
      // For now, return a single transaction
      return StockTransaction(
        id: response['id'],
        type: response['type'] == 'in'
            ? StockTransactionType.import
            : StockTransactionType.export,
        partnerId: null,
        partnerName: null,
        createdBy: response['created_by'] ?? 'unknown',
        totalAmount: (response['unit_cost'] as num?)?.toDouble() ?? 0.0,
        note: response['notes'],
        items: [], // Would need to fetch related items
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['created_at']),
      );
    } catch (e) {
      print('Error getting stock transaction by ID: $e');
      return null;
    }
  }

  // Update stock transaction
  static Future<StockTransaction> updateStockTransaction(
    String id,
    Map<String, dynamic> updates,
  ) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate update
      await Future.delayed(const Duration(milliseconds: 500));
      final transaction = await getStockTransactionById(id);
      return transaction!.copyWith(
        note: updates['note'] as String?,
        updatedAt: DateTime.now(),
      );
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      await _supabase!
          .from('stock_transactions')
          .update({'notes': updates['note']})
          .eq('id', id);

      final updatedTransaction = await getStockTransactionById(id);
      if (updatedTransaction == null) {
        throw Exception('Transaction not found after update');
      }
      return updatedTransaction;
    } catch (e) {
      throw Exception('Lỗi cập nhật phiếu xuất nhập kho: $e');
    }
  }

  // Delete stock transaction
  static Future<void> deleteStockTransaction(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // Simulate deletion
      await Future.delayed(const Duration(milliseconds: 500));
      return;
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      // Get transaction details before deletion to reverse stock changes
      final transaction = await getStockTransactionById(id);
      if (transaction != null) {
        // Reverse stock changes
        for (final item in transaction.items) {
          final reverseType = transaction.type == StockTransactionType.import
              ? StockTransactionType.export
              : StockTransactionType.import;
          await _updateProductStock(item.productId, item.quantity, reverseType);
        }
      }

      // Delete the transaction
      await _supabase!.from('stock_transactions').delete().eq('id', id);
    } catch (e) {
      throw Exception('Lỗi xóa phiếu xuất nhập kho: $e');
    }
  }

  // Get stock transaction statistics
  static Future<Map<String, dynamic>> getStockTransactionStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      final transactions = await getStockTransactions(
        startDate: startDate,
        endDate: endDate,
      );

      final imports = transactions.where(
        (t) => t.type == StockTransactionType.import,
      );
      final exports = transactions.where(
        (t) => t.type == StockTransactionType.export,
      );

      return {
        'totalTransactions': transactions.length,
        'totalImports': imports.length,
        'totalExports': exports.length,
        'totalImportValue': imports.fold<double>(
          0,
          (sum, t) => sum + t.totalAmount,
        ),
        'totalExportValue': exports.fold<double>(
          0,
          (sum, t) => sum + t.totalAmount,
        ),
        'totalItems': transactions.fold<int>(
          0,
          (sum, t) => sum + t.items.length,
        ),
      };
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      var query = _supabase!
          .from('stock_transactions')
          .select('type, quantity, unit_cost');

      if (startDate != null) {
        query = query.gte('created_at', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.lte('created_at', endDate.toIso8601String());
      }

      final response = await query;

      int totalTransactions = response.length;
      int totalImports = 0;
      int totalExports = 0;
      double totalImportValue = 0.0;
      double totalExportValue = 0.0;
      int totalItems = 0;

      for (final transaction in response) {
        final type = transaction['type'] as String;
        final quantity = transaction['quantity'] as int;
        final unitCost = (transaction['unit_cost'] as num?)?.toDouble() ?? 0.0;
        final value = quantity * unitCost;

        totalItems += quantity;

        if (type == 'in') {
          totalImports++;
          totalImportValue += value;
        } else if (type == 'out') {
          totalExports++;
          totalExportValue += value;
        }
      }

      return {
        'totalTransactions': totalTransactions,
        'totalImports': totalImports,
        'totalExports': totalExports,
        'totalImportValue': totalImportValue,
        'totalExportValue': totalExportValue,
        'totalItems': totalItems,
      };
    } catch (e) {
      print('Error getting stock transaction stats: $e');
      return {
        'totalTransactions': 0,
        'totalImports': 0,
        'totalExports': 0,
        'totalImportValue': 0.0,
        'totalExportValue': 0.0,
        'totalItems': 0,
      };
    }
  }

  // Demo data methods
  static List<StockTransaction> _getDemoStockTransactions() {
    final now = DateTime.now();

    return [
      StockTransaction(
        id: 'st_001',
        type: StockTransactionType.import,
        partnerId: 'supplier_001',
        partnerName: 'Nhà cung cấp ABC',
        createdBy: 'admin',
        totalAmount: 5000000,
        note: 'Nhập hàng đầu tháng',
        items: [
          StockTransactionItem(
            id: 'sti_001',
            transactionId: 'st_001',
            productId: '1',
            productName: 'Cà phê đen',
            quantity: 100,
            unitPrice: 20000,
            totalPrice: 2000000,
          ),
          StockTransactionItem(
            id: 'sti_002',
            transactionId: 'st_001',
            productId: '2',
            productName: 'Cà phê sữa',
            quantity: 80,
            unitPrice: 25000,
            totalPrice: 2000000,
          ),
          StockTransactionItem(
            id: 'sti_003',
            transactionId: 'st_001',
            productId: '3',
            productName: 'Bánh mì thịt',
            quantity: 50,
            unitPrice: 20000,
            totalPrice: 1000000,
          ),
        ],
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),

      StockTransaction(
        id: 'st_002',
        type: StockTransactionType.export,
        partnerId: null,
        partnerName: 'Xuất bán lẻ',
        createdBy: 'staff',
        totalAmount: 1500000,
        note: 'Xuất hàng bán lẻ',
        items: [
          StockTransactionItem(
            id: 'sti_004',
            transactionId: 'st_002',
            productId: '1',
            productName: 'Cà phê đen',
            quantity: 30,
            unitPrice: 25000,
            totalPrice: 750000,
          ),
          StockTransactionItem(
            id: 'sti_005',
            transactionId: 'st_002',
            productId: '4',
            productName: 'Nước cam',
            quantity: 50,
            unitPrice: 15000,
            totalPrice: 750000,
          ),
        ],
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 2)),
      ),

      StockTransaction(
        id: 'st_003',
        type: StockTransactionType.import,
        partnerId: 'supplier_002',
        partnerName: 'Nhà cung cấp XYZ',
        createdBy: 'admin',
        totalAmount: 3000000,
        note: 'Nhập hàng bổ sung',
        items: [
          StockTransactionItem(
            id: 'sti_006',
            transactionId: 'st_003',
            productId: '5',
            productName: 'Bánh ngọt',
            quantity: 200,
            unitPrice: 10000,
            totalPrice: 2000000,
          ),
          StockTransactionItem(
            id: 'sti_007',
            transactionId: 'st_003',
            productId: '4',
            productName: 'Nước cam',
            quantity: 100,
            unitPrice: 10000,
            totalPrice: 1000000,
          ),
        ],
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
    ];
  }

  static StockTransaction _createDemoStockTransaction({
    required StockTransactionType type,
    String? partnerId,
    String? partnerName,
    required String createdBy,
    required List<StockTransactionItem> items,
    String? note,
  }) {
    final now = DateTime.now();
    final totalAmount = items.fold<double>(
      0,
      (sum, item) => sum + item.totalPrice,
    );

    return StockTransaction(
      id: 'st_${now.millisecondsSinceEpoch}',
      type: type,
      partnerId: partnerId,
      partnerName: partnerName,
      createdBy: createdBy,
      totalAmount: totalAmount,
      note: note,
      items: items,
      createdAt: now,
      updatedAt: now,
    );
  }
}
