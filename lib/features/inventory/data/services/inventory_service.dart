import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/config/supabase_config.dart';
import '../../../../data/models/order.dart';
import '../../../../data/models/product.dart';

class InventoryService {
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Update inventory for order (trừ tồn kho)
  static Future<void> updateInventoryForOrder(Order order) async {
    if (SupabaseConfig.isDemoMode) {
      // Demo mode - simulate inventory update
      await Future.delayed(const Duration(milliseconds: 300));

      // Validate stock availability
      for (final item in order.items) {
        // In real implementation, check actual stock levels
        if (item.quantity <= 0) {
          throw Exception('Số lượng sản phẩm ${item.productName} không hợp lệ');
        }

        // Simulate stock check
        final availableStock = 100; // Demo stock
        if (item.quantity > availableStock) {
          throw Exception(
            '<PERSON>hông đủ tồn kho cho sản phẩm ${item.productName}. Tồn kho hiện tại: $availableStock',
          );
        }
      }
      return;
    }

    // Real implementation would:
    // 1. Check stock availability for each item
    // 2. Update product stock quantities
    // 3. Create stock transaction records

    try {
      for (final item in order.items) {
        // Get current product stock
        if (item.productId == null) continue;

        final response = await _supabase
            .from('products')
            .select('stock_quantity')
            .eq('id', item.productId!)
            .single();

        final currentStock = response['stock_quantity'] as int;

        // Check if enough stock
        if (currentStock < item.quantity) {
          throw Exception(
            'Không đủ tồn kho cho sản phẩm ${item.productName}. Tồn kho hiện tại: $currentStock',
          );
        }

        // Update stock
        await _supabase
            .from('products')
            .update({
              'stock_quantity': currentStock - item.quantity,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', item.productId!);

        // Create stock transaction record
        await _supabase.from('stock_transactions').insert({
          'product_id': item.productId!,
          'type': 'out',
          'quantity': -item.quantity, // Negative for outbound
          'unit_cost': item.unitPrice,
          'reference_type': 'order',
          'reference_id': order.id,
          'notes': 'Xuất kho từ đơn hàng ${order.orderNumber}',
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      throw Exception('Lỗi cập nhật tồn kho: $e');
    }
  }

  // Rollback inventory changes
  static Future<void> rollbackInventoryChanges(Order order) async {
    if (SupabaseConfig.isDemoMode) {
      // Demo mode - simulate rollback
      await Future.delayed(const Duration(milliseconds: 200));
      return;
    }

    try {
      for (final item in order.items) {
        if (item.productId == null) continue;

        // Get current product stock
        final response = await _supabase
            .from('products')
            .select('stock_quantity')
            .eq('id', item.productId!)
            .single();

        final currentStock = response['stock_quantity'] as int;

        // Restore stock
        await _supabase
            .from('products')
            .update({
              'stock_quantity': currentStock + item.quantity,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', item.productId!);
      }
    } catch (e) {
      throw Exception('Lỗi hoàn tác tồn kho: $e');
    }
  }

  // Check stock availability
  static Future<bool> checkStockAvailability(
    String productId,
    int quantity,
  ) async {
    if (SupabaseConfig.isDemoMode) {
      // Demo mode - always return true for small quantities
      return quantity <= 100;
    }

    try {
      final response = await _supabase
          .from('products')
          .select('stock_quantity')
          .eq('id', productId)
          .single();

      final currentStock = response['stock_quantity'] as int;
      return currentStock >= quantity;
    } catch (e) {
      return false;
    }
  }

  // Get product stock level
  static Future<int> getProductStock(String productId) async {
    if (SupabaseConfig.isDemoMode) {
      // Demo mode - return random stock
      return 50 + (DateTime.now().millisecondsSinceEpoch % 100);
    }

    try {
      final response = await _supabase
          .from('products')
          .select('stock_quantity')
          .eq('id', productId)
          .single();

      return response['stock_quantity'] as int;
    } catch (e) {
      return 0;
    }
  }

  // Update product stock
  static Future<void> updateProductStock(String productId, int newStock) async {
    if (SupabaseConfig.isDemoMode) {
      // Demo mode - simulate update
      await Future.delayed(const Duration(milliseconds: 200));
      return;
    }

    try {
      await _supabase
          .from('products')
          .update({
            'stock_quantity': newStock,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', productId);
    } catch (e) {
      throw Exception('Lỗi cập nhật tồn kho: $e');
    }
  }

  // Get low stock products
  static Future<List<Product>> getLowStockProducts({int threshold = 10}) async {
    if (SupabaseConfig.isDemoMode) {
      // Demo mode - return empty list
      return [];
    }

    try {
      final response = await _supabase
          .from('products')
          .select('*')
          .lt('stock_quantity', threshold)
          .eq('is_active', true);

      return response.map((data) => Product.fromJson(data)).toList();
    } catch (e) {
      return [];
    }
  }
}
