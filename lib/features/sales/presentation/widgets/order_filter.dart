import 'package:flutter/material.dart';
import '../../../../core/themes/app_theme.dart';
import '../../../../generated/l10n/app_localizations.dart';

class OrderFilter extends StatelessWidget {
  final String? selectedStatus;
  final String? selectedPaymentStatus;
  final ValueChanged<String?> onStatusChanged;
  final ValueChanged<String?> onPaymentStatusChanged;
  final VoidCallback onClearFilters;

  const OrderFilter({
    super.key,
    this.selectedStatus,
    this.selectedPaymentStatus,
    required this.onStatusChanged,
    required this.onPaymentStatusChanged,
    required this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  l10n.filters,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (selectedStatus != null || selectedPaymentStatus != null)
                  TextButton(
                    onPressed: onClearFilters,
                    child: Text(l10n.clearFilters),
                  ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingM),
            Wrap(
              spacing: AppTheme.spacingM,
              runSpacing: AppTheme.spacingS,
              children: [
                // Order Status Filter
                SizedBox(
                  width: 200,
                  child: DropdownButtonFormField<String>(
                    value: selectedStatus,
                    decoration: InputDecoration(
                      labelText: l10n.orderStatus,
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingM,
                        vertical: AppTheme.spacingS,
                      ),
                    ),
                    items: [
                      DropdownMenuItem(
                        value: null,
                        child: Text(l10n.allStatuses),
                      ),
                      DropdownMenuItem(
                        value: 'pending',
                        child: Text(l10n.pending),
                      ),
                      DropdownMenuItem(
                        value: 'confirmed',
                        child: Text(l10n.confirmed),
                      ),
                      DropdownMenuItem(
                        value: 'completed',
                        child: Text(l10n.completed),
                      ),
                      DropdownMenuItem(
                        value: 'cancelled',
                        child: Text(l10n.cancelled),
                      ),
                    ],
                    onChanged: onStatusChanged,
                  ),
                ),

                // Payment Status Filter
                SizedBox(
                  width: 200,
                  child: DropdownButtonFormField<String>(
                    value: selectedPaymentStatus,
                    decoration: InputDecoration(
                      labelText: l10n.paymentStatus,
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingM,
                        vertical: AppTheme.spacingS,
                      ),
                    ),
                    items: [
                      DropdownMenuItem(
                        value: null,
                        child: Text(l10n.allPaymentStatuses),
                      ),
                      DropdownMenuItem(
                        value: 'pending',
                        child: Text(l10n.pendingPayment),
                      ),
                      DropdownMenuItem(
                        value: 'partial',
                        child: Text(l10n.partialPayment),
                      ),
                      DropdownMenuItem(
                        value: 'paid',
                        child: Text(l10n.paid),
                      ),
                      DropdownMenuItem(
                        value: 'refunded',
                        child: Text(l10n.refunded),
                      ),
                    ],
                    onChanged: onPaymentStatusChanged,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
