import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/models/order.dart';

class SimpleInvoiceDisplayScreen extends ConsumerWidget {
  final Order order;

  const SimpleInvoiceDisplayScreen({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hóa đơn bán hàng'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/pos'),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            const Center(
              child: Column(
                children: [
                  Text(
                    'CITY POS',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('Hóa đơn bán hàng'),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Order Info
            Text('Số hóa đơn: ${order.orderNumber}'),
            Text('Ngày: ${order.createdAt?.toString() ?? 'N/A'}'),
            Text('Trạng thái: ${order.status}'),
            Text('Phương thức thanh toán: ${order.paymentMethod ?? 'Tiền mặt'}'),
            
            const SizedBox(height: 20),

            // Customer Info
            if (order.notes != null) ...[
              const Text(
                'Thông tin khách hàng:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(order.notes!),
              const SizedBox(height: 20),
            ],

            // Items
            const Text(
              'Chi tiết đơn hàng:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            
            Expanded(
              child: ListView.builder(
                itemCount: order.items.length,
                itemBuilder: (context, index) {
                  final item = order.items[index];
                  return Card(
                    child: ListTile(
                      title: Text(item.productName ?? 'N/A'),
                      subtitle: Text('Số lượng: ${item.quantity}'),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text('${item.unitPrice.toStringAsFixed(0)}₫'),
                          Text(
                            '${item.totalAmount.toStringAsFixed(0)}₫',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Total
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Tạm tính:'),
                Text('${order.subtotal.toStringAsFixed(0)}₫'),
              ],
            ),
            if (order.discountAmount > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Giảm giá:'),
                  Text('-${order.discountAmount.toStringAsFixed(0)}₫'),
                ],
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Thuế:'),
                Text('${order.taxAmount.toStringAsFixed(0)}₫'),
              ],
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Tổng cộng:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${order.totalAmount.toStringAsFixed(0)}₫',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Simple share action
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Tính năng chia sẻ đang được phát triển'),
                          backgroundColor: Colors.blue,
                        ),
                      );
                    },
                    icon: const Icon(Icons.share),
                    label: const Text('Chia sẻ'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => context.go('/pos'),
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('Quay lại'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
