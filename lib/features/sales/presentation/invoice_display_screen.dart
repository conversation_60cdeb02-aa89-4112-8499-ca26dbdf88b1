import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/services/pdf_service.dart';
import '../../../core/themes/app_theme.dart';
import '../../../data/models/order.dart';
import '../../../generated/l10n/app_localizations.dart';

class InvoiceDisplayScreen extends ConsumerStatefulWidget {
  final Order order;

  const InvoiceDisplayScreen({super.key, required this.order});

  @override
  ConsumerState<InvoiceDisplayScreen> createState() =>
      _InvoiceDisplayScreenState();
}

class _InvoiceDisplayScreenState extends ConsumerState<InvoiceDisplayScreen> {
  bool _isGeneratingPDF = false;
  File? _pdfFile;

  @override
  void initState() {
    super.initState();
    _generatePDF();
  }

  Future<void> _generatePDF() async {
    setState(() {
      _isGeneratingPDF = true;
    });

    try {
      final pdfFile = await PDFService.generateInvoicePDF(widget.order);
      setState(() {
        _pdfFile = pdfFile;
        _isGeneratingPDF = false;
      });
    } catch (e) {
      setState(() {
        _isGeneratingPDF = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi tạo PDF: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _shareInvoice() async {
    if (_pdfFile == null) return;

    try {
      final result = await Share.shareXFiles(
        [XFile(_pdfFile!.path)],
        text: 'Hóa đơn ${widget.order.orderNumber ?? 'N/A'}',
        subject: 'Hóa đơn bán hàng',
      );

      // After sharing, navigate back to POS
      if (mounted && result.status == ShareResultStatus.success) {
        context.go(AppRoutes.pos);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi chia sẻ: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _navigateBackToPOS() {
    context.go(AppRoutes.pos);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Hóa đơn ${widget.order.orderNumber ?? 'N/A'}'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _navigateBackToPOS,
        ),
      ),
      body: Column(
        children: [
          // Invoice preview
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingM),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _isGeneratingPDF
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: AppTheme.spacingM),
                          Text('Đang tạo hóa đơn...'),
                        ],
                      ),
                    )
                  : _buildInvoicePreview(),
            ),
          ),

          // Action buttons
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingM),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _pdfFile != null ? _shareInvoice : null,
                    icon: const Icon(Icons.share),
                    label: const Text('Chia sẻ'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppTheme.spacingM,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingM),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // TODO: Implement print functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Tính năng in đang phát triển'),
                        ),
                      );
                    },
                    icon: const Icon(Icons.print),
                    label: const Text('In'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: AppTheme.spacingM,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoicePreview() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(),
          const SizedBox(height: AppTheme.spacingL),

          // Invoice info
          _buildInvoiceInfo(),
          const SizedBox(height: AppTheme.spacingL),

          // Customer info
          _buildCustomerInfo(),
          const SizedBox(height: AppTheme.spacingL),

          // Items
          _buildItemsList(),
          const SizedBox(height: AppTheme.spacingL),

          // Total
          _buildTotal(),
          const SizedBox(height: AppTheme.spacingL),

          // Footer
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'CITY POS',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppTheme.spacingS),
        Text(
          'HÓA ĐƠN BÁN HÀNG',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppTheme.spacingXS),
        Text(
          'SALES INVOICE',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppTheme.textSecondaryColor),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInvoiceInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Số hóa đơn: ${widget.order.orderNumber ?? 'N/A'}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: AppTheme.spacingXS),
            Text(
              'Ngày: ${_formatDate(widget.order.createdAt)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'Trạng thái: ${widget.order.status}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: AppTheme.spacingXS),
            Text(
              'Thanh toán: ${widget.order.paymentMethod ?? 'Tiền mặt'}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCustomerInfo() {
    String customerName = 'Khách lẻ';
    String customerPhone = '';

    // Extract customer info from notes if available
    if (widget.order.notes != null &&
        widget.order.notes!.contains('Khách hàng:')) {
      final parts = widget.order.notes!.split(' - ');
      if (parts.isNotEmpty) {
        customerName = parts[0].replaceAll('Khách hàng: ', '');
      }
      if (parts.length > 1) {
        customerPhone = parts[1].replaceAll('SĐT: ', '');
      }
    }

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        border: Border.all(color: AppTheme.borderColor),
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'THÔNG TIN KHÁCH HÀNG',
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text('Tên: $customerName'),
          if (customerPhone.isNotEmpty) ...[
            const SizedBox(height: AppTheme.spacingXS),
            Text('SĐT: $customerPhone'),
          ],
        ],
      ),
    );
  }

  Widget _buildItemsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'CHI TIẾT SẢN PHẨM',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppTheme.spacingS),
        Table(
          border: TableBorder.all(color: AppTheme.borderColor),
          children: [
            // Header
            TableRow(
              decoration: BoxDecoration(color: Colors.grey.shade100),
              children: [
                _buildTableCell('STT', isHeader: true),
                _buildTableCell('Tên sản phẩm', isHeader: true),
                _buildTableCell('SL', isHeader: true),
                _buildTableCell('Đơn giá', isHeader: true),
                _buildTableCell('Thành tiền', isHeader: true),
              ],
            ),
            // Items
            ...widget.order.items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return TableRow(
                children: [
                  _buildTableCell('${index + 1}'),
                  _buildTableCell(item.productName ?? 'N/A'),
                  _buildTableCell('${item.quantity}'),
                  _buildTableCell(_formatCurrency(item.unitPrice)),
                  _buildTableCell(_formatCurrency(item.totalAmount)),
                ],
              );
            }),
          ],
        ),
      ],
    );
  }

  Widget _buildTableCell(String text, {bool isHeader = false}) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingS),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTotal() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        SizedBox(
          width: 200,
          child: Column(
            children: [
              _buildTotalRow(
                'Tạm tính:',
                _formatCurrency(widget.order.subtotal),
              ),
              if (widget.order.discountAmount > 0)
                _buildTotalRow(
                  'Giảm giá:',
                  '-${_formatCurrency(widget.order.discountAmount)}',
                ),
              const Divider(),
              _buildTotalRow(
                'TỔNG CỘNG:',
                _formatCurrency(widget.order.totalAmount),
                isTotal: true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTotalRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Column(
      children: [
        Text(
          'Cảm ơn quý khách đã mua hàng!',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppTheme.spacingXS),
        Text(
          'Thank you for your purchase!',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondaryColor),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}₫';
  }
}
