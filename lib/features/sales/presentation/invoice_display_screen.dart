import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/services/pdf_service.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../../../data/models/order.dart';

class InvoiceDisplayScreen extends ConsumerStatefulWidget {
  final Order order;

  const InvoiceDisplayScreen({super.key, required this.order});

  @override
  ConsumerState<InvoiceDisplayScreen> createState() =>
      _InvoiceDisplayScreenState();
}

class _InvoiceDisplayScreenState extends ConsumerState<InvoiceDisplayScreen> {
  bool _isGeneratingPdf = false;

  Future<void> _shareInvoice() async {
    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      final pdfService = PDFService();
      await pdfService.generateAndShareInvoice(widget.order);

      // Show success message
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(l10n.invoiceSharedSuccess),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        // Navigate back to POS after successful share
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            context.go('/pos');
          }
        });
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('${l10n.invoiceShareError}: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingPdf = false;
        });
      }
    }
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}₫';
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.salesInvoice),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/pos'),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Center(
              child: Column(
                children: [
                  const Text(
                    'CITY POS',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  Text(l10n.salesInvoice),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Order Info
            Text('${l10n.invoiceNumber}: ${widget.order.orderNumber}'),
            Text(
              '${l10n.date}: ${widget.order.createdAt?.toString().split(' ')[0] ?? 'N/A'}',
            ),
            Text('${l10n.status}: ${widget.order.status}'),
            Text(
              '${l10n.paymentMethod}: ${widget.order.paymentMethod ?? l10n.cash}',
            ),

            const SizedBox(height: 20),

            // Customer Info
            if (widget.order.notes != null &&
                widget.order.notes!.isNotEmpty) ...[
              Text(
                l10n.customerInfo,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(widget.order.notes!),
              const SizedBox(height: 20),
            ],

            // Items
            Text(
              l10n.orderDetails,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),

            Expanded(
              child: ListView.builder(
                itemCount: widget.order.items.length,
                itemBuilder: (context, index) {
                  final item = widget.order.items[index];
                  return Card(
                    child: ListTile(
                      title: Text(item.productName ?? 'N/A'),
                      subtitle: Text('${l10n.quantity}: ${item.quantity}'),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(_formatCurrency(item.unitPrice)),
                          Text(
                            _formatCurrency(item.totalAmount),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Total
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(l10n.subtotal),
                Text(_formatCurrency(widget.order.subtotal)),
              ],
            ),
            if (widget.order.discountAmount > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(l10n.discount),
                  Text('-${_formatCurrency(widget.order.discountAmount)}'),
                ],
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(l10n.tax),
                Text(_formatCurrency(widget.order.taxAmount)),
              ],
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.total,
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Text(
                  _formatCurrency(widget.order.totalAmount),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isGeneratingPdf ? null : _shareInvoice,
                    icon: _isGeneratingPdf
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.share),
                    label: Text(
                      _isGeneratingPdf ? l10n.generatingPdf : l10n.share,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => context.go('/pos'),
                    icon: const Icon(Icons.arrow_back),
                    label: Text(l10n.back),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
