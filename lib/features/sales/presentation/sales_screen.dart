import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/sales_provider.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/responsive.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../domain/entities/order.dart';
import 'widgets/sales_stats_card.dart';

class SalesScreen extends ConsumerStatefulWidget {
  const SalesScreen({super.key});

  @override
  ConsumerState<SalesScreen> createState() => _SalesScreenState();
}

class _SalesScreenState extends ConsumerState<SalesScreen> {
  OrderStatus? _selectedStatus;
  PaymentStatus? _selectedPaymentStatus;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final salesState = ref.watch(salesProvider);
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/dashboard'),
        ),
        title: Text(l10n.sales),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_shopping_cart),
            onPressed: () => _startNewSale(context),
            tooltip: l10n.newSaleButton,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () => _showSalesAnalytics(context),
            tooltip: l10n.analytics,
          ),
        ],
      ),
      body: Column(
        children: [
          // Sales Stats
          if (salesState.salesStats != null)
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              child: SalesStatsCard(stats: salesState.salesStats!),
            ),

          // Filter Section - TODO: Implement proper filter widget
          Container(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingM),
            child: Row(
              children: [
                Text('${l10n.filters}: '),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedStatus = null;
                      _selectedPaymentStatus = null;
                    });
                    ref.read(salesProvider.notifier).loadOrders();
                  },
                  child: Text(l10n.clearFilters),
                ),
              ],
            ),
          ),

          // Orders List
          Expanded(child: _buildOrdersList(context, salesState, responsive)),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _startNewSale(context),
        tooltip: l10n.newSale,
        child: const Icon(Icons.add_shopping_cart),
      ),
    );
  }

  Widget _buildOrdersList(
    BuildContext context,
    SalesState state,
    Responsive responsive,
  ) {
    final l10n = AppLocalizations.of(context);

    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppTheme.errorColor),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              l10n.errorOccurred,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              state.error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingM),
            ElevatedButton(
              onPressed: () {
                ref.read(salesProvider.notifier).loadOrders();
              },
              child: Text(l10n.retry),
            ),
          ],
        ),
      );
    }

    final filteredOrders = _getFilteredOrders(state.orders.cast<Order>());

    if (filteredOrders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: AppTheme.textSecondaryColor,
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              l10n.noOrdersFound,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              l10n.createFirstOrder,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),
            ElevatedButton.icon(
              onPressed: () => _startNewSale(context),
              icon: const Icon(Icons.add_shopping_cart),
              label: Text(l10n.newSale),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(salesProvider.notifier).loadOrders();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        itemCount: filteredOrders.length,
        itemBuilder: (context, index) {
          final order = filteredOrders[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppTheme.spacingM),
            child: Card(
              child: ListTile(
                title: Text(order.displayOrderNumber),
                subtitle: Text(
                  '${order.partnerName} - ${order.formattedTotal}',
                ),
                trailing: Text(order.statusDisplayText),
                onTap: () => _showOrderDetails(context, order),
              ),
            ),
          );
        },
      ),
    );
  }

  List<Order> _getFilteredOrders(List<Order> orders) {
    return orders.where((order) {
      if (_selectedStatus != null && order.status != _selectedStatus) {
        return false;
      }
      if (_selectedPaymentStatus != null &&
          order.paymentStatus != _selectedPaymentStatus) {
        return false;
      }
      return true;
    }).toList();
  }

  void _startNewSale(BuildContext context) {
    ref.read(salesProvider.notifier).startNewOrder();
    // Navigate to POS screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context).featureInDevelopment),
      ),
    );
  }

  void _showOrderDetails(BuildContext context, Order order) {
    final l10n = AppLocalizations.of(context);
    // Navigate to order details screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${l10n.order} ${order.displayOrderNumber}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${l10n.customer}: ${order.partnerName}'),
            Text('${l10n.status}: ${order.statusDisplayText}'),
            Text('${l10n.payment}: ${order.paymentStatusDisplayText}'),
            Text('${l10n.totalAmount}: ${order.formattedTotal}'),
            Text('${l10n.quantity}: ${order.totalItemsCount}'),
            if (order.notes != null && order.notes!.isNotEmpty) ...[
              const SizedBox(height: AppTheme.spacingS),
              Text('${l10n.notes}: ${order.notes}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.close),
          ),
        ],
      ),
    );
  }

  void _showSalesAnalytics(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context).featureInDevelopment),
      ),
    );
  }
}

// Extension to provide missing getters for Order
extension OrderDisplayExtension on Order {
  String get displayOrderNumber => orderNumber;

  String get partnerName => customerName ?? 'Walk-in Customer';

  String get statusDisplayText => status.displayName;

  String get paymentStatusDisplayText => paymentStatus.displayName;

  String get formattedTotal {
    return '${total.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}₫';
  }

  int get totalItemsCount => items.fold(0, (sum, item) => sum + item.quantity);

  String? get notes => note;
}
