import 'package:flutter/material.dart';
import '../../../../core/themes/app_theme.dart';
import '../../../../generated/l10n/app_localizations.dart';

class DateRangePicker extends StatefulWidget {
  final DateTimeRange? initialRange;
  final ValueChanged<DateTimeRange?> onRangeChanged;

  const DateRangePicker({
    super.key,
    this.initialRange,
    required this.onRangeChanged,
  });

  @override
  State<DateRangePicker> createState() => _DateRangePickerState();
}

class _DateRangePickerState extends State<DateRangePicker> {
  DateTimeRange? _selectedRange;

  @override
  void initState() {
    super.initState();
    _selectedRange = widget.initialRange;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.selectDateRange,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),
            
            // Quick date range buttons
            Wrap(
              spacing: AppTheme.spacingS,
              runSpacing: AppTheme.spacingS,
              children: [
                _buildQuickRangeButton(
                  context,
                  l10n.today,
                  _getTodayRange(),
                ),
                _buildQuickRangeButton(
                  context,
                  l10n.yesterday,
                  _getYesterdayRange(),
                ),
                _buildQuickRangeButton(
                  context,
                  l10n.thisWeek,
                  _getThisWeekRange(),
                ),
                _buildQuickRangeButton(
                  context,
                  l10n.lastWeek,
                  _getLastWeekRange(),
                ),
                _buildQuickRangeButton(
                  context,
                  l10n.thisMonth,
                  _getThisMonthRange(),
                ),
                _buildQuickRangeButton(
                  context,
                  l10n.lastMonth,
                  _getLastMonthRange(),
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingM),

            // Custom date range picker
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showCustomDateRangePicker(context),
                    icon: const Icon(Icons.date_range),
                    label: Text(
                      _selectedRange != null
                          ? '${_formatDate(_selectedRange!.start)} - ${_formatDate(_selectedRange!.end)}'
                          : l10n.selectCustomRange,
                    ),
                  ),
                ),
                if (_selectedRange != null) ...[
                  const SizedBox(width: AppTheme.spacingS),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _selectedRange = null;
                      });
                      widget.onRangeChanged(null);
                    },
                    icon: const Icon(Icons.clear),
                    tooltip: l10n.clearSelection,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickRangeButton(BuildContext context, String label, DateTimeRange range) {
    final isSelected = _selectedRange != null &&
        _selectedRange!.start.isAtSameMomentAs(range.start) &&
        _selectedRange!.end.isAtSameMomentAs(range.end);

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedRange = range;
          });
          widget.onRangeChanged(range);
        }
      },
    );
  }

  Future<void> _showCustomDateRangePicker(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _selectedRange,
    );

    if (picked != null) {
      setState(() {
        _selectedRange = picked;
      });
      widget.onRangeChanged(picked);
    }
  }

  DateTimeRange _getTodayRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    return DateTimeRange(
      start: today,
      end: today.add(const Duration(days: 1)).subtract(const Duration(microseconds: 1)),
    );
  }

  DateTimeRange _getYesterdayRange() {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    return DateTimeRange(
      start: yesterday,
      end: yesterday.add(const Duration(days: 1)).subtract(const Duration(microseconds: 1)),
    );
  }

  DateTimeRange _getThisWeekRange() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final start = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);
    final end = start.add(const Duration(days: 7)).subtract(const Duration(microseconds: 1));
    return DateTimeRange(start: start, end: end);
  }

  DateTimeRange _getLastWeekRange() {
    final thisWeek = _getThisWeekRange();
    final start = thisWeek.start.subtract(const Duration(days: 7));
    final end = thisWeek.start.subtract(const Duration(microseconds: 1));
    return DateTimeRange(start: start, end: end);
  }

  DateTimeRange _getThisMonthRange() {
    final now = DateTime.now();
    final start = DateTime(now.year, now.month, 1);
    final end = DateTime(now.year, now.month + 1, 1).subtract(const Duration(microseconds: 1));
    return DateTimeRange(start: start, end: end);
  }

  DateTimeRange _getLastMonthRange() {
    final now = DateTime.now();
    final start = DateTime(now.year, now.month - 1, 1);
    final end = DateTime(now.year, now.month, 1).subtract(const Duration(microseconds: 1));
    return DateTimeRange(start: start, end: end);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
