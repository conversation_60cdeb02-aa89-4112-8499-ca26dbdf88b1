import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/themes/app_theme.dart';
import '../../../core/utils/responsive.dart';
import '../../../generated/l10n/app_localizations.dart';
import 'widgets/report_card.dart';

class ReportsScreen extends ConsumerStatefulWidget {
  const ReportsScreen({super.key});

  @override
  ConsumerState<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends ConsumerState<ReportsScreen> {
  DateTimeRange? _selectedDateRange;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: Text(l10n.reports),
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: () => _showDateRangePicker(context),
            tooltip: l10n.selectDateRange,
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () => _exportReports(context),
            tooltip: l10n.exportReports,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(responsive.isMobile ? 6.0 : AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date Range Display
            if (_selectedDateRange != null)
              Card(
                child: Padding(
                  padding: EdgeInsets.all(
                    responsive.isMobile ? 8.0 : AppTheme.spacingM,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.date_range,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      SizedBox(
                        width: responsive.isMobile ? 4.0 : AppTheme.spacingS,
                      ),
                      Text(
                        '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _selectedDateRange = null;
                          });
                        },
                        child: Text(l10n.clearFilter),
                      ),
                    ],
                  ),
                ),
              ),

            SizedBox(height: responsive.isMobile ? 6.0 : AppTheme.spacingM),

            // Reports Grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: responsive.isMobile
                  ? 1
                  : responsive.isTablet
                  ? 2
                  : 3,
              crossAxisSpacing: responsive.isMobile ? 6.0 : AppTheme.spacingM,
              mainAxisSpacing: responsive.isMobile ? 6.0 : AppTheme.spacingM,
              childAspectRatio: responsive.isMobile ? 2.5 : 1.5,
              children: [
                ReportCard(
                  title: l10n.salesReport,
                  description: l10n.salesReportDescription,
                  icon: Icons.trending_up,
                  color: Colors.green,
                  onTap: () => _showSalesReport(context),
                ),
                ReportCard(
                  title: l10n.inventoryReport,
                  description: l10n.inventoryReportDescription,
                  icon: Icons.inventory,
                  color: Colors.blue,
                  onTap: () => _showInventoryReport(context),
                ),
                ReportCard(
                  title: l10n.financeReport,
                  description: l10n.financeReportDescription,
                  icon: Icons.account_balance,
                  color: Colors.purple,
                  onTap: () => _showFinanceReport(context),
                ),
                ReportCard(
                  title: l10n.customerReport,
                  description: l10n.customerReportDescription,
                  icon: Icons.people,
                  color: Colors.orange,
                  onTap: () => _showCustomerReport(context),
                ),
                ReportCard(
                  title: l10n.productReport,
                  description: l10n.productReportDescription,
                  icon: Icons.category,
                  color: Colors.teal,
                  onTap: () => _showProductReport(context),
                ),
                ReportCard(
                  title: l10n.profitReport,
                  description: l10n.profitReportDescription,
                  icon: Icons.monetization_on,
                  color: Colors.indigo,
                  onTap: () => _showProfitReport(context),
                ),
              ],
            ),

            SizedBox(height: responsive.isMobile ? 8.0 : AppTheme.spacingL),

            // Quick Stats Section
            Text(
              l10n.quickStats,
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: responsive.isMobile ? 6.0 : AppTheme.spacingM),

            // Quick stats cards would go here
            Card(
              child: Padding(
                padding: EdgeInsets.all(
                  responsive.isMobile ? 12.0 : AppTheme.spacingL,
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.analytics,
                      size: responsive.isMobile ? 48 : 64,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    SizedBox(
                      height: responsive.isMobile ? 6.0 : AppTheme.spacingM,
                    ),
                    Text(
                      l10n.reportsComingSoon,
                      style: Theme.of(context).textTheme.titleLarge,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(
                      height: responsive.isMobile ? 3.0 : AppTheme.spacingS,
                    ),
                    Text(
                      l10n.reportsComingSoonDescription,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDateRangePicker(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _exportReports(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context).featureInDevelopment),
      ),
    );
  }

  void _showSalesReport(BuildContext context) {
    _showReportDialog(context, AppLocalizations.of(context).salesReport);
  }

  void _showInventoryReport(BuildContext context) {
    _showReportDialog(context, AppLocalizations.of(context).inventoryReport);
  }

  void _showFinanceReport(BuildContext context) {
    _showReportDialog(context, AppLocalizations.of(context).financeReport);
  }

  void _showCustomerReport(BuildContext context) {
    _showReportDialog(context, AppLocalizations.of(context).customerReport);
  }

  void _showProductReport(BuildContext context) {
    _showReportDialog(context, AppLocalizations.of(context).productReport);
  }

  void _showProfitReport(BuildContext context) {
    _showReportDialog(context, AppLocalizations.of(context).profitReport);
  }

  void _showReportDialog(BuildContext context, String reportType) {
    final l10n = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(reportType),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.analytics,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              l10n.featureInDevelopment,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              l10n.reportWillBeAvailableSoon,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.close),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
