import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../data/services/reports_service.dart';
import '../domain/entities/report_data.dart';

// Simple Reports State
class ReportsSimpleState {
  final bool isLoading;
  final String? error;
  final SalesReportData? salesReport;
  final InventoryReportData? inventoryReport;
  final FinanceReportData? financeReport;
  final List<Map<String, dynamic>> salesTrend;
  final List<TopSellingProduct> topProducts;
  final List<ProductStockInfo> lowStockProducts;
  final DateTime startDate;
  final DateTime endDate;

  const ReportsSimpleState({
    this.isLoading = false,
    this.error,
    this.salesReport,
    this.inventoryReport,
    this.financeReport,
    this.salesTrend = const [],
    this.topProducts = const [],
    this.lowStockProducts = const [],
    required this.startDate,
    required this.endDate,
  });

  ReportsSimpleState copyWith({
    bool? isLoading,
    String? error,
    SalesReportData? salesReport,
    InventoryReportData? inventoryReport,
    FinanceReportData? financeReport,
    List<Map<String, dynamic>>? salesTrend,
    List<TopSellingProduct>? topProducts,
    List<ProductStockInfo>? lowStockProducts,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return ReportsSimpleState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      salesReport: salesReport ?? this.salesReport,
      inventoryReport: inventoryReport ?? this.inventoryReport,
      financeReport: financeReport ?? this.financeReport,
      salesTrend: salesTrend ?? this.salesTrend,
      topProducts: topProducts ?? this.topProducts,
      lowStockProducts: lowStockProducts ?? this.lowStockProducts,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }
}

// Reports Notifier
class ReportsSimpleNotifier extends StateNotifier<ReportsSimpleState> {
  ReportsSimpleNotifier()
    : super(
        ReportsSimpleState(
          startDate: DateTime.now().subtract(const Duration(days: 30)),
          endDate: DateTime.now(),
        ),
      ) {
    loadAllReports();
  }

  Future<void> loadAllReports() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final salesReport = await ReportsService.getSalesReport(
        startDate: state.startDate,
        endDate: state.endDate,
      );

      final inventoryReport = await ReportsService.getInventoryReport();

      final financeReport = await ReportsService.getFinanceReport(
        startDate: state.startDate,
        endDate: state.endDate,
      );

      final salesTrend = await ReportsService.getSalesTrend(
        startDate: state.startDate,
        endDate: state.endDate,
      );

      final topProducts = await ReportsService.getTopSellingProducts(
        startDate: state.startDate,
        endDate: state.endDate,
        limit: 5,
      );

      final lowStockProducts = await ReportsService.getLowStockProducts(
        limit: 10,
      );

      state = state.copyWith(
        isLoading: false,
        salesReport: salesReport,
        inventoryReport: inventoryReport,
        financeReport: financeReport,
        salesTrend: salesTrend,
        topProducts: topProducts,
        lowStockProducts: lowStockProducts,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void setDateRange(DateTime startDate, DateTime endDate) {
    state = state.copyWith(startDate: startDate, endDate: endDate);
    loadAllReports();
  }
}

// Provider
final reportsSimpleProvider =
    StateNotifierProvider<ReportsSimpleNotifier, ReportsSimpleState>(
      (ref) => ReportsSimpleNotifier(),
    );

class ReportsScreenSimple extends ConsumerWidget {
  const ReportsScreenSimple({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(reportsSimpleProvider);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: const Text('Báo cáo'),
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: () => _showDateRangePicker(context, ref),
            tooltip: 'Chọn khoảng thời gian',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () =>
                ref.read(reportsSimpleProvider.notifier).loadAllReports(),
            tooltip: 'Làm mới',
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
          ? AppErrorWidget(
              message: state.error!,
              onRetry: () =>
                  ref.read(reportsSimpleProvider.notifier).loadAllReports(),
            )
          : _buildContent(context, ref, state),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    ReportsSimpleState state,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Range Display
          _buildDateRangeCard(context, state),

          const SizedBox(height: 16),

          // Quick Stats
          _buildQuickStats(context, state),

          const SizedBox(height: 24),

          // Sales Report Section
          _buildSalesReportSection(context, state),

          const SizedBox(height: 24),

          // Inventory Report Section
          _buildInventoryReportSection(context, state),

          const SizedBox(height: 24),

          // Finance Report Section
          _buildFinanceReportSection(context, state),

          const SizedBox(height: 24),

          // Top Products Section
          _buildTopProductsSection(context, state),

          const SizedBox(height: 24),

          // Low Stock Alert Section
          _buildLowStockSection(context, state),
        ],
      ),
    );
  }

  Widget _buildDateRangeCard(BuildContext context, ReportsSimpleState state) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // If too narrow, use column layout
            if (constraints.maxWidth < 400) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.date_range,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Khoảng thời gian:',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.w500,
                              fontSize: MediaQuery.of(context).size.width < 600
                                  ? 12.0
                                  : 14.0,
                            ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Padding(
                    padding: const EdgeInsets.only(left: 32),
                    child: Text(
                      '${dateFormat.format(state.startDate)} - ${dateFormat.format(state.endDate)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: MediaQuery.of(context).size.width < 600
                            ? 11.0
                            : 13.0,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              );
            } else {
              return Row(
                children: [
                  Icon(Icons.date_range, color: Theme.of(context).primaryColor),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Khoảng thời gian: ${dateFormat.format(state.startDate)} - ${dateFormat.format(state.endDate)}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: MediaQuery.of(context).size.width < 600
                            ? 12.0
                            : 14.0,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, ReportsSimpleState state) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tổng quan',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            // If too narrow, use 2x2 grid
            if (constraints.maxWidth < 800) {
              return Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          context,
                          'Doanh thu',
                          currencyFormat.format(
                            state.salesReport?.totalRevenue ?? 0,
                          ),
                          Icons.trending_up,
                          Colors.green,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          context,
                          'Lợi nhuận',
                          currencyFormat.format(
                            state.salesReport?.grossProfit ?? 0,
                          ),
                          Icons.monetization_on,
                          Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          context,
                          'Đơn hàng',
                          '${state.salesReport?.totalOrders ?? 0}',
                          Icons.shopping_cart,
                          Colors.orange,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          context,
                          'Tồn kho',
                          currencyFormat.format(
                            state.inventoryReport?.totalStockValue ?? 0,
                          ),
                          Icons.inventory,
                          Colors.purple,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            } else {
              return Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'Doanh thu',
                      currencyFormat.format(
                        state.salesReport?.totalRevenue ?? 0,
                      ),
                      Icons.trending_up,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'Lợi nhuận',
                      currencyFormat.format(
                        state.salesReport?.grossProfit ?? 0,
                      ),
                      Icons.monetization_on,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'Đơn hàng',
                      '${state.salesReport?.totalOrders ?? 0}',
                      Icons.shopping_cart,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'Tồn kho',
                      currencyFormat.format(
                        state.inventoryReport?.totalStockValue ?? 0,
                      ),
                      Icons.inventory,
                      Colors.purple,
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return AppCard(
      child: Padding(
        padding: EdgeInsets.all(
          MediaQuery.of(context).size.width < 600 ? 12.0 : 16.0,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: MediaQuery.of(context).size.width < 600 ? 24.0 : 32.0,
            ),
            SizedBox(
              height: MediaQuery.of(context).size.width < 600 ? 6.0 : 8.0,
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: MediaQuery.of(context).size.width < 600 ? 12.0 : 14.0,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontSize: MediaQuery.of(context).size.width < 600 ? 10.0 : 12.0,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesReportSection(
    BuildContext context,
    ReportsSimpleState state,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final salesReport = state.salesReport;

    if (salesReport == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Báo cáo bán hàng',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        AppCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        'Tổng doanh thu',
                        currencyFormat.format(salesReport.totalRevenue),
                        Icons.attach_money,
                        Colors.green,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        'Tổng chi phí',
                        currencyFormat.format(salesReport.totalCost),
                        Icons.money_off,
                        Colors.red,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        'Lợi nhuận gộp',
                        currencyFormat.format(salesReport.grossProfit),
                        Icons.trending_up,
                        Colors.blue,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        'Tỷ suất lợi nhuận',
                        '${salesReport.profitMargin.toStringAsFixed(1)}%',
                        Icons.percent,
                        Colors.purple,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        'Số đơn hàng',
                        '${salesReport.totalOrders}',
                        Icons.shopping_cart,
                        Colors.orange,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        'Giá trị TB/đơn',
                        currencyFormat.format(salesReport.averageOrderValue),
                        Icons.calculate,
                        Colors.teal,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryReportSection(
    BuildContext context,
    ReportsSimpleState state,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final inventoryReport = state.inventoryReport;

    if (inventoryReport == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Báo cáo tồn kho',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        AppCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        'Tổng sản phẩm',
                        '${inventoryReport.totalProducts}',
                        Icons.inventory_2,
                        Colors.blue,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        'Giá trị tồn kho',
                        currencyFormat.format(inventoryReport.totalStockValue),
                        Icons.monetization_on,
                        Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        'Sắp hết hàng',
                        '${inventoryReport.lowStockProducts}',
                        Icons.warning,
                        Colors.orange,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        'Hết hàng',
                        '${inventoryReport.outOfStockProducts}',
                        Icons.error,
                        Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFinanceReportSection(
    BuildContext context,
    ReportsSimpleState state,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final financeReport = state.financeReport;

    if (financeReport == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Báo cáo tài chính',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        AppCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        'Tổng thu',
                        currencyFormat.format(financeReport.totalReceipts),
                        Icons.trending_up,
                        Colors.green,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        'Tổng chi',
                        currencyFormat.format(financeReport.totalPayments),
                        Icons.trending_down,
                        Colors.red,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        'Dòng tiền ròng',
                        currencyFormat.format(financeReport.netCashFlow),
                        financeReport.netCashFlow >= 0
                            ? Icons.arrow_upward
                            : Icons.arrow_downward,
                        financeReport.netCashFlow >= 0
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        'Số dư cuối kỳ',
                        currencyFormat.format(financeReport.closingBalance),
                        Icons.account_balance_wallet,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTopProductsSection(
    BuildContext context,
    ReportsSimpleState state,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sản phẩm bán chạy',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        AppCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: state.topProducts.map((product) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final isSmallScreen =
                          MediaQuery.of(context).size.width < 600;
                      final isVerySmallScreen =
                          MediaQuery.of(context).size.width < 400;

                      // If very narrow, use column layout
                      if (constraints.maxWidth < 300) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: isSmallScreen ? 32 : 40,
                                  height: isSmallScreen ? 32 : 40,
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.inventory_2,
                                    color: Colors.blue,
                                    size: isSmallScreen ? 16 : 20,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    product.productName,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontSize: isVerySmallScreen
                                              ? 11.0
                                              : isSmallScreen
                                              ? 12.0
                                              : 14.0,
                                        ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Đã bán: ${product.quantitySold}',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: Colors.grey[600],
                                        fontSize: isVerySmallScreen
                                            ? 9.0
                                            : isSmallScreen
                                            ? 10.0
                                            : 12.0,
                                      ),
                                ),
                                Text(
                                  currencyFormat.format(product.revenue),
                                  style: Theme.of(context).textTheme.titleSmall
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green,
                                        fontSize: isVerySmallScreen
                                            ? 10.0
                                            : isSmallScreen
                                            ? 11.0
                                            : 13.0,
                                      ),
                                ),
                              ],
                            ),
                          ],
                        );
                      } else {
                        return Row(
                          children: [
                            Container(
                              width: isSmallScreen ? 32 : 40,
                              height: isSmallScreen ? 32 : 40,
                              decoration: BoxDecoration(
                                color: Colors.blue.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.inventory_2,
                                color: Colors.blue,
                                size: isSmallScreen ? 16 : 20,
                              ),
                            ),
                            SizedBox(width: isSmallScreen ? 8 : 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    product.productName,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontSize: isVerySmallScreen
                                              ? 11.0
                                              : isSmallScreen
                                              ? 12.0
                                              : 14.0,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    'Đã bán: ${product.quantitySold}',
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(
                                          color: Colors.grey[600],
                                          fontSize: isVerySmallScreen
                                              ? 9.0
                                              : isSmallScreen
                                              ? 10.0
                                              : 12.0,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: isSmallScreen ? 80 : 120,
                              child: Text(
                                currencyFormat.format(product.revenue),
                                style: Theme.of(context).textTheme.titleSmall
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green,
                                      fontSize: isVerySmallScreen
                                          ? 10.0
                                          : isSmallScreen
                                          ? 11.0
                                          : 13.0,
                                    ),
                                textAlign: TextAlign.end,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        );
                      }
                    },
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLowStockSection(BuildContext context, ReportsSimpleState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cảnh báo tồn kho',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        AppCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: state.lowStockProducts.isEmpty
                ? Center(
                    child: Column(
                      children: [
                        Icon(Icons.check_circle, size: 48, color: Colors.green),
                        const SizedBox(height: 8),
                        Text(
                          'Tất cả sản phẩm đều có đủ tồn kho',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(color: Colors.green),
                        ),
                      ],
                    ),
                  )
                : Column(
                    children: state.lowStockProducts.map((product) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            final isSmallScreen =
                                MediaQuery.of(context).size.width < 600;
                            final isVerySmallScreen =
                                MediaQuery.of(context).size.width < 400;

                            // If very narrow, use column layout
                            if (constraints.maxWidth < 300) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        width: isSmallScreen ? 32 : 40,
                                        height: isSmallScreen ? 32 : 40,
                                        decoration: BoxDecoration(
                                          color: product.isOutOfStock
                                              ? Colors.red.withValues(
                                                  alpha: 0.1,
                                                )
                                              : Colors.orange.withValues(
                                                  alpha: 0.1,
                                                ),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Icon(
                                          product.isOutOfStock
                                              ? Icons.error
                                              : Icons.warning,
                                          color: product.isOutOfStock
                                              ? Colors.red
                                              : Colors.orange,
                                          size: isSmallScreen ? 16 : 20,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          product.productName,
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleSmall
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                fontSize: isVerySmallScreen
                                                    ? 11.0
                                                    : isSmallScreen
                                                    ? 12.0
                                                    : 14.0,
                                              ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          'Tồn: ${product.currentStock} / Tối thiểu: ${product.minStock}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall
                                              ?.copyWith(
                                                color: Colors.grey[600],
                                                fontSize: isVerySmallScreen
                                                    ? 9.0
                                                    : isSmallScreen
                                                    ? 10.0
                                                    : 12.0,
                                              ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: isSmallScreen ? 6.0 : 8.0,
                                          vertical: isSmallScreen ? 2.0 : 4.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: product.isOutOfStock
                                              ? Colors.red.withValues(
                                                  alpha: 0.1,
                                                )
                                              : Colors.orange.withValues(
                                                  alpha: 0.1,
                                                ),
                                          borderRadius: BorderRadius.circular(
                                            isSmallScreen ? 8.0 : 12.0,
                                          ),
                                        ),
                                        child: Text(
                                          product.isOutOfStock
                                              ? 'Hết hàng'
                                              : 'Sắp hết',
                                          style: TextStyle(
                                            color: product.isOutOfStock
                                                ? Colors.red
                                                : Colors.orange,
                                            fontSize: isVerySmallScreen
                                                ? 9.0
                                                : isSmallScreen
                                                ? 10.0
                                                : 12.0,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            } else {
                              return Row(
                                children: [
                                  Container(
                                    width: isSmallScreen ? 32 : 40,
                                    height: isSmallScreen ? 32 : 40,
                                    decoration: BoxDecoration(
                                      color: product.isOutOfStock
                                          ? Colors.red.withValues(alpha: 0.1)
                                          : Colors.orange.withValues(
                                              alpha: 0.1,
                                            ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      product.isOutOfStock
                                          ? Icons.error
                                          : Icons.warning,
                                      color: product.isOutOfStock
                                          ? Colors.red
                                          : Colors.orange,
                                      size: isSmallScreen ? 16 : 20,
                                    ),
                                  ),
                                  SizedBox(width: isSmallScreen ? 8 : 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          product.productName,
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleSmall
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                fontSize: isVerySmallScreen
                                                    ? 11.0
                                                    : isSmallScreen
                                                    ? 12.0
                                                    : 14.0,
                                              ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        Text(
                                          'Tồn: ${product.currentStock} / Tối thiểu: ${product.minStock}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall
                                              ?.copyWith(
                                                color: Colors.grey[600],
                                                fontSize: isVerySmallScreen
                                                    ? 9.0
                                                    : isSmallScreen
                                                    ? 10.0
                                                    : 12.0,
                                              ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: isSmallScreen ? 6.0 : 8.0,
                                      vertical: isSmallScreen ? 2.0 : 4.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: product.isOutOfStock
                                          ? Colors.red.withValues(alpha: 0.1)
                                          : Colors.orange.withValues(
                                              alpha: 0.1,
                                            ),
                                      borderRadius: BorderRadius.circular(
                                        isSmallScreen ? 8.0 : 12.0,
                                      ),
                                    ),
                                    child: Text(
                                      product.isOutOfStock
                                          ? 'Hết hàng'
                                          : 'Sắp hết',
                                      style: TextStyle(
                                        color: product.isOutOfStock
                                            ? Colors.red
                                            : Colors.orange,
                                        fontSize: isVerySmallScreen
                                            ? 9.0
                                            : isSmallScreen
                                            ? 10.0
                                            : 12.0,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            }
                          },
                        ),
                      );
                    }).toList(),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildReportItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Builder(
      builder: (context) {
        final isSmallScreen = MediaQuery.of(context).size.width < 600;
        final isVerySmallScreen = MediaQuery.of(context).size.width < 400;

        return Container(
          padding: EdgeInsets.all(isSmallScreen ? 8.0 : 12.0),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Icon(icon, color: color, size: isSmallScreen ? 20.0 : 24.0),
              SizedBox(height: isSmallScreen ? 4.0 : 8.0),
              Text(
                value,
                style: TextStyle(
                  fontSize: isVerySmallScreen
                      ? 11.0
                      : isSmallScreen
                      ? 13.0
                      : 16.0,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: isSmallScreen ? 2.0 : 4.0),
              Text(
                title,
                style: TextStyle(
                  fontSize: isVerySmallScreen
                      ? 9.0
                      : isSmallScreen
                      ? 10.0
                      : 12.0,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        );
      },
    );
  }

  void _showDateRangePicker(BuildContext context, WidgetRef ref) async {
    final state = ref.read(reportsSimpleProvider);

    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: state.startDate,
        end: state.endDate,
      ),
    );

    if (picked != null) {
      ref
          .read(reportsSimpleProvider.notifier)
          .setDateRange(picked.start, picked.end);
    }
  }
}
