import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:city_pos/generated/l10n/app_localizations.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../data/services/reports_service.dart';
import '../domain/entities/report_data.dart';

// Extension để truy cập localization dễ dàng hơn
extension LocalizationExtension on BuildContext {
  AppLocalizations get l10n {
    return AppLocalizations.of(this);
  }
}

// Simple Reports State
class ReportsSimpleState {
  final bool isLoading;
  final String? error;
  final SalesReportData? salesReport;
  final InventoryReportData? inventoryReport;
  final FinanceReportData? financeReport;
  final List<Map<String, dynamic>> salesTrend;
  final List<TopSellingProduct> topProducts;
  final List<ProductStockInfo> lowStockProducts;
  final DateTime startDate;
  final DateTime endDate;

  const ReportsSimpleState({
    this.isLoading = false,
    this.error,
    this.salesReport,
    this.inventoryReport,
    this.financeReport,
    this.salesTrend = const [],
    this.topProducts = const [],
    this.lowStockProducts = const [],
    required this.startDate,
    required this.endDate,
  });

  ReportsSimpleState copyWith({
    bool? isLoading,
    String? error,
    SalesReportData? salesReport,
    InventoryReportData? inventoryReport,
    FinanceReportData? financeReport,
    List<Map<String, dynamic>>? salesTrend,
    List<TopSellingProduct>? topProducts,
    List<ProductStockInfo>? lowStockProducts,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return ReportsSimpleState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      salesReport: salesReport ?? this.salesReport,
      inventoryReport: inventoryReport ?? this.inventoryReport,
      financeReport: financeReport ?? this.financeReport,
      salesTrend: salesTrend ?? this.salesTrend,
      topProducts: topProducts ?? this.topProducts,
      lowStockProducts: lowStockProducts ?? this.lowStockProducts,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }
}

// Reports Notifier
class ReportsSimpleNotifier extends StateNotifier<ReportsSimpleState> {
  ReportsSimpleNotifier()
    : super(
        ReportsSimpleState(
          startDate: DateTime.now().subtract(const Duration(days: 30)),
          endDate: DateTime.now(),
        ),
      ) {
    loadAllReports();
  }

  Future<void> loadAllReports() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final salesReport = await ReportsService.getSalesReport(
        startDate: state.startDate,
        endDate: state.endDate,
      );

      final inventoryReport = await ReportsService.getInventoryReport();

      final financeReport = await ReportsService.getFinanceReport(
        startDate: state.startDate,
        endDate: state.endDate,
      );

      final salesTrend = await ReportsService.getSalesTrend(
        startDate: state.startDate,
        endDate: state.endDate,
      );

      final topProducts = await ReportsService.getTopSellingProducts(
        startDate: state.startDate,
        endDate: state.endDate,
        limit: 5,
      );

      final lowStockProducts = await ReportsService.getLowStockProducts(
        limit: 10,
      );

      state = state.copyWith(
        isLoading: false,
        salesReport: salesReport,
        inventoryReport: inventoryReport,
        financeReport: financeReport,
        salesTrend: salesTrend,
        topProducts: topProducts,
        lowStockProducts: lowStockProducts,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void setDateRange(DateTime startDate, DateTime endDate) {
    state = state.copyWith(startDate: startDate, endDate: endDate);
    loadAllReports();
  }
}

// Provider
final reportsSimpleProvider =
    StateNotifierProvider<ReportsSimpleNotifier, ReportsSimpleState>(
      (ref) => ReportsSimpleNotifier(),
    );

class ReportsScreenSimple extends ConsumerWidget {
  const ReportsScreenSimple({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(reportsSimpleProvider);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: Text(context.l10n.reportsScreenTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: () => _showDateRangePicker(context, ref),
            tooltip: context.l10n.selectDateRange,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () =>
                ref.read(reportsSimpleProvider.notifier).loadAllReports(),
            tooltip: context.l10n.refresh,
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
          ? AppErrorWidget(
              message: state.error!,
              onRetry: () =>
                  ref.read(reportsSimpleProvider.notifier).loadAllReports(),
            )
          : _buildContent(context, ref, state),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    ReportsSimpleState state,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Range Display
          _buildDateRangeCard(context, state),

          const SizedBox(height: 16),

          // Quick Stats
          _buildQuickStats(context, state),

          const SizedBox(height: 24),

          // Sales Report Section
          _buildSalesReportSection(context, state),

          const SizedBox(height: 24),

          // Inventory Report Section
          _buildInventoryReportSection(context, state),

          const SizedBox(height: 24),

          // Finance Report Section
          _buildFinanceReportSection(context, state),

          const SizedBox(height: 24),

          // Top Products Section
          _buildTopProductsSection(context, state),

          const SizedBox(height: 24),

          // Low Stock Alert Section
          _buildLowStockSection(context, state),
        ],
      ),
    );
  }

  Widget _buildDateRangeCard(BuildContext context, ReportsSimpleState state) {
    final dateFormat = DateFormat('dd/MM/yyyy');
    final dateRangeText = '${dateFormat.format(state.startDate)} - ${dateFormat.format(state.endDate)}';
    // Sử dụng localization cho tiêu đề khoảng thời gian
    final dateRangePrefix = context.l10n.dateRange;

    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // If too narrow, use column layout
            if (constraints.maxWidth < 400) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.date_range,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '$dateRangePrefix:',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.w500,
                              fontSize: MediaQuery.of(context).size.width < 600
                                  ? 12.0
                                  : 14.0,
                            ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Padding(
                    padding: const EdgeInsets.only(left: 32),
                    child: Text(
                      dateRangeText,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: MediaQuery.of(context).size.width < 600
                            ? 11.0
                            : 13.0,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              );
            } else {
              return Row(
                children: [
                  Icon(Icons.date_range, color: Theme.of(context).primaryColor),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '$dateRangePrefix: $dateRangeText',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: MediaQuery.of(context).size.width < 600
                            ? 12.0
                            : 14.0,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, ReportsSimpleState state) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.overview,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            // If too narrow, use 2x2 grid with tighter spacing
            if (constraints.maxWidth < 800) {
              return Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          context,
                          context.l10n.revenue,
                          currencyFormat.format(
                            state.salesReport?.totalRevenue ?? 0,
                          ),
                          Icons.trending_up,
                          Colors.green,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          context,
                          context.l10n.profit,
                          currencyFormat.format(
                            state.salesReport?.grossProfit ?? 0,
                          ),
                          Icons.monetization_on,
                          Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          context,
                          context.l10n.orderCount,
                          '${state.salesReport?.totalOrders ?? 0}',
                          Icons.shopping_cart,
                          Colors.orange,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          context,
                          context.l10n.inventoryValue,
                          currencyFormat.format(
                            state.inventoryReport?.totalStockValue ?? 0,
                          ),
                          Icons.inventory,
                          Colors.purple,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            } else {
              return Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context,
                      context.l10n.revenue,
                      currencyFormat.format(
                        state.salesReport?.totalRevenue ?? 0,
                      ),
                      Icons.trending_up,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      context.l10n.profit,
                      currencyFormat.format(
                        state.salesReport?.grossProfit ?? 0,
                      ),
                      Icons.monetization_on,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      context.l10n.orderCount,
                      '${state.salesReport?.totalOrders ?? 0}',
                      Icons.shopping_cart,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      context.l10n.inventoryValue,
                      currencyFormat.format(
                        state.inventoryReport?.totalStockValue ?? 0,
                      ),
                      Icons.inventory,
                      Colors.purple,
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return AppCard(
      child: Padding(
        padding: EdgeInsets.all(
          isMobile ? 16.0 : 16.0,
        ), // Increased mobile padding from 12 to 16
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: isMobile
                  ? 32.0
                  : 32.0, // Increased mobile icon size from 24 to 32
            ),
            SizedBox(
              height: isMobile
                  ? 8.0
                  : 8.0, // Increased mobile spacing from 6 to 8
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: isMobile
                    ? 14.0
                    : 14.0, // Increased mobile font size from 12 to 14
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4.0), // Added consistent spacing
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontSize: isMobile
                    ? 12.0
                    : 12.0, // Increased mobile font size from 10 to 12
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesReportSection(
    BuildContext context,
    ReportsSimpleState state,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final salesReport = state.salesReport;

    if (salesReport == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.salesReport,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        AppCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.totalRevenue,
                        currencyFormat.format(salesReport.totalRevenue),
                        Icons.attach_money,
                        Colors.green,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.totalCost,
                        currencyFormat.format(salesReport.totalCost),
                        Icons.money_off,
                        Colors.red,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.grossProfit,
                        currencyFormat.format(salesReport.grossProfit),
                        Icons.trending_up,
                        Colors.blue,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.profitMargin,
                        '${salesReport.profitMargin.toStringAsFixed(1)}%',
                        Icons.percent,
                        Colors.purple,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.orderCount,
                        '${salesReport.totalOrders}',
                        Icons.shopping_cart,
                        Colors.orange,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.averageOrderValue,
                        currencyFormat.format(salesReport.averageOrderValue),
                        Icons.calculate,
                        Colors.teal,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFinanceReportSection(
    BuildContext context,
    ReportsSimpleState state,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final financeReport = state.financeReport;

    if (financeReport == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.financeReport,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        AppCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.totalIncome,
                        currencyFormat.format(financeReport.totalReceipts),
                        Icons.trending_up,
                        Colors.green,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600 ? 8.0 : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.totalExpense,
                        currencyFormat.format(financeReport.totalPayments),
                        Icons.trending_down,
                        Colors.red,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.netCashFlow,
                        currencyFormat.format(financeReport.netCashFlow),
                        financeReport.netCashFlow >= 0
                            ? Icons.arrow_upward
                            : Icons.arrow_downward,
                        financeReport.netCashFlow >= 0
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600 ? 8.0 : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.endingBalance,
                        currencyFormat.format(financeReport.closingBalance),
                        Icons.account_balance_wallet,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryReportSection(
    BuildContext context,
    ReportsSimpleState state,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final inventoryReport = state.inventoryReport;

    if (inventoryReport == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.inventoryReport,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        AppCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.totalProducts,
                        '${inventoryReport.totalProducts}',
                        Icons.inventory_2,
                        Colors.blue,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
context.l10n.inventoryValue,
                        currencyFormat.format(inventoryReport.totalStockValue),
                        Icons.monetization_on,
                        Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.lowStockItems,
                        '${inventoryReport.lowStockProducts}',
                        Icons.warning,
                        Colors.orange,
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 12.0,
                    ),
                    Expanded(
                      child: _buildReportItem(
                        context.l10n.outOfStockItems,
                        '${inventoryReport.outOfStockProducts}',
                        Icons.error,
                        Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTopProductsSection(
    BuildContext context,
    ReportsSimpleState state,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final l10n = context.l10n;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.topSellingProducts,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        AppCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: state.topProducts.map((product) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final isSmallScreen =
                          MediaQuery.of(context).size.width < 600;
                      final isVerySmallScreen =
                          MediaQuery.of(context).size.width < 400;

                      // If very narrow, use column layout
                      if (constraints.maxWidth < 300) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: isSmallScreen ? 32 : 40,
                                  height: isSmallScreen ? 32 : 40,
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.inventory_2,
                                    color: Colors.blue,
                                    size: isSmallScreen ? 16 : 20,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    product.productName,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontSize: isVerySmallScreen
                                              ? 11.0
                                              : isSmallScreen
                                              ? 12.0
                                              : 14.0,
                                        ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  '${context.l10n.sold}: ${product.quantitySold}',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: Colors.grey[600],
                                        fontSize: isVerySmallScreen
                                            ? 9.0
                                            : isSmallScreen
                                            ? 10.0
                                            : 12.0,
                                      ),
                                ),
                                Text(
                                  currencyFormat.format(product.revenue),
                                  style: Theme.of(context).textTheme.titleSmall
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green,
                                        fontSize: isVerySmallScreen
                                            ? 10.0
                                            : isSmallScreen
                                            ? 11.0
                                            : 13.0,
                                      ),
                                ),
                              ],
                            ),
                          ],
                        );
                      } else {
                        return Row(
                          children: [
                            Container(
                              width: isSmallScreen ? 32 : 40,
                              height: isSmallScreen ? 32 : 40,
                              decoration: BoxDecoration(
                                color: Colors.blue.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.inventory_2,
                                color: Colors.blue,
                                size: isSmallScreen ? 16 : 20,
                              ),
                            ),
                            SizedBox(width: isSmallScreen ? 8 : 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    product.productName,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontSize: isVerySmallScreen
                                              ? 11.0
                                              : isSmallScreen
                                              ? 12.0
                                              : 14.0,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    '${context.l10n.sold}: ${product.quantitySold}',
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(
                                          color: Colors.grey[600],
                                          fontSize: isVerySmallScreen
                                              ? 9.0
                                              : isSmallScreen
                                              ? 10.0
                                              : 12.0,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: isSmallScreen ? 80 : 120,
                              child: Text(
                                currencyFormat.format(product.revenue),
                                style: Theme.of(context).textTheme.titleSmall
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green,
                                      fontSize: isVerySmallScreen
                                          ? 10.0
                                          : isSmallScreen
                                          ? 11.0
                                          : 13.0,
                                    ),
                                textAlign: TextAlign.end,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        );
                      }
                    },
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLowStockSection(BuildContext context, ReportsSimpleState state) {
    final l10n = context.l10n;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.inventoryAlerts,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
          ),
        ),
        const SizedBox(height: 16),
        AppCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: state.lowStockProducts.isEmpty
                ? Center(
                    child: Column(
                      children: [
                        Icon(Icons.check_circle, size: 48, color: Colors.green),
                        const SizedBox(height: 8),
                        Text(
                          l10n.allProductsInStock,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(color: Colors.green),
                        ),
                      ],
                    ),
                  )
                : Column(
                    children: state.lowStockProducts.map((product) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            final isSmallScreen =
                                MediaQuery.of(context).size.width < 600;
                            final isVerySmallScreen =
                                MediaQuery.of(context).size.width < 400;

                            // If very narrow, use column layout
                            if (constraints.maxWidth < 300) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        width: isSmallScreen ? 32 : 40,
                                        height: isSmallScreen ? 32 : 40,
                                        decoration: BoxDecoration(
                                          color: product.isOutOfStock
                                              ? Colors.red.withValues(
                                                  alpha: 0.1,
                                                )
                                              : Colors.orange.withValues(
                                                  alpha: 0.1,
                                                ),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Icon(
                                          product.isOutOfStock
                                              ? Icons.error
                                              : Icons.warning,
                                          color: product.isOutOfStock
                                              ? Colors.red
                                              : Colors.orange,
                                          size: isSmallScreen ? 16 : 20,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          product.productName,
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleSmall
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                fontSize: isVerySmallScreen
                                                    ? 11.0
                                                    : isSmallScreen
                                                    ? 12.0
                                                    : 14.0,
                                              ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          '${"Tồn"}: ${product.currentStock} / ${"Tối thiểu"}: ${product.minStock}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall
                                              ?.copyWith(
                                                color: Colors.grey[600],
                                                fontSize: isVerySmallScreen
                                                    ? 9.0
                                                    : isSmallScreen
                                                    ? 10.0
                                                    : 12.0,
                                              ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: isSmallScreen ? 6.0 : 8.0,
                                          vertical: isSmallScreen ? 2.0 : 4.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: product.isOutOfStock
                                              ? Colors.red.withValues(
                                                  alpha: 0.1,
                                                )
                                              : Colors.orange.withValues(
                                                  alpha: 0.1,
                                                ),
                                          borderRadius: BorderRadius.circular(
                                            isSmallScreen ? 8.0 : 12.0,
                                          ),
                                        ),
                                        child: Text(
                                          product.isOutOfStock
                                              ? context.l10n.outOfStock
                                              : context.l10n.lowStock,
                                          style: TextStyle(
                                            color: product.isOutOfStock
                                                ? Colors.red
                                                : Colors.orange,
                                            fontSize: isVerySmallScreen
                                                ? 9.0
                                                : isSmallScreen
                                                ? 10.0
                                                : 12.0,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            } else {
                              return Row(
                                children: [
                                  Container(
                                    width: isSmallScreen ? 32 : 40,
                                    height: isSmallScreen ? 32 : 40,
                                    decoration: BoxDecoration(
                                      color: product.isOutOfStock
                                          ? Colors.red.withValues(alpha: 0.1)
                                          : Colors.orange.withValues(
                                              alpha: 0.1,
                                            ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      product.isOutOfStock
                                          ? Icons.error
                                          : Icons.warning,
                                      color: product.isOutOfStock
                                          ? Colors.red
                                          : Colors.orange,
                                      size: isSmallScreen ? 16 : 20,
                                    ),
                                  ),
                                  SizedBox(width: isSmallScreen ? 8 : 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          product.productName,
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleSmall
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                fontSize: isVerySmallScreen
                                                    ? 11.0
                                                    : isSmallScreen
                                                    ? 12.0
                                                    : 14.0,
                                              ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        Text(
                                          '${"Tồn"}: ${product.currentStock} / ${"Tối thiểu"}: ${product.minStock}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall
                                              ?.copyWith(
                                                color: Colors.grey[600],
                                                fontSize: isVerySmallScreen
                                                    ? 9.0
                                                    : isSmallScreen
                                                    ? 10.0
                                                    : 12.0,
                                              ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: isSmallScreen ? 6.0 : 8.0,
                                      vertical: isSmallScreen ? 2.0 : 4.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: product.isOutOfStock
                                          ? Colors.red.withValues(alpha: 0.1)
                                          : Colors.orange.withValues(
                                              alpha: 0.1,
                                            ),
                                      borderRadius: BorderRadius.circular(
                                        isSmallScreen ? 8.0 : 12.0,
                                      ),
                                    ),
                                    child: Text(
                                      product.isOutOfStock
                                          ? context.l10n.outOfStock
                                          : context.l10n.lowStock,
                                      style: TextStyle(
                                        color: product.isOutOfStock
                                            ? Colors.red
                                            : Colors.orange,
                                        fontSize: isVerySmallScreen
                                            ? 9.0
                                            : isSmallScreen
                                            ? 10.0
                                            : 12.0,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            }
                          },
                        ),
                      );
                    }).toList(),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildReportItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Builder(
      builder: (context) {
        final isSmallScreen = MediaQuery.of(context).size.width < 600;
        final isVerySmallScreen = MediaQuery.of(context).size.width < 400;

        return Container(
          padding: EdgeInsets.all(isSmallScreen ? 8.0 : 12.0),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Icon(icon, color: color, size: isSmallScreen ? 20.0 : 24.0),
              SizedBox(height: isSmallScreen ? 4.0 : 8.0),
              Text(
                value,
                style: TextStyle(
                  fontSize: isVerySmallScreen
                      ? 11.0
                      : isSmallScreen
                      ? 13.0
                      : 16.0,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: isSmallScreen ? 2.0 : 4.0),
              Text(
                title,
                style: TextStyle(
                  fontSize: isVerySmallScreen
                      ? 9.0
                      : isSmallScreen
                      ? 10.0
                      : 12.0,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        );
      },
    );
  }

  void _showDateRangePicker(BuildContext context, WidgetRef ref) async {
    final state = ref.read(reportsSimpleProvider);
    final l10n = context.l10n;

    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: state.startDate,
        end: state.endDate,
      ),
      helpText: l10n.selectDateRange,
      cancelText: l10n.cancel,
      confirmText: l10n.confirm,
      saveText: l10n.save,
      errorFormatText: l10n.invalidDateFormat,
      errorInvalidText: l10n.invalidDateRange,
      errorInvalidRangeText: l10n.invalidDateRangeOrder,
    );

    if (picked != null) {
      ref
          .read(reportsSimpleProvider.notifier)
          .setDateRange(picked.start, picked.end);
    }
  }
}
