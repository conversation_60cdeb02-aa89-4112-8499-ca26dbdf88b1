class InventoryReport {
  final String id;
  final int totalProducts;
  final double totalValue;
  final int lowStockProducts;
  final int outOfStockProducts;
  final Map<String, int> categories;
  final List<Map<String, dynamic>> topValueProducts;
  final List<Map<String, dynamic>> lowStockItems;
  final List<Map<String, dynamic>> recentMovements;
  final DateTime createdAt;

  const InventoryReport({
    required this.id,
    required this.totalProducts,
    required this.totalValue,
    required this.lowStockProducts,
    required this.outOfStockProducts,
    required this.categories,
    required this.topValueProducts,
    required this.lowStockItems,
    required this.recentMovements,
    required this.createdAt,
  });

  factory InventoryReport.fromJson(Map<String, dynamic> json) {
    return InventoryReport(
      id: json['id'] as String,
      totalProducts: json['total_products'] as int,
      totalValue: (json['total_value'] as num).toDouble(),
      lowStockProducts: json['low_stock_products'] as int,
      outOfStockProducts: json['out_of_stock_products'] as int,
      categories: Map<String, int>.from(json['categories'] as Map),
      topValueProducts: List<Map<String, dynamic>>.from(json['top_value_products'] as List),
      lowStockItems: List<Map<String, dynamic>>.from(json['low_stock_items'] as List),
      recentMovements: List<Map<String, dynamic>>.from(json['recent_movements'] as List),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'total_products': totalProducts,
      'total_value': totalValue,
      'low_stock_products': lowStockProducts,
      'out_of_stock_products': outOfStockProducts,
      'categories': categories,
      'top_value_products': topValueProducts,
      'low_stock_items': lowStockItems,
      'recent_movements': recentMovements,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Calculated properties
  double get averageProductValue => 
      totalProducts > 0 ? totalValue / totalProducts : 0.0;

  double get stockHealthPercentage {
    final healthyProducts = totalProducts - lowStockProducts - outOfStockProducts;
    return totalProducts > 0 ? (healthyProducts / totalProducts) * 100 : 0.0;
  }

  double get lowStockPercentage => 
      totalProducts > 0 ? (lowStockProducts / totalProducts) * 100 : 0.0;

  double get outOfStockPercentage => 
      totalProducts > 0 ? (outOfStockProducts / totalProducts) * 100 : 0.0;

  String get largestCategory {
    if (categories.isEmpty) return '';
    
    return categories.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  int get totalCategoryProducts => categories.values.fold(0, (sum, count) => sum + count);

  List<Map<String, dynamic>> get criticalStockItems {
    return lowStockItems.where((item) => item['status'] == 'out').toList();
  }

  List<Map<String, dynamic>> get recentInboundMovements {
    return recentMovements.where((movement) => movement['type'] == 'in').toList();
  }

  List<Map<String, dynamic>> get recentOutboundMovements {
    return recentMovements.where((movement) => movement['type'] == 'out').toList();
  }

  InventoryReport copyWith({
    String? id,
    int? totalProducts,
    double? totalValue,
    int? lowStockProducts,
    int? outOfStockProducts,
    Map<String, int>? categories,
    List<Map<String, dynamic>>? topValueProducts,
    List<Map<String, dynamic>>? lowStockItems,
    List<Map<String, dynamic>>? recentMovements,
    DateTime? createdAt,
  }) {
    return InventoryReport(
      id: id ?? this.id,
      totalProducts: totalProducts ?? this.totalProducts,
      totalValue: totalValue ?? this.totalValue,
      lowStockProducts: lowStockProducts ?? this.lowStockProducts,
      outOfStockProducts: outOfStockProducts ?? this.outOfStockProducts,
      categories: categories ?? this.categories,
      topValueProducts: topValueProducts ?? this.topValueProducts,
      lowStockItems: lowStockItems ?? this.lowStockItems,
      recentMovements: recentMovements ?? this.recentMovements,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'InventoryReport(id: $id, totalProducts: $totalProducts, totalValue: $totalValue)';
  }
}
