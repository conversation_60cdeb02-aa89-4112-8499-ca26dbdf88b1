import 'package:flutter/material.dart';
import 'package:city_pos/generated/l10n/app_localizations.dart';

enum ReportType {
  sales,
  inventory,
  finance,
  profit,
  cashFlow,
}

enum ReportPeriod {
  daily,
  weekly,
  monthly,
  quarterly,
  yearly,
  custom,
}

extension ReportTypeExtension on ReportType {
  String getDisplayName(BuildContext context) {
    switch (this) {
      case ReportType.sales:
        return AppLocalizations.of(context).salesReport;
      case ReportType.inventory:
        return AppLocalizations.of(context).inventoryReport;
      case ReportType.finance:
        return AppLocalizations.of(context).financeReport;
      case ReportType.profit:
        return AppLocalizations.of(context).profitReport;
      case ReportType.cashFlow:
        return AppLocalizations.of(context).cashFlowReport;
    }
  }
  
  String get value {
    switch (this) {
      case ReportType.sales:
        return 'sales';
      case ReportType.inventory:
        return 'inventory';
      case ReportType.finance:
        return 'finance';
      case ReportType.profit:
        return 'profit';
      case ReportType.cashFlow:
        return 'cash_flow';
    }
  }
}

extension ReportPeriodExtension on ReportPeriod {
  String getDisplayName(BuildContext context) {
    switch (this) {
      case ReportPeriod.daily:
        return AppLocalizations.of(context).daily;
      case ReportPeriod.weekly:
        return AppLocalizations.of(context).weekly;
      case ReportPeriod.monthly:
        return AppLocalizations.of(context).monthly;
      case ReportPeriod.quarterly:
        return AppLocalizations.of(context).quarterly;
      case ReportPeriod.yearly:
        return AppLocalizations.of(context).yearly;
      case ReportPeriod.custom:
        return AppLocalizations.of(context).custom;
    }
  }
}

// Sales Report Data
class SalesReportData {
  final DateTime date;
  final double totalRevenue;
  final double totalCost;
  final double grossProfit;
  final int totalOrders;
  final int totalItems;
  final double averageOrderValue;
  final Map<String, double> revenueByCategory;
  final Map<String, int> ordersByPaymentMethod;
  final List<TopSellingProduct> topProducts;

  const SalesReportData({
    required this.date,
    required this.totalRevenue,
    required this.totalCost,
    required this.grossProfit,
    required this.totalOrders,
    required this.totalItems,
    required this.averageOrderValue,
    required this.revenueByCategory,
    required this.ordersByPaymentMethod,
    required this.topProducts,
  });

  factory SalesReportData.fromJson(Map<String, dynamic> json) {
    return SalesReportData(
      date: DateTime.parse(json['date'] as String),
      totalRevenue: (json['total_revenue'] as num).toDouble(),
      totalCost: (json['total_cost'] as num).toDouble(),
      grossProfit: (json['gross_profit'] as num).toDouble(),
      totalOrders: json['total_orders'] as int,
      totalItems: json['total_items'] as int,
      averageOrderValue: (json['average_order_value'] as num).toDouble(),
      revenueByCategory: Map<String, double>.from(json['revenue_by_category'] as Map),
      ordersByPaymentMethod: Map<String, int>.from(json['orders_by_payment_method'] as Map),
      topProducts: (json['top_products'] as List<dynamic>)
          .map((item) => TopSellingProduct.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'total_revenue': totalRevenue,
      'total_cost': totalCost,
      'gross_profit': grossProfit,
      'total_orders': totalOrders,
      'total_items': totalItems,
      'average_order_value': averageOrderValue,
      'revenue_by_category': revenueByCategory,
      'orders_by_payment_method': ordersByPaymentMethod,
      'top_products': topProducts.map((product) => product.toJson()).toList(),
    };
  }

  double get profitMargin => totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0.0;
}

// Inventory Report Data
class InventoryReportData {
  final DateTime date;
  final int totalProducts;
  final double totalStockValue;
  final int lowStockProducts;
  final int outOfStockProducts;
  final List<ProductStockInfo> productStocks;
  final Map<String, double> stockValueByCategory;

  const InventoryReportData({
    required this.date,
    required this.totalProducts,
    required this.totalStockValue,
    required this.lowStockProducts,
    required this.outOfStockProducts,
    required this.productStocks,
    required this.stockValueByCategory,
  });

  factory InventoryReportData.fromJson(Map<String, dynamic> json) {
    return InventoryReportData(
      date: DateTime.parse(json['date'] as String),
      totalProducts: json['total_products'] as int,
      totalStockValue: (json['total_stock_value'] as num).toDouble(),
      lowStockProducts: json['low_stock_products'] as int,
      outOfStockProducts: json['out_of_stock_products'] as int,
      productStocks: (json['product_stocks'] as List<dynamic>)
          .map((item) => ProductStockInfo.fromJson(item as Map<String, dynamic>))
          .toList(),
      stockValueByCategory: Map<String, double>.from(json['stock_value_by_category'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'total_products': totalProducts,
      'total_stock_value': totalStockValue,
      'low_stock_products': lowStockProducts,
      'out_of_stock_products': outOfStockProducts,
      'product_stocks': productStocks.map((stock) => stock.toJson()).toList(),
      'stock_value_by_category': stockValueByCategory,
    };
  }
}

// Finance Report Data
class FinanceReportData {
  final DateTime date;
  final double totalReceipts;
  final double totalPayments;
  final double netCashFlow;
  final double openingBalance;
  final double closingBalance;
  final Map<String, double> receiptsByCategory;
  final Map<String, double> paymentsByCategory;

  const FinanceReportData({
    required this.date,
    required this.totalReceipts,
    required this.totalPayments,
    required this.netCashFlow,
    required this.openingBalance,
    required this.closingBalance,
    required this.receiptsByCategory,
    required this.paymentsByCategory,
  });

  factory FinanceReportData.fromJson(Map<String, dynamic> json) {
    return FinanceReportData(
      date: DateTime.parse(json['date'] as String),
      totalReceipts: (json['total_receipts'] as num).toDouble(),
      totalPayments: (json['total_payments'] as num).toDouble(),
      netCashFlow: (json['net_cash_flow'] as num).toDouble(),
      openingBalance: (json['opening_balance'] as num).toDouble(),
      closingBalance: (json['closing_balance'] as num).toDouble(),
      receiptsByCategory: Map<String, double>.from(json['receipts_by_category'] as Map),
      paymentsByCategory: Map<String, double>.from(json['payments_by_category'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'total_receipts': totalReceipts,
      'total_payments': totalPayments,
      'net_cash_flow': netCashFlow,
      'opening_balance': openingBalance,
      'closing_balance': closingBalance,
      'receipts_by_category': receiptsByCategory,
      'payments_by_category': paymentsByCategory,
    };
  }
}

// Supporting Classes
class TopSellingProduct {
  final String productId;
  final String productName;
  final int quantitySold;
  final double revenue;

  const TopSellingProduct({
    required this.productId,
    required this.productName,
    required this.quantitySold,
    required this.revenue,
  });

  factory TopSellingProduct.fromJson(Map<String, dynamic> json) {
    return TopSellingProduct(
      productId: json['product_id'] as String,
      productName: json['product_name'] as String,
      quantitySold: json['quantity_sold'] as int,
      revenue: (json['revenue'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'product_id': productId,
      'product_name': productName,
      'quantity_sold': quantitySold,
      'revenue': revenue,
    };
  }
}

class ProductStockInfo {
  final String productId;
  final String productName;
  final String category;
  final int currentStock;
  final int minStock;
  final double unitCost;
  final double stockValue;

  const ProductStockInfo({
    required this.productId,
    required this.productName,
    required this.category,
    required this.currentStock,
    required this.minStock,
    required this.unitCost,
    required this.stockValue,
  });

  factory ProductStockInfo.fromJson(Map<String, dynamic> json) {
    return ProductStockInfo(
      productId: json['product_id'] as String,
      productName: json['product_name'] as String,
      category: json['category'] as String,
      currentStock: json['current_stock'] as int,
      minStock: json['min_stock'] as int,
      unitCost: (json['unit_cost'] as num).toDouble(),
      stockValue: (json['stock_value'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'product_id': productId,
      'product_name': productName,
      'category': category,
      'current_stock': currentStock,
      'min_stock': minStock,
      'unit_cost': unitCost,
      'stock_value': stockValue,
    };
  }

  bool get isLowStock => currentStock <= minStock;
  bool get isOutOfStock => currentStock <= 0;
}
