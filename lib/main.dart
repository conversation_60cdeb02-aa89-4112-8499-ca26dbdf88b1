import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/config/supabase_config.dart';
import 'core/constants/app_constants.dart';
import 'core/providers/language_provider.dart';
import 'core/routers/app_router.dart';
// Import for printer service
import 'core/services/printer_service.dart';
import 'core/services/push_notification_service.dart';
import 'core/services/storage_service.dart';
import 'core/themes/app_theme.dart';
import 'data/models/printer.dart';
import 'generated/l10n/app_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive for local storage
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(PrinterAdapter());
  Hive.registerAdapter(PrinterTypeAdapter());

  // Initialize Storage Service
  await StorageService.init();

  // Initialize Printer Service
  await PrinterService.init();

  // Initialize Supabase
  await SupabaseConfig.initialize();

  // Initialize Push Notification Service
  await PushNotificationService.initialize();

  runApp(const ProviderScope(child: CityPosApp()));
}

class CityPosApp extends ConsumerWidget {
  const CityPosApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);
    final locale = ref.watch(languageProvider);

    return MaterialApp.router(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      routerConfig: router,
      debugShowCheckedModeBanner: false,

      // Localization
      locale: locale,
      localizationsDelegates: [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('vi', 'VN'),
        Locale('en', 'US'),
        Locale('ja', 'JP'),
      ],
    );
  }
}
